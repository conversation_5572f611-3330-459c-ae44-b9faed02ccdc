import React from 'react';
import styles from './BackOffice.module.css';

const employeeShifts = [
  { name: '<PERSON>', role: 'General Manager', clockIn: '08:00 AM', clockOut: '04:00 PM', hours: '8 hrs', status: 'Approved' },
  { name: '<PERSON>', role: 'Superintendent', clockIn: '07:30 AM', clockOut: '03:30 PM', hours: '8 hrs', status: 'Pending' },
  { name: '<PERSON>', role: 'Beverage Specialist', clockIn: '10:00 AM', clockOut: '06:00 PM', hours: '8 hrs', status: 'Pending' },
  { name: '<PERSON><PERSON>', role: 'Grounds Keeper', clockIn: '06:00 AM', clockOut: '02:00 PM', hours: '8 hrs', status: 'Approved' },
];

const marketingTemplates = [
  { name: 'Welcome Email', type: 'Email', lastModified: '2024-01-15', status: 'Active' },
  { name: 'Membership Renewal', type: 'SMS', lastModified: '2024-01-14', status: 'Draft' },
  { name: 'Tournament Invitation', type: 'Email', lastModified: '2024-01-13', status: 'Active' },
  { name: 'Special Offer', type: 'SMS', lastModified: '2024-01-12', status: 'Inactive' },
];

const SummaryTab: React.FC = () => (
  <div className={styles.summaryTab}>
    <h2 className={styles.heading}>Employee Shifts</h2>
    <div className={styles.shiftsTableWrapper}>
      <table className={styles.shiftsTable}>
        <thead>
          <tr>
            <th>Employee</th>
            <th>Clock In</th>
            <th>Clock Out</th>
            <th>Hours</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {employeeShifts.map((shift, idx) => (
            <tr key={idx}>
              <td>
                <div className={styles.employeeInfo}>
                  <div className={styles.avatar}>{shift.name[0]}</div>
                  <div>
                    <div className={styles.employeeName}>{shift.name}</div>
                    <div className={styles.employeeRole}>{shift.role}</div>
                  </div>
                </div>
              </td>
              <td>{shift.clockIn}</td>
              <td>{shift.clockOut}</td>
              <td>{shift.hours}</td>
              <td>
                <span className={
                  shift.status === 'Approved' ? styles.statusApproved : styles.statusPending
                }>
                  {shift.status}
                </span>
              </td>
              <td>
                <span className={styles.actionIcon}>✔️</span>
                <span className={styles.actionIcon}>❌</span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
    <h2 className={styles.heading}>Marketing Templates</h2>
    <div className={styles.templatesGrid}>
      {marketingTemplates.map((template, idx) => (
        <div key={idx} className={styles.templateCard}>
          <div className={styles.templateHeader}>
            <span className={styles.templateType}>{template.type}</span>
            <span className={
              template.status === 'Active' ? styles.templateStatusActive :
              template.status === 'Draft' ? styles.templateStatusDraft :
              styles.templateStatusInactive
            }>
              {template.status}
            </span>
          </div>
          <div className={styles.templateName}>{template.name}</div>
          <div className={styles.templateMeta}>Last modified: {template.lastModified}</div>
        </div>
      ))}
    </div>
  </div>
);

export default SummaryTab; 