# Albatros Golf Management System - Data Models

This document provides a comprehensive overview of all data models used in the Albatros Golf Management System. Models are organized by module and include TypeScript classes with decorators for both MongoDB schemas (via Typegoose) and GraphQL types (via TypeGraphQL).

## Table of Contents
- [Core Models](#core-models)
- [CRM Modules](#crm-modules)
  - [Dashboard](#dashboard)
  - [Calendar/Tee Sheet](#calendartee-sheet)
  - [Roster](#roster)
  - [Pro Shop](#pro-shop)
  - [19th Hole (F&B)](#19th-hole-fb)
  - [Events & Tournaments](#events--tournaments)
  - [Analytics](#analytics)
  - [Back Office](#back-office)
  - [Settings](#settings)
- [Mobile Application Models](#mobile-application-models)
  - [Home Screen](#home-screen)
  - [Book a Tee Time](#book-a-tee-time)
  - [In-Play Mode](#in-play-mode)
  - [Marketplace](#marketplace)
  - [Golfer Profile](#golfer-profile)
  - [Rewards](#rewards)
- [Coach Platform Models](#coach-platform-models)
  - [Calendar](#calendar)
  - [Messenger](#messenger)
  - [Payment](#payment)
  - [Academies & Clinics](#academies--clinics)

## Core Models

### User
```typescript
import { Field, ObjectType, ID, registerEnumType } from 'type-graphql';
import { prop as Property, getModelForClass, Ref } from '@typegoose/typegoose';
import { Course } from './Course';

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  STAFF = 'staff'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

registerEnumType(UserRole, {
  name: 'UserRole',
  description: 'The role of the user in the system'
});

registerEnumType(UserStatus, {
  name: 'UserStatus',
  description: 'The current status of the user'
});

@ObjectType()
export class User {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field()
  @Property({ required: true, unique: true })
  email: string;

  @Field()
  @Property({ required: true })
  firstName: string;

  @Field()
  @Property({ required: true })
  lastName: string;

  @Field(() => UserRole)
  @Property({ 
    required: true,
    type: String,
    enum: UserRole,
    default: UserRole.STAFF
  })
  role: UserRole;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  permissions: string[];

  @Field(() => [ID])
  @Property({ ref: 'Course', default: [] })
  courses: Ref<Course>[];

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', required: false })
  corporateId?: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', required: false })
  regionId?: string;

  @Field(() => Date, { nullable: true })
  @Property()
  lastLogin?: Date;

  @Field(() => UserStatus)
  @Property({ 
    type: String, 
    enum: UserStatus, 
    default: UserStatus.ACTIVE 
  })
  status: UserStatus;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;

  @Property({ required: true, select: false })
  password: string;
}

export const UserModel = getModelForClass(User);
```

### Course
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Coordinates {
  @Field()
  @Property({ required: true })
  latitude: number;

  @Field()
  @Property({ required: true })
  longitude: number;
}

@ObjectType()
export class Location {
  @Field()
  @Property({ required: true })
  address: string;

  @Field()
  @Property({ required: true })
  city: string;

  @Field()
  @Property({ required: true })
  state: string;

  @Field()
  @Property({ required: true })
  country: string;

  @Field()
  @Property({ required: true })
  timezone: string;

  @Field(() => Coordinates)
  @Property({ type: Coordinates, _id: false })
  coordinates: Coordinates;
}

@ObjectType()
export class Contact {
  @Field()
  @Property()
  phone: string;

  @Field()
  @Property()
  email: string;

  @Field({ nullable: true })
  @Property()
  website?: string;
}

@ObjectType()
export class DailyHours {
  @Field()
  @Property()
  open: string;

  @Field()
  @Property()
  close: string;
}

@ObjectType()
export class OperatingHours {
  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  monday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  tuesday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  wednesday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  thursday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  friday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  saturday: DailyHours;

  @Field(() => DailyHours)
  @Property({ type: DailyHours, _id: false })
  sunday: DailyHours;
}

@ObjectType()
export class CourseSettings {
  @Field()
  @Property({ required: true, default: 10 })
  teeTimeInterval: number;

  @Field()
  @Property({ required: true, default: 4 })
  maxPlayersPerSlot: number;

  @Field()
  @Property({ required: true })
  cancellationPolicy: string;

  @Field()
  @Property({ required: true })
  weatherPolicy: string;

  @Field()
  @Property({ required: true, default: false })
  isPrivate: boolean;

  @Field()
  @Property({ required: true, default: false })
  requiresMembership: boolean;
}

@ObjectType()
export class Course {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field()
  @Property({ required: true })
  name: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', required: false })
  corporateId?: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', required: false })
  regionId?: string;

  @Field(() => Location)
  @Property({ type: Location, required: true, _id: false })
  location: Location;

  @Field(() => Contact)
  @Property({ type: Contact, required: true, _id: false })
  contact: Contact;

  @Field(() => OperatingHours)
  @Property({ type: OperatingHours, required: true, _id: false })
  operatingHours: OperatingHours;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  features: string[];

  @Field(() => CourseSettings)
  @Property({ type: CourseSettings, required: true, _id: false })
  settings: CourseSettings;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const CourseModel = getModelForClass(Course);
```

### Golfer
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass, Ref } from '@typegoose/typegoose';
import { IsEmail, IsOptional, IsPhoneNumber } from 'class-validator';

@ObjectType()
export class Address {
  @Field()
  @Property()
  street: string;

  @Field()
  @Property()
  city: string;

  @Field()
  @Property()
  state: string;

  @Field()
  @Property()
  zipCode: string;
}

@ObjectType()
export class Golfer {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field()
  @Property({ required: true })
  firstName: string;

  @Field()
  @Property({ required: true })
  lastName: string;

  @Field()
  @IsPhoneNumber(null, { message: 'Invalid phone number' })
  @Property({ required: true })
  phoneNumber: string;

  @Field()
  @IsEmail({}, { message: 'Invalid email' })
  @Property({ required: true, unique: true })
  email: string;

  @Field(() => Address, { nullable: true })
  @Property({ type: Address, _id: false })
  address?: Address;

  @Field(() => Date, { nullable: true })
  @Property()
  lastPlayDate?: Date;

  @Field(() => Date, { nullable: true })
  @Property()
  upcomingPlayDate?: Date;

  @Field()
  @Property({ default: false })
  membershipStatus: boolean;

  @Field({ nullable: true })
  @Property()
  membershipType?: string;

  @Field({ nullable: true })
  @Property()
  membershipId?: string;

  @Field({ nullable: true })
  @Property()
  satisfactionScore?: number;

  @Field({ nullable: true })
  @Property({ min: 1, max: 3 })
  starRating?: number;

  @Field()
  @Property({ default: false })
  hasCardOnFile: boolean;

  @Field()
  @Property({ default: false })
  isStudent: boolean;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Coach' })
  professorId?: Ref<any>;

  @Field({ nullable: true })
  @Property({ default: 0 })
  totalRounds?: number;

  @Field({ nullable: true })
  @Property()
  averageScore?: number;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const GolferModel = getModelForClass(Golfer);
```

## CRM Modules

### Dashboard

#### DashboardMetrics
```typescript
import { Field, ObjectType } from 'type-graphql';
import { prop as Property } from '@typegoose/typegoose';

@ObjectType()
export class DashboardMetrics {
  @Field()
  @Property({ required: true })
  value: number;

  @Field()
  @Property({ required: true })
  change: number;

  @Field(() => [Number])
  @Property({ type: [Number], default: [] })
  trend: number[];
}
```

#### DashboardOverview
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass, Ref } from '@typegoose/typegoose';
import { DashboardMetrics } from './DashboardMetrics';

@ObjectType()
export class UpcomingTeeTimesSummary {
  @Field()
  @Property({ required: true })
  total: number;

  @Field()
  @Property({ required: true })
  upcoming: number;
}

@ObjectType()
export class InventoryAlert {
  @Field(() => ID)
  @Property({ ref: 'Product', required: true })
  productId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  currentStock: number;

  @Field()
  @Property({ required: true })
  reorderLevel: number;
}

@ObjectType()
export class WeatherForecast {
  @Field(() => Date)
  @Property({ required: true })
  date: Date;

  @Field()
  @Property({ required: true })
  temperature: number;

  @Field()
  @Property({ required: true })
  condition: string;

  @Field()
  @Property({ required: true })
  icon: string;
}

@ObjectType()
export class DashboardOverview {
  @Field(() => DashboardMetrics)
  @Property({ type: DashboardMetrics, required: true, _id: false })
  revenue: DashboardMetrics;

  @Field(() => DashboardMetrics)
  @Property({ type: DashboardMetrics, required: true, _id: false })
  teeTimeCount: DashboardMetrics;

  @Field(() => DashboardMetrics)
  @Property({ type: DashboardMetrics, required: true, _id: false })
  golferCount: DashboardMetrics;

  @Field(() => UpcomingTeeTimesSummary)
  @Property({ type: UpcomingTeeTimesSummary, required: true, _id: false })
  upcomingTeeTimesSummary: UpcomingTeeTimesSummary;

  @Field(() => [InventoryAlert])
  @Property({ type: [InventoryAlert], default: [], _id: false })
  inventoryAlerts: InventoryAlert[];

  @Field(() => WeatherForecast)
  @Property({ type: WeatherForecast, required: true, _id: false })
  weatherForecast: WeatherForecast;

  @Field(() => [ID])
  @Property({ ref: 'Event', default: [] })
  upcomingEvents: Ref<any>[];
}

export const DashboardOverviewModel = getModelForClass(DashboardOverview);
```

### Calendar/Tee Sheet

#### TeeTime
```typescript
import { Field, ObjectType, ID, registerEnumType } from 'type-graphql';
import { prop as Property, getModelForClass, Ref } from '@typegoose/typegoose';
import { WeatherForecast } from './Dashboard';
import { Golfer } from '../Core/Golfer';

export enum TeeTimeStatus {
  BOOKED = 'booked',
  AVAILABLE = 'available',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum TeeTimeStyle {
  STANDARD = 'standard',
  SHOTGUN = 'shotgun',
  CROSSOVER = 'crossover'
}

registerEnumType(TeeTimeStatus, {
  name: 'TeeTimeStatus',
  description: 'Status of a tee time'
});

registerEnumType(TeeTimeStyle, {
  name: 'TeeTimeStyle',
  description: 'Style of a tee time'
});

@ObjectType()
export class TeeTime {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => Date)
  @Property({ required: true })
  time: Date;

  @Field()
  @Property({ required: true, min: 0 })
  duration: number;

  @Field()
  @Property({ required: true, min: 0 })
  price: number;

  @Field()
  @Property({ required: true, default: false })
  dynamicPricing: boolean;

  @Field()
  @Property({ required: true, min: 0 })
  memberPrice: number;

  @Field()
  @Property({ required: true, min: 1 })
  maxPlayers: number;

  @Field(() => [ID])
  @Property({ ref: 'Golfer', default: [] })
  players: Ref<Golfer>[];

  @Field(() => TeeTimeStatus)
  @Property({ 
    type: String, 
    enum: TeeTimeStatus, 
    default: TeeTimeStatus.AVAILABLE 
  })
  status: TeeTimeStatus;

  @Field(() => WeatherForecast, { nullable: true })
  @Property({ type: WeatherForecast, _id: false })
  weatherForecast?: WeatherForecast;

  @Field({ nullable: true })
  @Property({ min: 0 })
  revenue?: number;

  @Field(() => [String], { nullable: true })
  @Property({ type: [String], default: [] })
  specialRequests?: string[];

  @Field(() => TeeTimeStyle)
  @Property({ 
    type: String, 
    enum: TeeTimeStyle, 
    default: TeeTimeStyle.STANDARD 
  })
  teeTimeStyle: TeeTimeStyle;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const TeeTimeModel = getModelForClass(TeeTime);
```

#### BookingWindow
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class BookingWindow {
  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true, min: 1, max: 10 })
  outlookWeeks: number;

  @Field(() => [Date])
  @Property({ type: [Date], default: [] })
  holidays: Date[];

  @Field(() => String)
  @Property({ required: true })
  teeTimeStyle: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const BookingWindowModel = getModelForClass(BookingWindow);
```

### Roster

#### Golfer
```typescript
// See Core Models - Golfer
```

### Pro Shop

#### Product
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Product {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  sku: string;

  @Field()
  @Property({ required: true })
  image: string;

  @Field()
  @Property({ required: true })
  category: string;

  @Field()
  @Property({ required: true })
  subcategory: string;

  @Field()
  @Property({ required: true })
  brand: string;

  @Field()
  @Property({ required: true, min: 0 })
  cost: number;

  @Field()
  @Property({ required: true, min: 0 })
  price: number;

  @Field()
  @Property({ required: true, min: 0 })
  membershipDiscount: number;

  @Field()
  @Property({ required: true, min: 0 })
  quantity: number;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  sizes: string[];

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  colors: string[];

  @Field(() => String)
  @Property({ required: true })
  hand: string;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  loft: number;

  @Field(() => String)
  @Property({ required: true })
  flex: string;

  @Field(() => String)
  @Property({ required: true })
  shaft: string;

  @Field()
  @Property({ required: true, default: false })
  availableInMobileApp: boolean;

  @Field()
  @Property({ required: true, default: false })
  availableInBookingFlow: boolean;

  @Field(() => Object)
  @Property({ required: true })
  specifications: object;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const ProductModel = getModelForClass(Product);
```

#### Category
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Category {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Category', required: false })
  parentId?: Ref<any>;

  @Field()
  @Property({ required: true })
  image: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const CategoryModel = getModelForClass(Category);
```

#### Sale
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Sale {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => [SaleItem])
  @Property({ type: [SaleItem], default: [] })
  items: SaleItem[];

  @Field()
  @Property({ required: true, min: 0 })
  subtotal: number;

  @Field()
  @Property({ required: true, min: 0 })
  tax: number;

  @Field()
  @Property({ required: true, min: 0 })
  total: number;

  @Field(() => String)
  @Property({ required: true })
  paymentMethod: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Golfer', required: false })
  golferId?: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  staffId: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;
}

export const SaleModel = getModelForClass(Sale);
```

### 19th Hole (F&B)

#### MenuItem
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class MenuItem {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  image: string;

  @Field()
  @Property({ required: true })
  category: string;

  @Field()
  @Property({ required: true, min: 0 })
  price: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  membershipDiscount: number;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  ingredients: string[];

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  allergens: string[];

  @Field(() => NutritionalInfo)
  @Property({ type: NutritionalInfo, required: true, _id: false })
  nutritionalInfo: NutritionalInfo;

  @Field(() => [Customization])
  @Property({ type: [Customization], default: [] })
  customizations: Customization[];

  @Field()
  @Property({ required: true, default: false })
  availableInMobileApp: boolean;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  preparationTime: number;

  @Field()
  @Property({ required: true, default: true })
  available: boolean;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const MenuItemModel = getModelForClass(MenuItem);
```

#### FoodOrder
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class FoodOrder {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => [FoodOrderItem])
  @Property({ type: [FoodOrderItem], default: [] })
  items: FoodOrderItem[];

  @Field()
  @Property({ required: true, min: 0 })
  subtotal: number;

  @Field()
  @Property({ required: true, min: 0 })
  tax: number;

  @Field()
  @Property({ required: true, min: 0 })
  total: number;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Golfer', required: false })
  golferId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'TeeTime', required: false })
  teeTimeId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Round', required: false })
  roundId?: Ref<any>;

  @Field(() => String)
  @Property({ required: true })
  deliveryLocation: string;

  @Field(() => String)
  @Property({ required: true })
  paymentMethod: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const FoodOrderModel = getModelForClass(FoodOrder);
```

### Events & Tournaments

#### Event
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Event {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field(() => Date)
  @Property({ required: true })
  startDate: Date;

  @Field(() => Date)
  @Property({ required: true })
  endDate: Date;

  @Field(() => Date)
  @Property({ required: true })
  registrationDeadline: Date;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  capacity: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  registeredCount: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  price: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  memberPrice: number;

  @Field(() => [ID])
  @Property({ ref: 'Golfer', default: [] })
  registrants: Ref<any>[];

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  promotionChannels: string[];

  @Field()
  @Property({ required: true })
  micrositeUrl: string;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const EventModel = getModelForClass(Event);
```

### Analytics

#### AnalyticsQuery
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class AnalyticsQuery {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => TimeRange)
  @Property({ type: TimeRange, required: true, _id: false })
  timeRange: TimeRange;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  dimensions: string[];

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  metrics: string[];

  @Field(() => [Filter])
  @Property({ type: [Filter], default: [] })
  filters: Filter[];

  @Field(() => [Sort])
  @Property({ type: [Sort], default: [] })
  sort: Sort[];

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  limit: number;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const AnalyticsQueryModel = getModelForClass(AnalyticsQuery);
```

#### ScheduledReport
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class ScheduledReport {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => ID)
  @Property({ ref: 'AnalyticsQuery', required: true })
  queryId: Ref<any>;

  @Field(() => Schedule)
  @Property({ type: Schedule, required: true, _id: false })
  schedule: Schedule;

  @Field(() => String)
  @Property({ required: true })
  format: string;

  @Field(() => [Recipient])
  @Property({ type: [Recipient], default: [], _id: false })
  recipients: Recipient[];

  @Field(() => Date)
  @Property({ required: true })
  lastRun: Date;

  @Field(() => Date)
  @Property({ required: true })
  nextRun: Date;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const ScheduledReportModel = getModelForClass(ScheduledReport);
```

### Back Office

#### Employee
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Employee {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  firstName: string;

  @Field()
  @Property({ required: true })
  lastName: string;

  @Field()
  @Property({ required: true })
  email: string;

  @Field()
  @Property({ required: true })
  phone: string;

  @Field(() => String)
  @Property({ required: true })
  role: string;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  permissions: string[];

  @Field(() => [ID])
  @Property({ ref: 'Shift', default: [] })
  schedule: Ref<any>[];

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const EmployeeModel = getModelForClass(Employee);
```

#### Shift
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Shift {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'Employee', required: true })
  employeeId: Ref<any>;

  @Field(() => Date)
  @Property({ required: true })
  date: Date;

  @Field(() => String)
  @Property({ required: true })
  startTime: string;

  @Field(() => String)
  @Property({ required: true })
  endTime: string;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => Date)
  @Property({ required: true })
  actualClockIn: Date;

  @Field(() => Date)
  @Property({ required: true })
  actualClockOut: Date;

  @Field(() => String)
  @Property({ required: true })
  department: string;

  @Field(() => String)
  @Property({ required: true })
  notes: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const ShiftModel = getModelForClass(Shift);
```

#### MarketingTemplate
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class MarketingTemplate {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => String)
  @Property({ required: true })
  category: string;

  @Field(() => String)
  @Property({ required: true })
  content: string;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  variables: string[];

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const MarketingTemplateModel = getModelForClass(MarketingTemplate);
```

#### MarketingCampaign
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class MarketingCampaign {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => ID)
  @Property({ ref: 'MarketingTemplate', required: true })
  templateId: Ref<any>;

  @Field(() => Audience)
  @Property({ type: Audience, required: true, _id: false })
  audience: Audience;

  @Field(() => Schedule)
  @Property({ type: Schedule, required: true, _id: false })
  schedule: Schedule;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => Metrics)
  @Property({ type: Metrics, required: true, _id: false })
  metrics: Metrics;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const MarketingCampaignModel = getModelForClass(MarketingCampaign);
```

#### Promotion
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Promotion {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => String)
  @Property({ required: true })
  type: string;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  value: number;

  @Field(() => Boolean)
  @Property({ required: true })
  isPercentage: boolean;

  @Field(() => Date)
  @Property({ required: true })
  startDate: Date;

  @Field(() => Date)
  @Property({ required: true })
  endDate: Date;

  @Field(() => String)
  @Property({ required: true })
  applicableTo: string;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  minimumPurchase: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  maximumDiscount: number;

  @Field(() => String)
  @Property({ required: true })
  code: string;

  @Field(() => Boolean)
  @Property({ required: true })
  canStackWithRewards: boolean;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  targetGroups: string[];

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  usageLimit: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  usageCount: number;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const PromotionModel = getModelForClass(Promotion);
```

#### AccountingRecord
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class AccountingRecord {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => String)
  @Property({ required: true })
  type: string;

  @Field(() => String)
  @Property({ required: true })
  category: string;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  amount: number;

  @Field(() => String)
  @Property({ required: true })
  description: string;

  @Field(() => Date)
  @Property({ required: true })
  date: Date;

  @Field(() => String)
  @Property({ required: true })
  referenceId: string;

  @Field(() => String)
  @Property({ required: true })
  notes: string;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const AccountingRecordModel = getModelForClass(AccountingRecord);
```

### Settings

#### CourseSettings
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class CourseSettings {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => General)
  @Property({ type: General, required: true, _id: false })
  general: General;

  @Field(() => TeeSheet)
  @Property({ type: TeeSheet, required: true, _id: false })
  teeSheet: TeeSheet;

  @Field(() => Payments)
  @Property({ type: Payments, required: true, _id: false })
  payments: Payments;

  @Field(() => Notifications)
  @Property({ type: Notifications, required: true, _id: false })
  notifications: Notifications;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  updatedBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const CourseSettingsModel = getModelForClass(CourseSettings);
```

#### UserRole
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class UserRole {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field()
  @Property({ required: true })
  name: string;

  @Field()
  @Property({ required: true })
  description: string;

  @Field(() => [String])
  @Property({ type: [String], default: [] })
  permissions: string[];

  @Field(() => Modules)
  @Property({ type: Modules, required: true, _id: false })
  modules: Modules;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const UserRoleModel = getModelForClass(UserRole);
```

#### AuditLog
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class AuditLog {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  userId: Ref<any>;

  @Field(() => String)
  @Property({ required: true })
  action: string;

  @Field(() => String)
  @Property({ required: true })
  resourceType: string;

  @Field(() => ID)
  @Property({ ref: 'TeeTime', required: true })
  resourceId: Ref<any>;

  @Field(() => Object)
  @Property({ required: true })
  changes: object;

  @Field(() => String)
  @Property({ required: true })
  ipAddress: string;

  @Field(() => String)
  @Property({ required: true })
  userAgent: string;

  @Field(() => Date)
  @Property({ required: true })
  timestamp: Date;
}

export const AuditLogModel = getModelForClass(AuditLog);
```

#### Integration
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Integration {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => String)
  @Property({ required: true })
  type: string;

  @Field(() => String)
  @Property({ required: true })
  provider: string;

  @Field(() => Object)
  @Property({ required: true })
  credentials: object;

  @Field(() => Object)
  @Property({ required: true })
  config: object;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => Date)
  @Property({ required: true })
  lastSynced: Date;

  @Field(() => ID)
  @Property({ ref: 'User', required: true })
  createdBy: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const IntegrationModel = getModelForClass(Integration);
```

## Mobile Application Models

### Home Screen

#### UserPreferences
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class UserPreferences {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Golfer', required: true })
  golferId: Ref<any>;

  @Field(() => [ID])
  @Property({ type: [ID], default: [] })
  favoriteCoursesIds: Ref<any>[];

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  primaryClubId: Ref<any>;

  @Field(() => [PreferredPlayTime])
  @Property({ type: [PreferredPlayTime], default: [] })
  preferredPlayTimes: PreferredPlayTime[];

  @Field(() => NotificationPreferences)
  @Property({ type: NotificationPreferences, required: true, _id: false })
  notificationPreferences: NotificationPreferences;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const UserPreferencesModel = getModelForClass(UserPreferences);
```

#### NewsItem
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class NewsItem {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field()
  @Property({ required: true })
  title: string;

  @Field()
  @Property({ required: true })
  content: string;

  @Field()
  @Property({ required: true })
  summary: string;

  @Field(() => String)
  @Property({ required: true })
  imageUrl: string;

  @Field(() => String)
  @Property({ required: true })
  category: string;

  @Field(() => String)
  @Property({ required: true })
  source: string;

  @Field(() => String)
  @Property({ required: true })
  url: string;

  @Field(() => Date)
  @Property({ required: true })
  publishedAt: Date;

  @Field(() => Boolean)
  @Property({ required: true })
  featured: boolean;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Tournament', required: false })
  relatedTournamentId?: Ref<any>;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;
}

export const NewsItemModel = getModelForClass(NewsItem);
```

### Book a Tee Time

#### BookingResult
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class BookingResult {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'TeeTime', required: true })
  teeTimeId: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'Golfer', required: true })
  golferId: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => Date)
  @Property({ required: true })
  time: Date;

  @Field(() => [Player])
  @Property({ type: [Player], default: [] })
  players: Player[];

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  price: number;

  @Field(() => Number)
  @Property({ required: true, min: 0 })
  totalPrice: number;

  @Field(() => String)
  @Property({ required: true })
  paymentStatus: string;

  @Field(() => [PaymentRequest])
  @Property({ type: [PaymentRequest], default: [] })
  paymentRequests: PaymentRequest[];

  @Field(() => [AdditionalService])
  @Property({ type: [AdditionalService], default: [] })
  additionalServices: AdditionalService[];

  @Field(() => String)
  @Property({ required: true })
  confirmationCode: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const BookingResultModel = getModelForClass(BookingResult);
```

### In-Play Mode

#### Round
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class Round {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'TeeTime', required: true })
  teeTimeId: Ref<any>;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => Date)
  @Property({ required: true })
  date: Date;

  @Field(() => [ID])
  @Property({ ref: 'Golfer', default: [] })
  players: Ref<any>[];

  @Field(() => [Score])
  @Property({ type: [Score], default: [] })
  scores: Score[];

  @Field(() => String)
  @Property({ required: true })
  weatherConditions: string;

  @Field(() => [Feedback])
  @Property({ type: [Feedback], default: [] })
  feedback: Feedback[];

  @Field(() => [ID])
  @Property({ ref: 'F&BOrder', default: [] })
  orders: Ref<any>[];

  @Field(() => GameMode)
  @Property({ type: GameMode, required: true, _id: false })
  gameMode: GameMode;

  @Field(() => String)
  @Property({ required: true })
  status: string;

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const RoundModel = getModelForClass(Round);
```

#### CourseMap
```typescript
import { Field, ObjectType, ID } from 'type-graphql';
import { prop as Property, getModelForClass } from '@typegoose/typegoose';

@ObjectType()
export class CourseMap {
  @Field(() => ID)
  @Property({ auto: true })
  id: string;

  @Field(() => ID)
  @Property({ ref: 'Course', required: true })
  courseId: Ref<any>;

  @Field(() => String)
  @Property({ required: true })
  name: string;

  @Field(() => [Hole])
  @Property({ type: [Hole], default: [] })
  holes: Hole[];

  @Field(() => Date)
  @Property({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Property({ default: Date.now })
  updatedAt: Date;
}

export const CourseMapModel = getModelForClass(CourseMap);
```

### Marketplace

#### Cart
```typescript
```javascript
{
  id: ObjectId,
  teeTimeId: ObjectId,
  golferId: ObjectId,
  courseId: ObjectId,
  time: Date,
  players: [{
    golferId: ObjectId,
    name: String,
    email: String,
    hasPaid: Boolean
  }],
  price: Number,
  totalPrice: Number,
  paymentStatus: String, // 'pending', 'partial', 'complete'
  paymentRequests: [{
    playerId: ObjectId,
    email: String,
    amount: Number,
    status: String, // 'pending', 'paid', 'expired'
    expiresAt: Date
  }],
  additionalServices: [{
    type: String, // 'golf-balls', 'club-rental', etc.
    name: String,
    price: Number,
    productId: ObjectId // Reference to Product if applicable
  }],
  confirmationCode: String,
  createdAt: Date,
  updatedAt: Date
}
```

### In-Play Mode

#### Round
```javascript
{
  id: ObjectId,
  teeTimeId: ObjectId,
  courseId: ObjectId,
  date: Date,
  players: [ObjectId], // References to Golfer
  scores: [{
    playerId: ObjectId,
    holeScores: [Number], // Array of 18 scores
    totalScore: Number,
    details: {
      teeShots: [String], // 'fairway', 'left', 'right'
      approachShots: [String], // 'green', 'bunker', 'rough'
      putts: [Number] // Number of putts per hole
    }
  }],
  weatherConditions: String,
  feedback: [{
    question: String,
    rating: Number,
    hole: Number
  }],
  orders: [ObjectId], // References to F&B orders
  gameMode: {
    type: String, // 'scramble', 'skins', 'wolf', 'closest'
    wagerAmount: Number,
    results: [{
      playerId: ObjectId,
      playerName: String,
      amount: Number // Positive = winnings, Negative = owes
    }]
  },
  status: String, // 'active', 'completed', 'cancelled'
  createdAt: Date,
  updatedAt: Date
}
```

#### CourseMap
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  name: String,
  holes: [{
    number: Number,
    par: Number,
    distance: {
      black: Number,
      blue: Number,
      white: Number,
      red: Number
    },
    handicap: Number,
    layout: {
      teeCoordinates: {
        latitude: Number,
        longitude: Number
      },
      greenCoordinates: {
        latitude: Number,
        longitude: Number
      },
      fairwayCoordinates: [{ // Series of points defining the fairway
        latitude: Number,
        longitude: Number
      }],
      hazardCoordinates: [{
        type: String, // 'bunker', 'water', 'trees'
        coordinates: [{
          latitude: Number,
          longitude: Number
        }]
      }]
    }
  }],
  createdAt: Date,
  updatedAt: Date
}
```

### Marketplace

#### Cart
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  courseId: ObjectId,
  items: [{
    productId: ObjectId,
    name: String,
    price: Number,
    quantity: Number,
    attributes: Object // Size, color, etc.
  }],
  subtotal: Number,
  tax: Number,
  total: Number,
  createdAt: Date,
  updatedAt: Date
}
```

#### Order
```javascript
{
  id: ObjectId,
  cartId: ObjectId,
  golferId: ObjectId,
  courseId: ObjectId,
  items: [{
    productId: ObjectId,
    name: String,
    price: Number,
    quantity: Number,
    attributes: Object
  }],
  subtotal: Number,
  tax: Number,
  total: Number,
  paymentMethod: String,
  paymentId: String, // Payment processor reference
  status: String, // 'pending', 'processing', 'completed', 'cancelled'
  pickup: {
    location: String,
    date: Date,
    notes: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Golfer Profile

#### Membership
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  courseId: ObjectId,
  type: String, // 'full', 'weekday', 'junior', etc.
  membershipId: String,
  startDate: Date,
  expiryDate: Date,
  benefits: [String],
  status: String, // 'active', 'expired', 'pending'
  verifiedAt: Date,
  verifiedBy: ObjectId,
  createdAt: Date,
  updatedAt: Date
}
```

#### GolferEquipment
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  clubs: [{
    type: String, // 'driver', 'iron', 'putter', etc.
    brand: String,
    model: String,
    yearReleased: Number
  }],
  balls: String,
  otherEquipment: [String],
  updatedAt: Date
}
```

#### PaymentMethod
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  type: String, // 'credit', 'debit', 'paypal', etc.
  provider: String,
  lastFour: String,
  expiryDate: String,
  isDefault: Boolean,
  billingAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### Subscription
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  plan: String, // 'free', 'amateur', 'pro'
  price: Number,
  billingCycle: String, // 'monthly', 'annual'
  status: String, // 'active', 'cancelled', 'pastDue'
  startDate: Date,
  nextBillingDate: Date,
  paymentMethodId: ObjectId,
  features: [String], // Features included in this subscription tier
  createdAt: Date,
  updatedAt: Date
}
```

### Rewards

#### Rewards
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  totalPoints: Number,
  tier: String, // 'bronze', 'silver', 'gold'
  pointsToNextTier: Number,
  transactions: [{
    date: Date,
    activity: String, 
    points: Number,
    balance: Number
  }],
  updatedAt: Date
}
```

#### RewardOptions
```javascript
{
  id: ObjectId,
  courseId: ObjectId, // Optional, for course-specific rewards
  name: String,
  description: String,
  pointsCost: Number,
  category: String, // 'merchandise', 'tee-times', 'f&b', 'experience'
  imageUrl: String,
  quantity: Number, // Limited quantity available
  startDate: Date,
  endDate: Date, // Expiration date
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Prediction
```javascript
{
  id: ObjectId,
  golferId: ObjectId,
  tournamentId: ObjectId,
  tournamentName: String,
  predictedWinnerId: String,
  predictedWinnerName: String,
  result: String, // 'pending', 'correct', 'incorrect'
  pointsEarned: Number,
  createdAt: Date,
  updatedAt: Date
}
```

## Coach Platform Models

### Calendar

#### Lesson
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  studentIds: [ObjectId], // References to Golfer
  date: Date,
  startTime: String,
  endTime: String,
  duration: Number, // in minutes
  lessonType: String, // 'individual', 'group', 'clinic'
  notes: String,
  location: String,
  status: String, // 'scheduled', 'completed', 'cancelled'
  paymentStatus: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### TimeSlot
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  date: Date,
  startTime: String,
  endTime: String,
  status: String, // 'available', 'booked', 'blocked'
  recurring: Boolean,
  recurrencePattern: {
    frequency: String, // 'daily', 'weekly', etc.
    daysOfWeek: [Number], // 0-6 for Sunday-Saturday
    endDate: Date
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Messenger

#### Conversation
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  studentId: ObjectId,
  lastMessage: String,
  lastMessageTime: Date,
  unreadCount: Number,
  createdAt: Date,
  updatedAt: Date
}
```

#### Message
```javascript
{
  id: ObjectId,
  conversationId: ObjectId,
  senderId: ObjectId,
  content: String,
  attachments: [{
    type: String, // 'image', 'video', 'document'
    url: String,
    name: String,
    size: Number
  }],
  status: String, // 'sent', 'delivered', 'read'
  createdAt: Date
}
```

#### MessageTemplate
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  name: String,
  content: String,
  variables: [String], // Template variables like {{name}}
  category: String, // 'welcome', 'reminder', 'follow-up', etc.
  createdAt: Date,
  updatedAt: Date
}
```

### Payment

#### Payment
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  studentId: ObjectId,
  lessonId: ObjectId,
  amount: Number,
  serviceType: String,
  paymentMethod: String,
  transactionId: String,
  status: String,
  receiptUrl: String,
  createdAt: Date
}
```

#### ServiceType
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  name: String,
  description: String,
  price: Number,
  duration: Number, // in minutes
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Refund
```javascript
{
  id: ObjectId,
  paymentId: ObjectId,
  amount: Number,
  reason: String,
  status: String, // 'pending', 'completed', 'failed'
  processedBy: ObjectId,
  transactionId: String,
  createdAt: Date
}
```

### Academies & Clinics

#### Clinic
```javascript
{
  id: ObjectId,
  coachId: ObjectId,
  name: String,
  description: String,
  startDate: Date,
  endDate: Date,
  schedule: [{
    day: String,
    startTime: String,
    endTime: String
  }],
  capacity: Number,
  enrolledCount: Number,
  price: Number,
  location: String,
  students: [ObjectId], // References to Golfer
  createdAt: Date,
  updatedAt: Date
}
```

#### ClinicSession
```javascript
{
  id: ObjectId,
  clinicId: ObjectId,
  date: Date,
  startTime: String,
  endTime: String,
  topic: String,
  notes: String,
  attendance: [{
    studentId: ObjectId,
    present: Boolean,
    notes: String
  }],
  status: String, // 'scheduled', 'completed', 'cancelled'
  createdAt: Date,
  updatedAt: Date
}
``` 