# Albatros Golf Management System - Architecture Diagram

title: "Albatros Golf Management System\nGoogle Cloud Architecture" {
  near: top-center
  shape: text
  style.font-size: 40
  style.bold: true
  style.underline: true
}

# Define styles for reuse


# Create a legend box at the top
legend: {
  label: "Legend"
  style.fill: "#FFFFFF"
  style.stroke: "#CCCCCC"
  style.border-radius: 8
  style.shadow: true
  style.stroke-width: 1
  
  # Place legend items in a row
  direction: right
  
  cloud_legend: {
    label: "cloud"
    shape: rectangle
    style.fill: "#F8F9FA"
    style.stroke: "#DADCE0"
    style.border-radius: 10
  }
  
  service_legend: {
    label: "service"
    shape: rectangle
    style.fill: "#E8F0FE"
    style.stroke: "#4285F4"
  }
  
  database_legend: {
    label: "database"
    shape: rectangle
    style.fill: "#FEEFC3"
    style.stroke: "#F9AB00"
  }
  
  storage_legend: {
    label: "storage"
    shape: rectangle
    style.fill: "#D7AEFB"
    style.stroke: "#9334E6"
  }
  
  security_legend: {
    label: "security"
    shape: rectangle
    style.fill: "#CEEAD6"
    style.stroke: "#34A853"
  }
  
  golfer_legend: {
    label: "golfer"
    shape: rectangle
    style.fill: "#CBF0F8"
    style.stroke: "#00ACC1"
  }
  
  client_legend: {
    label: "client"
    shape: rectangle
    style.fill: "#F6E3CE"
    style.stroke: "#E8710A"
  }
}



# End Users
users: {
  label: "End Users"
  style.fill: "#CBF0F8"
  style.stroke: "#00ACC1"
  style.border-radius: 10
  style.shadow: true
  style.stroke-width: 2
  
  # Place users in a column
  direction: down
  
  golfers: {
    label: "Golfers"
    shape: person
  }
  
  staff: {
    label: "Course Staff"
    shape: person
  }
  
  coaches: {
    label: "Coaches"
    shape: person
  }
}

# Main diagram
direction: down

# Client Applications
clients: {
  label: "Client Applications"
  style.fill: "#F6E3CE"
  style.stroke: "#E8710A"
  style.border-radius: 10
  style.shadow: true
  style.stroke-width: 2
  
  # Place clients in a column
  direction: down
  
  mobile_app: {
    label: "Mobile App\n(React Native)"
    shape: rectangle
  }
  
  web_crm: {
    label: "Desktop CRM\n(React)"
    shape: rectangle
  }
  
  coach_portal: {
    label: "Coach Portal\n(React)"
    shape: rectangle
  }
}

# Google Cloud Platform - main container
gcp: {
  label: "Google Cloud Platform"
  style.fill: "#F8F9FA"
  style.stroke: "#DADCE0"
  style.border-radius: 10
  style.shadow: true
  style.stroke-width: 2
  shape: cloud
  
  # Use a horizontal layout for GCP components
  direction: right
  
  # Four distinct columns for service layers
  frontend: {
    label: "Frontend Layer"
    style.fill: "#E8F0FE"
    style.stroke: "#4285F4"
    style.border-radius: 10
    style.shadow: true
    style.stroke-width: 2
    direction: down
    
    cdn: {
      label: "Cloud CDN"
      shape: hexagon
    }
    
    lb: {
      label: "Load Balancer"
      shape: diamond
    }
    
    armor: {
      label: "Cloud Armor"
      style.fill: "#CEEAD6"
      style.stroke: "#34A853"
      shape: hexagon
    }
  }
  
  app: {
    label: "Application Layer"
    style.fill: "#E8F0FE"
    style.stroke: "#4285F4"
    style.border-radius: 10
    style.shadow: true
    style.stroke-width: 2
    direction: down
    
    cloud_run: {
      label: "Cloud Run\nNode.js + TypeScript"
      shape: rectangle
    }
    
    firebase_auth: {
      label: "Firebase Authentication"
      style.fill: "#CEEAD6"
      style.stroke: "#34A853"
      shape: cylinder
    }
  }
  
  data: {
    label: "Data Layer"
    style.fill: "#FEEFC3"
    style.stroke: "#F9AB00"
    style.border-radius: 10
    style.shadow: true
    style.stroke-width: 2
    direction: down
    
    mongodb: {
      label: "MongoDB Atlas"
      shape: cylinder
    }
    
    redis: {
      label: "Memorystore\n(Redis)"
      shape: cylinder
    }
    
    cloud_storage: {
      label: "Cloud Storage"
      style.fill: "#D7AEFB"
      style.stroke: "#9334E6"
      shape: cylinder
    }
  }
  
  management: {
    label: "Management Layer"
    style.fill: "#E8F0FE"
    style.stroke: "#4285F4"
    style.border-radius: 10
    style.shadow: true
    style.stroke-width: 2
    direction: down
    
    secret_manager: {
      label: "Secret Manager"
      style.fill: "#CEEAD6"
      style.stroke: "#34A853"
      shape: oval
    }
    
    monitoring: {
      label: "Cloud Monitoring"
      shape: hexagon
    }
    
    logging: {
      label: "Cloud Logging"
      shape: page
    }
  }
}

# User to Client connections
users.golfers -> clients.mobile_app: "Uses"
users.staff -> clients.web_crm: "Uses"
users.coaches -> clients.coach_portal: "Uses"

# Client to Frontend connections
clients.mobile_app -> gcp.frontend.cdn: "HTTPS" {
  source-arrowhead: none
}
clients.web_crm -> gcp.frontend.cdn: "HTTPS" {
  source-arrowhead: none
}
clients.coach_portal -> gcp.frontend.cdn: "HTTPS" {
  source-arrowhead: none
}

# Frontend flow
gcp.frontend.cdn -> gcp.frontend.lb: "Routes" {
  source-arrowhead: none
}
gcp.frontend.lb -> gcp.frontend.armor: "Secures" {
  source-arrowhead: none
}
gcp.frontend.armor -> gcp.app.cloud_run: "Access" {
  source-arrowhead: none
}

# App connections
gcp.app.cloud_run -> gcp.app.firebase_auth: "Auth" {
  source-arrowhead: none
}

# Data connections
gcp.app.cloud_run -> gcp.data.mongodb: "Data" {
  source-arrowhead: none
}
gcp.app.cloud_run -> gcp.data.redis: "Cache" {
  source-arrowhead: none
}
gcp.app.cloud_run -> gcp.data.cloud_storage: "Files" {
  source-arrowhead: none
}

# Management connections
gcp.app.cloud_run -> gcp.management.secret_manager: "Secrets" {
  source-arrowhead: none
}
gcp.management.monitoring -> gcp.app.cloud_run: "Monitor" {
  source-arrowhead: none
}
gcp.management.logging -> gcp.app.cloud_run: "Log" {
  source-arrowhead: none
}

# Layout for better spacing
legend {
  grid-columns: 7
  grid-gap: 20
}

users {
  grid-gap: 20
}

clients {
  grid-gap: 20
}

gcp {
  grid-gap: 60
}

gcp.frontend {
  grid-gap: 40
}

gcp.app {
  grid-gap: 40
}

gcp.data {
  grid-gap: 40
}

gcp.management {
  grid-gap: 40
}

# Main diagram flow
legend -> users -> clients -> gcp

# Set global direction
direction: down 