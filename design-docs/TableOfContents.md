# Albatros CRM - Table of Contents

This document provides a comprehensive table of contents for the Albatros Golf Management System, with links to both the Design Document and the Model Design Document.

## Design Document

- [Overview](DesignDocument.md#overview)
- [Goals](DesignDocument.md#goals)
- [System Architecture Overview](DesignDocument.md#system-architecture-overview)
  - [Access Hierarchy](DesignDocument.md#access-hierarchy)
  - [Core Applications](DesignDocument.md#core-applications)
- [Technology Stack](DesignDocument.md#technology-stack)
  - [Backend](DesignDocument.md#backend)
  - [Frontend](DesignDocument.md#frontend)
  - [Infrastructure](DesignDocument.md#infrastructure)
- [High-Level Architecture](DesignDocument.md#high-level-architecture)
  - [Microservices Structure](DesignDocument.md#microservices-structure)

## Desktop CRM Modules

- [Dashboard](DesignDocument.md#dashboard)
  - [Vision](DesignDocument.md#dashboard)
  - [Scaling Approach](DesignDocument.md#dashboard)
  - [Key Features](DesignDocument.md#dashboard)
  - [Data Components](DesignDocument.md#dashboard)
  - [AI Suggestive Action Modal](DesignDocument.md#dashboard)
  - [Data Models](ModelDesign.md#dashboard)

- [Calendar/Tee Sheet](DesignDocument.md#calendartee-sheet)
  - [Vision](DesignDocument.md#calendartee-sheet)
  - [Scaling Approach](DesignDocument.md#calendartee-sheet)
  - [Key Features](DesignDocument.md#calendartee-sheet)
  - [Data Models](ModelDesign.md#calendartee-sheet)

- [Roster](DesignDocument.md#roster)
  - [Vision](DesignDocument.md#roster)
  - [Scaling Approach](DesignDocument.md#roster)
  - [Key Features](DesignDocument.md#roster)
  - [Data Models](ModelDesign.md#roster)

- [Pro Shop](DesignDocument.md#pro-shop)
  - [Vision](DesignDocument.md#pro-shop)
  - [Scaling Approach](DesignDocument.md#pro-shop)
  - [Key Features](DesignDocument.md#pro-shop)
  - [Data Models](ModelDesign.md#pro-shop)

- [19th Hole (F&B)](DesignDocument.md#19th-hole-fb)
  - [Vision](DesignDocument.md#19th-hole-fb)
  - [Scaling Approach](DesignDocument.md#19th-hole-fb)
  - [Key Features](DesignDocument.md#19th-hole-fb)
  - [Data Models](ModelDesign.md#19th-hole-fb)

- [Events & Tournaments](DesignDocument.md#events--tournaments)
  - [Vision](DesignDocument.md#events--tournaments)
  - [Scaling Approach](DesignDocument.md#events--tournaments)
  - [Key Features](DesignDocument.md#events--tournaments)
  - [Data Models](ModelDesign.md#events--tournaments)

- [Analytics/Insights](DesignDocument.md#analyticsinsights)
  - [Vision](DesignDocument.md#analyticsinsights)
  - [Scaling Approach](DesignDocument.md#analyticsinsights)
  - [Key Features](DesignDocument.md#analyticsinsights)
  - [GraphQL Implementation](DesignDocument.md#analyticsinsights)
  - [Data Models](ModelDesign.md#analytics)

- [Back Office](DesignDocument.md#back-office)
  - [Vision](DesignDocument.md#back-office)
  - [Scaling Approach](DesignDocument.md#back-office)
  - [Key Features](DesignDocument.md#back-office)
  - [Data Models](ModelDesign.md#back-office)

- [Settings](DesignDocument.md#settings)
  - [Vision](DesignDocument.md#settings)
  - [Scaling Approach](DesignDocument.md#settings)
  - [Key Features](DesignDocument.md#settings)
  - [Data Models](ModelDesign.md#settings)

## Mobile Application Features

- [Home Screen](DesignDocument.md#home-screen)
  - [Vision](DesignDocument.md#home-screen)
  - [Scaling Approach](DesignDocument.md#home-screen)
  - [Key Features](DesignDocument.md#home-screen)
  - [GraphQL Implementation](DesignDocument.md#mobile-app---home-screen)
  - [Data Models](ModelDesign.md#home-screen)

- [Book a Tee Time](DesignDocument.md#book-a-tee-time)
  - [Vision](DesignDocument.md#book-a-tee-time)
  - [Scaling Approach](DesignDocument.md#book-a-tee-time)
  - [Key Features](DesignDocument.md#book-a-tee-time)
  - [GraphQL Implementation](DesignDocument.md#mobile-app---book-a-tee-time)
  - [Data Models](ModelDesign.md#book-a-tee-time)

- [In-Play Mode](DesignDocument.md#in-play-mode)
  - [Vision](DesignDocument.md#in-play-mode)
  - [Scaling Approach](DesignDocument.md#in-play-mode)
  - [Key Features](DesignDocument.md#in-play-mode)
  - [GraphQL Implementation](DesignDocument.md#mobile-app---in-play-mode)
  - [Data Models](ModelDesign.md#in-play-mode)

- [Marketplace](DesignDocument.md#marketplace)
  - [Vision](DesignDocument.md#marketplace)
  - [Scaling Approach](DesignDocument.md#marketplace)
  - [Key Features](DesignDocument.md#marketplace)
  - [Data Models](ModelDesign.md#marketplace)

- [Golfer Profile](DesignDocument.md#golfer-profile)
  - [Vision](DesignDocument.md#golfer-profile)
  - [Scaling Approach](DesignDocument.md#golfer-profile)
  - [Key Features](DesignDocument.md#golfer-profile)
  - [Data Models](ModelDesign.md#golfer-profile)

- [Rewards](DesignDocument.md#rewards)
  - [Vision](DesignDocument.md#rewards)
  - [Scaling Approach](DesignDocument.md#rewards)
  - [Key Features](DesignDocument.md#rewards)
  - [GraphQL Implementation](DesignDocument.md#mobile-app---rewards)
  - [Data Models](ModelDesign.md#rewards)

## Coach Platform Features

- [Calendar](DesignDocument.md#calendar)
  - [Vision](DesignDocument.md#calendar)
  - [Scaling Approach](DesignDocument.md#calendar)
  - [Key Features](DesignDocument.md#calendar)
  - [Data Models](ModelDesign.md#calendar)
  - [GraphQL Implementation](DesignDocument.md#coach-platform---calendar)

- [Messenger](DesignDocument.md#messenger)
  - [Vision](DesignDocument.md#messenger)
  - [Scaling Approach](DesignDocument.md#messenger)
  - [Key Features](DesignDocument.md#messenger)
  - [Implementation](DesignDocument.md#messenger)
  - [GraphQL Implementation](DesignDocument.md#coach-platform---messenger)
  - [Data Models](ModelDesign.md#messenger)

- [Payment](DesignDocument.md#payment)
  - [Vision](DesignDocument.md#payment)
  - [Scaling Approach](DesignDocument.md#payment)
  - [Key Features](DesignDocument.md#payment)
  - [Data Models](ModelDesign.md#payment)
  - [GraphQL Implementation](DesignDocument.md#coach-platform---payment)

- [Academies & Clinics](DesignDocument.md#academies--clinics)
  - [Vision](DesignDocument.md#academies--clinics)
  - [Scaling Approach](DesignDocument.md#academies--clinics)
  - [Key Features](DesignDocument.md#academies--clinics)
  - [Data Models](ModelDesign.md#academies--clinics)
  - [GraphQL Implementation](DesignDocument.md#coach-platform---academies--clinics)

## Architecture and Implementation

- [AI Suggestive Action System](DesignDocument.md#ai-suggestive-action-system)
  - [Key AI-Driven Features](DesignDocument.md#key-ai-driven-features)

- [GraphQL and Express Integration](DesignDocument.md#graphql-and-express-integration)
  - [Apollo Server and Express Relationship](DesignDocument.md#apollo-server-and-express-relationship)
  - [Benefits of This Approach](DesignDocument.md#benefits-of-this-approach)

- [Module Structure](DesignDocument.md#module-structure)

- [Data Models and Schema Management](DesignDocument.md#data-models-and-schema-management)
  - [TypeScript Decorators as Single Source of Truth](DesignDocument.md#typescript-decorators-as-single-source-of-truth)
  - [Benefits of Decorator Approach](DesignDocument.md#benefits-of-decorator-approach)

- [Base Model Class and GraphQL API Structure](DesignDocument.md#base-model-class-and-graphql-api-structure)
  - [Base Model Class with TypeGraphQL and Typegoose](DesignDocument.md#base-model-class-with-typegraphql-and-typegoose)
  - [GraphQL API Structure](DesignDocument.md#graphql-api-structure)

- [Authentication & Authorization](DesignDocument.md#authentication--authorization)
  - [Decorator-Based Authorization](DesignDocument.md#decorator-based-authorization)
  - [Multi-Tiered Authentication Flow](DesignDocument.md#multi-tiered-authentication-flow)

- [Implementation Phases](DesignDocument.md#implementation-phases)
  - [Phase 1: Foundation](DesignDocument.md#phase-1-foundation)
  - [Phase 2: Core CRM Features](DesignDocument.md#phase-2-core-crm-features)
  - [Phase 3: Extended CRM Features](DesignDocument.md#phase-3-extended-crm-features)
  - [Phase 4: Mobile Features](DesignDocument.md#phase-4-mobile-features)
  - [Phase 5: Coach Platform](DesignDocument.md#phase-5-coach-platform)
  - [Phase 6: Integration and Refinement](DesignDocument.md#phase-6-integration-and-refinement)

- [Conclusion](DesignDocument.md#conclusion)

## Data Models Reference

For a comprehensive reference of all data models used in the system, see the [ModelDesign document](ModelDesign.md):

- [Core Models](ModelDesign.md#core-models)
  - [User](ModelDesign.md#user) - TypeScript class with GraphQL and Mongoose decorators
  - [Course](ModelDesign.md#course) - TypeScript class with GraphQL and Mongoose decorators
  - [Golfer](ModelDesign.md#golfer) - TypeScript class with GraphQL and Mongoose decorators

- [CRM Modules](ModelDesign.md#crm-modules)
  - [Dashboard](ModelDesign.md#dashboard) - TypeScript classes with decorators
  - [Calendar/Tee Sheet](ModelDesign.md#calendartee-sheet) - TypeScript classes with decorators
  - [Roster](ModelDesign.md#roster) - TypeScript classes with decorators
  - [Pro Shop](ModelDesign.md#pro-shop) - TypeScript classes with decorators
  - [19th Hole (F&B)](ModelDesign.md#19th-hole-fb) - TypeScript classes with decorators
  - [Events & Tournaments](ModelDesign.md#events--tournaments) - TypeScript classes with decorators
  - [Analytics](ModelDesign.md#analytics) - TypeScript classes with decorators
  - [Back Office](ModelDesign.md#back-office) - TypeScript classes with decorators
  - [Settings](ModelDesign.md#settings) - TypeScript classes with decorators

- [Mobile Application Models](ModelDesign.md#mobile-application-models)
  - [Home Screen](ModelDesign.md#home-screen) - TypeScript classes with decorators
  - [Book a Tee Time](ModelDesign.md#book-a-tee-time) - TypeScript classes with decorators
  - [In-Play Mode](ModelDesign.md#in-play-mode) - TypeScript classes with decorators
  - [Marketplace](ModelDesign.md#marketplace) - TypeScript classes with decorators
  - [Golfer Profile](ModelDesign.md#golfer-profile) - TypeScript classes with decorators
  - [Rewards](ModelDesign.md#rewards) - TypeScript classes with decorators

- [Coach Platform Models](ModelDesign.md#coach-platform-models)
  - [Calendar](ModelDesign.md#calendar) - TypeScript classes with decorators
  - [Messenger](ModelDesign.md#messenger) - TypeScript classes with decorators
  - [Payment](ModelDesign.md#payment) - TypeScript classes with decorators
  - [Academies & Clinics](ModelDesign.md#academies--clinics) - TypeScript classes with decorators 