# Albatros Golf Management System - Infrastructure Design

## Table of Contents
- [Overview](#overview)
- [Infrastructure Philosophy](#infrastructure-philosophy)
- [Google Cloud Implementation](#google-cloud-implementation)
  - [Architecture Overview](#architecture-overview)
  - [Core Services](#core-services)
  - [Data Storage](#data-storage)
  - [Networking](#networking)
  - [Security](#security)
  - [Monitoring and Logging](#monitoring-and-logging)
- [Multi-Tenant Architecture](#multi-tenant-architecture)
  - [Data Isolation Strategy](#data-isolation-strategy)
  - [Tenant Onboarding](#tenant-onboarding)
- [Self-Hosted Alternative](#self-hosted-alternative)
  - [Hardware Requirements](#hardware-requirements)
  - [Software Stack](#software-stack)
  - [Network Configuration](#network-configuration)
  - [Maintenance Considerations](#maintenance-considerations)
- [Deployment Pipeline](#deployment-pipeline)
- [Scaling Strategy](#scaling-strategy)
- [Backup and Disaster Recovery](#backup-and-disaster-recovery)
- [Cost Analysis](#cost-analysis)
  - [Google Cloud Estimated Costs](#google-cloud-estimated-costs)
  - [Self-Hosted Estimated Costs](#self-hosted-estimated-costs)
  - [Cost Comparison](#cost-comparison)
- [Implementation Roadmap](#implementation-roadmap)

## Overview

This document outlines the infrastructure design for the Albatros Golf Management System, providing guidance for both cloud-based deployment on Google Cloud Platform (GCP) and self-hosted alternatives. The design prioritizes cost-effectiveness, scalability, and performance while maintaining high availability and security.

## Infrastructure Philosophy

While microservices architecture has become popular, for mid-market solutions like the Albatros Golf Management System, a **"scalable monolith"** approach often provides better cost efficiency and reduced operational complexity. This approach:

1. Reduces deployment complexity
2. Minimizes inter-service communication overhead
3. Simplifies debugging and monitoring
4. Lowers infrastructure costs
5. Allows easier development and testing

Our approach allows the system to be "microservice-ready" but deployed as a cohesive unit, with the option to extract high-traffic components as separate services if needed later.

## Google Cloud Implementation

### Architecture Overview

The architecture diagram below illustrates the Albatros Golf Management System deployment on Google Cloud Platform. The diagram was created using D2, a declarative diagramming language, and can be found in the `architecture-diagram.d2` file.

![Architecture Diagram](architecture-diagram.png)

*Note: To generate or update this diagram, install D2 (https://d2lang.com) and run:*
```bash
d2 architecture-diagram.d2 architecture-diagram.png
```

The system is deployed as a scalable monolith with the following characteristics:

- Node.js backend with TypeScript
- React frontends for different user interfaces
- MongoDB for data storage
- Redis for caching and session management
- Cloud Storage for static assets

### Core Services

#### Compute: Google Cloud Run

Google Cloud Run provides serverless, containerized application deployment that automatically scales based on traffic and scales to zero when not in use.

**Benefits:**
- Automatic scaling
- Pay-per-use billing model
- No infrastructure management
- Simple container deployment
- Fast cold starts
- Integrated with other GCP services

**Configuration:**
```yaml
CPU allocation: 2 vCPU
Memory: 4 GB
Concurrency: 80 requests per instance
Autoscaling: min 0, max 10 instances
```

#### Authentication: Firebase Authentication

Firebase Authentication provides a complete authentication system with support for multiple login methods.

**Implementation:**
- JWT-based authentication
- Role-based access control
- Multi-factor authentication for administrative users
- Integration with the TypeScript decorators for authorization

### Data Storage

#### Primary Database: Cloud SQL for MongoDB

MongoDB Atlas on Google Cloud provides a fully managed MongoDB database service with automatic sharding and backups.

**Configuration:**
- M30 tier (dedicated cluster)
- 3 nodes for high availability
- Automatic backups
- 10GB to start, with auto-scaling

#### Caching Layer: Memorystore (Redis)

Redis is used for caching frequently accessed data and session management.

**Configuration:**
- 1GB instance (basic tier)
- Standard tier for high availability
- No public IP (accessed only from Cloud Run)

#### File Storage: Cloud Storage

Used for storing user-uploaded content, images, PDFs, and other static assets.

**Implementation:**
- Standard storage tier
- Regional bucket for primary access
- Lifecycle policies to archive old content

### Networking

#### Cloud CDN and Load Balancing

Google Cloud CDN sits in front of the application to deliver static content quickly.

**Implementation:**
- Global HTTP(S) load balancer
- Cloud CDN for static content
- Custom domain mapping

#### VPC Service Controls

VPC Service Controls provide additional security by creating a security perimeter around the application.

**Configuration:**
- Private connectivity between services
- Access context restrictions for administrative functions

### Security

#### Cloud Armor

Cloud Armor provides DDoS protection and web application firewall (WAF) capabilities.

**Implementation:**
- Preconfigured WAF rules
- Rate limiting
- Geolocation-based restrictions

#### Secret Manager

Google Secret Manager stores API keys, passwords, and other sensitive configuration values.

**Implementation:**
- Automatic rotation for database credentials
- Integration with deployment pipeline
- Access controls based on service accounts

### Monitoring and Logging

#### Cloud Monitoring

Cloud Monitoring provides visibility into the performance, uptime, and health of the application.

**Implementation:**
- Custom dashboards for key metrics
- Alerting policies for critical thresholds
- Uptime checks for essential endpoints

#### Cloud Logging

Cloud Logging collects and analyzes logs from the application.

**Implementation:**
- Structured logging
- Error reporting integration
- Log-based metrics
- 30-day retention policy

## Multi-Tenant Architecture

The Albatros system is designed to support multiple golf courses or organizations as separate tenants within a single instance.

### Data Isolation Strategy

We implement a hybrid multi-tenancy approach that balances security and resource efficiency:

1. **Shared Infrastructure**: All tenants share the same application servers and Redis cache
2. **Database Isolation**: Each tenant has dedicated MongoDB collections with a tenant prefix
3. **Storage Isolation**: Tenant storage is isolated through prefixed Cloud Storage paths

**Implementation:**
- Tenant identifiers in all API requests
- Middleware that enforces tenant isolation
- Database schema design with tenant fields
- Access control lists (ACLs) for cross-tenant resources

### Tenant Onboarding

The system includes an administrative interface for onboarding new golf courses or organizations:

1. Tenant registration workflow
2. Initial data configuration
3. User role and permission setup
4. Branding customization
5. Integration configuration

## Self-Hosted Alternative

For clients who require on-premises deployment, we provide a self-hosted option with similar architecture but adapted for private data center or server room deployment.

### Hardware Requirements

#### Production Environment (High Availability)

| Component | Specifications | Quantity | Purpose |
|-----------|---------------|----------|---------|
| Application Servers | 8 vCPU, 16GB RAM, 100GB SSD | 2+ | Running the Node.js application |
| Database Servers | 8 vCPU, 32GB RAM, 500GB SSD | 3 | MongoDB replica set |
| Cache Servers | 4 vCPU, 8GB RAM, 50GB SSD | 2 | Redis for caching |
| Load Balancer | 2 vCPU, 4GB RAM | 2 | Traffic distribution |
| Storage Server | 4 vCPU, 8GB RAM, 2TB+ SSD/HDD hybrid | 1 | File storage |

#### Minimum Viable Environment

| Component | Specifications | Quantity | Purpose |
|-----------|---------------|----------|---------|
| Server | 16 vCPU, 64GB RAM, 1TB SSD | 1 | All-in-one server (app, DB, cache) |
| Backup Device | NAS with 2TB storage | 1 | Backups |

### Software Stack

The self-hosted solution uses open-source alternatives to GCP services:

| Component | Software | Purpose |
|-----------|----------|---------|
| Container Orchestration | Docker + Docker Compose | Running containerized application |
| Database | MongoDB Community Edition | Primary data storage |
| Caching | Redis | Session and data caching |
| Reverse Proxy | NGINX | Load balancing, SSL termination |
| Monitoring | Prometheus + Grafana | System monitoring |
| Log Management | ELK Stack (Elasticsearch, Logstash, Kibana) | Log collection and analysis |
| Backup | Duplicati | Automated backups |

### Network Configuration

**Requirements:**
- Dedicated VLAN for the application
- Firewall rules to control access
- SSL certificates for secure communication
- VPN for remote administration

**Recommended Network Diagram:**
```
Internet → Firewall → Load Balancer → Application Servers → Database Servers
                                    → Cache Servers       → Storage Server
```

### Maintenance Considerations

Self-hosted deployments require regular maintenance:

1. **Operating System Updates**: Monthly patching schedule
2. **Database Maintenance**: Index optimization, regular backups
3. **Application Updates**: Quarterly update schedule
4. **Security Audits**: Semi-annual security reviews
5. **Performance Monitoring**: Ongoing resource utilization tracking

We provide maintenance scripts and documentation for common tasks.

## Deployment Pipeline

The deployment pipeline is consistent across both cloud and self-hosted options, using CI/CD practices:

1. **Code Repository**: GitHub or self-hosted GitLab
2. **CI/CD Tool**: GitHub Actions or Jenkins
3. **Container Registry**: Google Container Registry or private Harbor registry
4. **Deployment Strategy**: Blue/green deployment for zero-downtime updates

**Deployment Workflow:**
1. Developer commits code to repository
2. CI/CD pipeline runs tests and builds container
3. Container is pushed to registry
4. Deployment job updates the application
5. Health checks confirm successful deployment

## Scaling Strategy

### Vertical Scaling

- Increase CPU and memory for Cloud Run instances or VM instances
- Upgrade database tier as needed

### Horizontal Scaling

- Add more Cloud Run instances or VM replicas
- Implement database read replicas
- Distribute high-volume tenants across dedicated resources

### Application-Level Scaling

- Implement caching for frequently accessed data
- Optimize database queries and indexing
- Use background jobs for resource-intensive operations

## Backup and Disaster Recovery

### Backup Strategy

**Google Cloud:**
- Automated daily database backups with 30-day retention
- Point-in-time recovery capability
- Weekly export to long-term storage (Cloud Storage)

**Self-Hosted:**
- Daily automated backups to local storage
- Weekly offsite backups
- Monthly full system backups

### Disaster Recovery

**Recovery Time Objective (RTO):** 4 hours
**Recovery Point Objective (RPO):** 24 hours

**Recovery Process:**
1. Restore database from latest backup
2. Deploy application containers
3. Restore file storage
4. Verify system integrity
5. Redirect traffic to restored system

## Cost Analysis

### Google Cloud Estimated Costs

| Service | Configuration | Monthly Cost (USD) |
|---------|--------------|-------------------|
| Cloud Run | 2 vCPU, 4GB, average 5 instances | $150-250 |
| MongoDB Atlas | M30 cluster | $400-500 |
| Memorystore (Redis) | 1GB instance | $50-75 |
| Cloud Storage | 500GB + operations | $20-40 |
| Cloud CDN | 500GB outbound | $40-60 |
| Cloud Armor | WAF protection | $5-10 |
| Monitoring & Logging | Basic tier | $30-50 |
| **Total Estimated** | | **$700-1,000** |

**Notes:**
- Costs scale with usage; the system scales to zero during very low usage periods
- Costs are shared across all tenants, making per-tenant cost decrease as more clients are added

### Self-Hosted Estimated Costs

| Component | Initial Cost (USD) | Monthly Operational Cost (USD) |
|-----------|-------------------|------------------------------|
| Hardware (amortized) | $15,000-25,000 | $500-800 |
| Software Licenses | $0 (open source) | $0 |
| Data Center / Colocation | - | $500-1,000 |
| Bandwidth | - | $200-500 |
| IT Personnel | - | $5,000-8,000 |
| **Total Estimated** | **$15,000-25,000** | **$6,200-10,300** |

### Cost Comparison

**Break-even Analysis:**
- Cloud deployment is more cost-effective for most deployments with <50 golf courses
- Self-hosted becomes cost-effective at high scale (50+ courses) or when regulatory/compliance requirements necessitate on-premises deployment
- Hidden costs of self-hosted (maintenance, upgrades, personnel) often exceed cloud costs

## Implementation Roadmap

### Phase 1: Foundation (Month 1-2)
- Set up Google Cloud environment or self-hosted infrastructure
- Deploy core database and storage services
- Implement CI/CD pipeline
- Deploy baseline application

### Phase 2: Production Readiness (Month 2-3)
- Set up monitoring and logging
- Implement backup and disaster recovery procedures
- Perform security hardening and testing
- Load testing and performance optimization

### Phase 3: Multi-Tenant Capabilities (Month 3-4)
- Implement tenant isolation
- Develop tenant onboarding flows
- Set up billing and metering (for SaaS model)

### Phase 4: Performance Optimization (Month 4-5)
- Implement caching strategies
- Optimize database queries
- Set up CDN for static assets
- Tune autoscaling parameters

### Phase 5: Ongoing Operations (Month 5+)
- Regular security updates
- Performance monitoring and optimization
- Cost optimization
- Feature enhancement 