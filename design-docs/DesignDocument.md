# Albatros CRM - Node.js Implementation Design Document

## Overview

This document outlines the design plan for the Albatros Golf Club Management System. We will build a Node.js backend that replaces the current Go prototype. The system uses GraphQL for API interactions, MongoDB for data storage, and AWS with Docker and Elastic Beanstalk for hosting.

The Albatros platform includes three connected applications:
1. **Albatros CRM** - Desktop system for golf course operators
2. **Albatros Mobile** - Mobile app for golfers
3. **Albatros Coach** - Platform for golf instructors

## Goals

- Build a modular microservices architecture with independent feature modules
- Use GraphQL for efficient data fetching
- Create a multi-tiered access model (Corporate → Regional → Course)
- Support both web and mobile interfaces with consistent data access
- Add AI-driven quick actions for common tasks
- Include thorough test coverage
- Provide clear API documentation
- Design for easy scaling
- Support React or Astro frontend development

## System Architecture Overview

### Access Hierarchy

The system uses a three-level access model:

1. **Corporate Level** 
   - Access to all courses and data
   - Optional geographic grouping (Regional, State, Metropolitan area)
   - Corporate-level analytics and reporting

2. **Course Level** 
   - Individual golf course management
   - Limited view of other courses based on permissions

3. **User Roles** 
   - Role-based access within each level
   - Customizable permissions per role

### Core Applications

1. **Desktop CRM**
   - Management interface for course operators
   - Module-based design
   - AI-driven quick actions

2. **Mobile App**
   - Golfer-facing application
   - Tee time booking
   - On-course experience features

3. **Coach Platform**
   - Tools for golf instructors
   - Student and lesson management
   - Payment processing

## Technology Stack

### Backend
- **Runtime**: Node.js (LTS version)
- **API Layer**: 
  - GraphQL with Apollo Server
  - Express for web server framework
- **Database**: MongoDB
- **Authentication**: JWT with refresh tokens
- **ORM/ODM**: Mongoose
- **Schema Generation**: graphql-compose-mongoose
- **Testing**: Jest, Supertest
- **Documentation**: GraphQL Playground, OpenAPI/Swagger
- **Validation**: Joi, GraphQL validators
- **Logging**: Winston, Morgan
- **AI Integration**: TensorFlow.js

### Frontend
- **Desktop CRM**: React with Material UI
- **Mobile App**: React Native
- **Coach Platform**: React (responsive design)

### Infrastructure
- **Containerization**: Docker
- **Orchestration**: Docker Compose (dev), AWS Elastic Beanstalk (prod)
- **Cloud Provider**: AWS
- **CI/CD**: GitHub Actions or AWS CodePipeline
- **Monitoring**: AWS CloudWatch, Prometheus
- **Media Storage**: AWS S3
- **CDN**: AWS CloudFront

## High-Level Architecture

```
                           +-------------------+
                           |                   |
                           |  API Gateway      |
                           |                   |
                           +-------------------+
                                     |
                                     |
        +---------------------------+|+-------------------------+
        |                            |                          |
        v                            v                          v
+----------------+        +------------------+        +------------------+
|                |        |                  |        |                  |
| Desktop CRM    |        | Mobile App       |        | Coach Platform   |
| Microservices  |        | Microservices    |        | Microservices    |
|                |        |                  |        |                  |
+----------------+        +------------------+        +------------------+
        |                            |                          |
        +---------------------------+|+-------------------------+
                                     |
                                     v
                           +-------------------+
                           |                   |
                           |  Shared Services  |
                           |                   |
                           +-------------------+
                                     |
                                     v
                           +-------------------+
                           |                   |
                           |     MongoDB       |
                           |                   |
                           +-------------------+
```

### Microservices Structure

The system will use domain-specific microservices:

1. **Core Services**
   - Authentication/Authorization
   - User Management
   - Course Management
   - Configuration

2. **CRM Modules**
   - Dashboard
   - Calendar/Tee Sheet
   - Roster
   - Pro Shop
   - 19th Hole (F&B)
   - Events & Tournaments
   - Analytics
   - Back Office
   - Settings

3. **Mobile Services**
   - User Profile
   - Tee Time Booking
   - In-Play Mode
   - Marketplace
   - Rewards
   - Coach Booking

4. **Coach Services**
   - Calendar
   - Messaging
   - Payment Processing
   - Academies & Clinics
   - Analytics

5. **Shared Services**
   - Notification System
   - Payment Processing
   - Analytics Engine
   - AI Suggestion Engine
   - Integration Hub

## Desktop CRM Modules

### Dashboard

**Vision:** 
The Dashboard serves as the nerve center of the golf course operation. It shows staff what needs attention right now. Each user sees a customized view based on their role. Managers see revenue and bookings, front desk staff see upcoming check-ins, and maintenance staff see course conditions.

The Dashboard features a unique AI assistant panel that learns from user behavior and suggests the most common actions. This reduces clicks and speeds up common tasks.

**Scaling Approach:**
- Data aggregation happens at the database level using MongoDB aggregation pipelines
- Widget data loads independently to prevent blocking
- Time-series metrics use data downsampling for historical views 
- UI components render progressively based on data availability
- Resource-intensive metrics are cached with configurable TTL

**Key Features:**
- Persona-based home screen with relevant KPIs
- Configurable widgets for different metrics
- Left-side panel navigation
- AI Suggestive Action panel

**Data Components:**
- Daily tee time summary
- Revenue metrics
- Roster activity
- Inventory alerts
- Weather forecast
- Upcoming events

**AI Suggestive Action Modal:**
- Quick Add (add golfer to schedule)
- Quick Food/Drink (process F&B orders)
- Quick Message (templated communications)
- Quick Check Out (process pro shop sales)
- Quick Clock-In/Out (employee time tracking)
- Help (contextual assistance)

### Calendar/Tee Sheet

**Vision:**
The Calendar/Tee Sheet is the core scheduling tool for golf course operations. It provides a visual grid of all available tee times and their booking status. Course managers can set up time slots based on their course's specific needs and capacity.

This module balances course utilization with player experience. The dynamic pricing feature helps courses maximize revenue during peak times while filling slots during off-peak hours. Weather integration helps staff plan for weather impacts.

**Scaling Approach:**
- Data is partitioned by date for faster queries
- Pagination handles large date ranges
- Tee times are stored with denormalized course data for read optimization
- Dynamic pricing calculations run as background jobs 
- Booking rules are configurable at course level
- Weather data is cached and shared across services

**Key Features:**
- Daily/weekly/monthly calendar views
- Configurable tee time spacing (7-15 minute intervals)
- Time-based pricing options
- Dynamic pricing capability
- Configurable booking window
- Weather integration
- Availability Selection Outlook (1-10 weeks via dropdown)
- Holiday settings for Course Closure Days (multi-dropdown with US Holidays)
- Tee Time Style configuration (Standard, Shotgun, Crossover) with radio buttons

**Data Model:**
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  time: Date,
  duration: Number,
  price: Number,
  dynamicPricing: Boolean,
  memberPrice: Number,
  maxPlayers: Number,
  players: [ObjectId], // References to Golfer
  status: String, // 'booked', 'available', 'completed', 'cancelled'
  weatherForecast: {
    temperature: Number,
    condition: String,
    icon: String
  },
  revenue: Number,
  specialRequests: [String],
  createdBy: ObjectId,
  createdAt: Date,
  updatedAt: Date
}
```

### Roster

**Vision:**
The Roster module helps golf courses build and maintain relationships with golfers. It serves as a CRM for tracking player information, preferences, and history. Courses can use this data to create personalized experiences and targeted marketing.

The star rating system shows staff which players are VIPs, either through frequent play or membership status. This helps staff provide the right level of service to different players.

**Scaling Approach:**
- Search uses MongoDB text indexes for fast retrieval
- Frequently accessed profiles are cached
- Player history uses time-based data partitioning
- Bulk operations for member imports and updates
- Background job processing for star rating calculations
- Sharding strategy based on geographic regions

**Key Features:**
- List view of all golfers
- Filters for recency, membership, alphabetical
- Search functionality
- Detailed golfer profiles
- Star rating system based on frequency and satisfaction

**Data Model:**
```javascript
{
  id: ObjectId,
  firstName: String,
  lastName: String,
  phoneNumber: String,
  email: String,
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String
  },
  lastPlayDate: Date,
  upcomingPlayDate: Date,
  membershipStatus: Boolean,
  membershipType: String,
  membershipId: String,
  satisfactionScore: Number,
  starRating: Number, // 1-3 crowns
  hasCardOnFile: Boolean,
  isStudent: Boolean,
  professorId: ObjectId, // Reference to Coach
  totalRounds: Number,
  averageScore: Number,
  createdAt: Date,
  updatedAt: Date
}
```

### Pro Shop

**Vision:**
The Pro Shop module turns physical retail operations into a digital inventory management system. It helps staff track stock levels, process sales, and analyze product performance. It connects with the mobile app to allow golfers to purchase items before their round.

The category-based organization mirrors physical store layout. This makes the system intuitive for staff used to the physical shop organization.

**Scaling Approach:**
- Product catalog uses MongoDB aggregation for category navigation
- Inventory updates use atomic operations to prevent conflicts
- Images stored in S3 with CloudFront CDN for fast delivery
- Bulk import/export tools for seasonal inventory changes
- Caching strategy for frequently viewed products
- Real-time stock updates with websocket notifications

**Key Features:**
- Category-based product organization
- Inventory tracking
- Integration with Quick Check Out
- Product variants (size, color, etc.)
- Pricing and discounts
- Golf ball sales integration for mobile/web booking (3-5 options based on Pro Shop inventory)

**Data Model:**
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  name: String,
  sku: String,
  image: String,
  category: String,
  subcategory: String,
  brand: String,
  cost: Number,
  price: Number,
  membershipDiscount: Number,
  quantity: Number,
  attributes: {
    sizes: [String],
    colors: [String],
    hand: String, // Left/Right
    loft: Number,
    flex: String,
    shaft: String
  },
  availableInMobileApp: Boolean,
  specifications: Object,
  createdAt: Date,
  updatedAt: Date
}
```

### 19th Hole (F&B)

**Vision:**
The 19th Hole module manages food and beverage operations. It helps staff track menu items, process orders, and analyze sales patterns. The mobile integration lets golfers order food and drinks while on the course, with delivery to their current hole.

This system balances kitchen needs with customer wants. The customization options make it easy to handle special requests while the preparation time helps kitchen staff manage workflow.

**Scaling Approach:**
- Menu categories use MongoDB aggregation for navigation
- Order processing uses queue-based architecture
- Real-time order notifications with WebSockets
- Kitchen display system with priority queue
- Order history partitioned by date
- Inventory tracking integrated with order fulfillment
- Cached menu items with invalidation on updates

**Key Features:**
- Category-based menu organization
- Customizable food options
- Integration with Quick Food/Drink
- Pricing and discounts
- Mobile ordering capability

**Data Model:**
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  name: String,
  image: String,
  category: String,
  price: Number,
  membershipDiscount: Number,
  description: String,
  ingredients: [String],
  allergens: [String],
  nutritionalInfo: {
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number
  },
  customizations: [{
    name: String,
    options: [{
      label: String,
      price: Number,
      default: Boolean
    }]
  }],
  availableInMobileApp: Boolean,
  preparationTime: Number,
  available: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Events & Tournaments

**Vision:**
The Events & Tournaments module helps courses plan and manage special golf events. These range from small member tournaments to large charity events. The system handles all aspects from creation to registration to execution.

This module helps courses generate extra revenue and build community. The microsite generation feature creates branded landing pages for each event that can be shared across marketing channels.

**Scaling Approach:**
- Event data includes denormalized information for read optimization
- Registrations processed with queue-based system
- Microsites generated and cached in S3/CloudFront
- Registration spikes handled with auto-scaling queue workers
- Concurrent registration conflicts prevented with optimistic locking
- Analytics processed as background jobs

**Key Features:**
- Event creation and management
- Registration tracking
- Capacity visualization
- Multi-channel promotion
- Microsite generation

**Data Model:**
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  name: String,
  startDate: Date,
  endDate: Date,
  registrationDeadline: Date,
  description: String,
  capacity: Number,
  registeredCount: Number,
  price: Number,
  memberPrice: Number,
  registrants: [ObjectId], // References to Golfer
  promotionChannels: [String], // 'website', 'email', 'mobile', 'social'
  micrositeUrl: String,
  createdBy: ObjectId,
  createdAt: Date,
  updatedAt: Date
}
```

### Analytics/Insights

**Vision:**
The Analytics module turns raw data into useful business insights. Course managers can track key metrics, visualize trends, and make data-driven decisions. The system allows flexible analysis across all aspects of the golf operation.

The reports created here help courses understand their business better. Is the pro shop inventory balanced with demand? Which tee times are most popular? What percentage of golfers return within 30 days?

**Scaling Approach:**
- Complex queries run on read replicas to avoid impacting operational database
- Pre-aggregated data stored for common metrics
- Report generation occurs as background jobs
- Data warehouse integration for long-term analysis
- Query caching with time-based invalidation
- Processing-intensive queries use MapReduce
- Large exports handled by background workers

**Key Features:**
- Customizable dashboard
- Visual data representations (charts, graphs)
- Report builder
- Export capabilities
- Scheduled reports

**GraphQL Implementation:**
```graphql
type AnalyticsQuery {
  timeRange: TimeRangeInput!
  dimensions: [String!]
  metrics: [String!]!
  filters: [FilterInput]
  sort: [SortInput]
  limit: Int
}

type TimeSeriesData {
  timestamps: [DateTime!]!
  series: [DataSeries!]!
}

type DataSeries {
  name: String!
  values: [Float!]!
}

type AnalyticsResult {
  timeSeriesData: TimeSeriesData
  tableData: [[String]]
  summaryMetrics: [MetricValue!]
}
```

### Back Office

**Vision:**
The Back Office module handles the behind-the-scenes operations of the golf course. It manages staff scheduling, time tracking, and internal communications. This system helps courses run efficiently with the right staff at the right times.

The shift management features help courses control labor costs while ensuring adequate coverage during busy periods. The marketing templates help create consistent communications across channels.

**Scaling Approach:**
- Employee data access controlled by role-based permissions
- Shift data partitioned by date range
- Clock in/out operations use atomic updates
- Payroll exports scheduled as background jobs
- Template storage uses versioning and S3 for assets
- Shift conflict detection using optimistic concurrency control
- Background workers process time aggregation for reporting

**Key Features:**
- Employee scheduling
- Time clock functionality
- Shift change requests
- Payroll export
- Marketing template management
- Multi-channel communications (Email/SMS/In-app) with campaign analytics
- Accounting features (Account Reconciliation, P&L Management, Financial export, OPEX Data, Financial Forecasting, GopherAI suggestions)
- Promotional setup (Course-specific promotions with non-stackable rules, targeting options, and campaign analytics)

**Data Models:**
```typescript
// User and Course models with TypeScript decorators

import { prop as Property, getModelForClass, modelOptions, Ref } from '@typegoose/typegoose';
import { Field, ObjectType, ID, registerEnumType } from 'type-graphql';
import { Types } from 'mongoose';
import { BaseModel } from '../core/BaseModel';

// Role enum to ensure type safety
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  STAFF = 'staff',
  FRONT_DESK = 'frontDesk',
  PRO_SHOP = 'proShop',
  RESTAURANT = 'restaurant'
}

// Register enum with GraphQL
registerEnumType(UserRole, {
  name: 'UserRole',
  description: 'Roles available for system users',
});

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

registerEnumType(UserStatus, {
  name: 'UserStatus',
  description: 'Current status of the user',
});

@ObjectType({ description: 'User model for system users' })
export class User extends BaseModel {
  @Field()
  @Property({ required: true, unique: true })
  email: string;

  @Field()
  @Property({ required: true })
  firstName: string;

  @Field()
  @Property({ required: true })
  lastName: string;

  @Field(() => UserRole)
  @Property({
    required: true,
    type: String,
    enum: UserRole,
    default: UserRole.STAFF
  })
  role: UserRole;

  @Field(() => [String])
  @Property({ type: () => [String] })
  permissions: string[];

  @Field(() => [ID])
  @Property({ 
    type: () => [Types.ObjectId],
    ref: 'Course',
    default: []
  })
  courses: Ref<Course>[];

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', type: Types.ObjectId })
  corporateId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', type: Types.ObjectId })
  regionId?: Ref<any>;

  @Field({ nullable: true })
  @Property()
  lastLogin?: Date;

  @Field(() => UserStatus)
  @Property({
    required: true,
    type: String,
    enum: UserStatus,
    default: UserStatus.ACTIVE
  })
  status: UserStatus;

  // Password field - not exposed in GraphQL
  @Property({ required: true })
  password: string;
}

// Models for Course entity
@ObjectType()
export class Coordinates {
  @Field()
  @Property({ required: true })
  latitude: number;

  @Field()
  @Property({ required: true })
  longitude: number;
}

@ObjectType()
export class CourseLocation {
  @Field()
  @Property({ required: true })
  address: string;

  @Field()
  @Property({ required: true })
  city: string;

  @Field()
  @Property({ required: true })
  state: string;

  @Field()
  @Property({ required: true })
  country: string;

  @Field()
  @Property({ required: true })
  timezone: string;

  @Field(() => Coordinates, { nullable: true })
  @Property({ type: () => Coordinates })
  coordinates?: Coordinates;
}

@ObjectType()
export class CourseContact {
  @Field()
  @Property({ required: true })
  phone: string;

  @Field()
  @Property({ required: true })
  email: string;

  @Field({ nullable: true })
  @Property()
  website?: string;
}

@ObjectType()
export class HoursOfOperation {
  @Field()
  @Property()
  open: string;

  @Field()
  @Property()
  close: string;
}

@ObjectType()
export class OperatingHours {
  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  monday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  tuesday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  wednesday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  thursday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  friday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  saturday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  sunday?: HoursOfOperation;
}

@ObjectType()
export class CourseSettings {
  @Field()
  @Property({ required: true, default: 10 })
  teeTimeInterval: number;

  @Field()
  @Property({ required: true, default: 4 })
  maxPlayersPerSlot: number;

  @Field({ nullable: true })
  @Property()
  cancellationPolicy?: string;

  @Field({ nullable: true })
  @Property()
  weatherPolicy?: string;

  @Field({ nullable: true })
  @Property({ default: false })
  isPrivate?: boolean;

  @Field({ nullable: true })
  @Property({ default: false })
  requiresMembership?: boolean;
}

@ObjectType({ description: 'Golf course entity' })
export class Course extends BaseModel {
  @Field()
  @Property({ required: true })
  name: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', type: Types.ObjectId })
  corporateId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', type: Types.ObjectId })
  regionId?: Ref<any>;

  @Field(() => CourseLocation)
  @Property({ type: () => CourseLocation, required: true })
  location: CourseLocation;

  @Field(() => CourseContact)
  @Property({ type: () => CourseContact, required: true })
  contact: CourseContact;

  @Field(() => OperatingHours, { nullable: true })
  @Property({ type: () => OperatingHours })
  operatingHours?: OperatingHours;

  @Field(() => [String])
  @Property({ type: () => [String], default: [] })
  features: string[];

  @Field(() => CourseSettings)
  @Property({ type: () => CourseSettings, required: true })
  settings: CourseSettings;
}

// Generate Mongoose models
export const UserModel = getModelForClass(User);
export const CourseModel = getModelForClass(Course);
```

### Settings

**Vision:**
The Settings module handles system configuration and user management. It's the control center for setting up how the system works for each course. For multi-course operations, it manages the hierarchy and relationships between courses.

This module is crucial for system administrators and IT staff. It provides tools for troubleshooting, audit trails, and integration management.

**Scaling Approach:**
- Configuration stored in MongoDB with caching layer
- User and permission data cached with fast invalidation
- Audit logs stored in separate collection with TTL indexes
- Integration credentials stored in secure key management
- Background jobs handle integration syncing
- Config changes propagate through pub/sub mechanism
- Sharding strategy based on corporate hierarchy

**Key Features:**
- User and role management
- Course information
- Corporate hierarchy (for multi-location)
- Billing/plan information
- Audit trail
- Integrations management

**Data Models:**
```typescript
// User and Course models with TypeScript decorators

import { prop as Property, getModelForClass, modelOptions, Ref } from '@typegoose/typegoose';
import { Field, ObjectType, ID, registerEnumType } from 'type-graphql';
import { Types } from 'mongoose';
import { BaseModel } from '../core/BaseModel';

// Role enum to ensure type safety
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  STAFF = 'staff',
  FRONT_DESK = 'frontDesk',
  PRO_SHOP = 'proShop',
  RESTAURANT = 'restaurant'
}

// Register enum with GraphQL
registerEnumType(UserRole, {
  name: 'UserRole',
  description: 'Roles available for system users',
});

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

registerEnumType(UserStatus, {
  name: 'UserStatus',
  description: 'Current status of the user',
});

@ObjectType({ description: 'User model for system users' })
export class User extends BaseModel {
  @Field()
  @Property({ required: true, unique: true })
  email: string;

  @Field()
  @Property({ required: true })
  firstName: string;

  @Field()
  @Property({ required: true })
  lastName: string;

  @Field(() => UserRole)
  @Property({
    required: true,
    type: String,
    enum: UserRole,
    default: UserRole.STAFF
  })
  role: UserRole;

  @Field(() => [String])
  @Property({ type: () => [String] })
  permissions: string[];

  @Field(() => [ID])
  @Property({ 
    type: () => [Types.ObjectId],
    ref: 'Course',
    default: []
  })
  courses: Ref<Course>[];

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', type: Types.ObjectId })
  corporateId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', type: Types.ObjectId })
  regionId?: Ref<any>;

  @Field({ nullable: true })
  @Property()
  lastLogin?: Date;

  @Field(() => UserStatus)
  @Property({
    required: true,
    type: String,
    enum: UserStatus,
    default: UserStatus.ACTIVE
  })
  status: UserStatus;

  // Password field - not exposed in GraphQL
  @Property({ required: true })
  password: string;
}

// Models for Course entity
@ObjectType()
export class Coordinates {
  @Field()
  @Property({ required: true })
  latitude: number;

  @Field()
  @Property({ required: true })
  longitude: number;
}

@ObjectType()
export class CourseLocation {
  @Field()
  @Property({ required: true })
  address: string;

  @Field()
  @Property({ required: true })
  city: string;

  @Field()
  @Property({ required: true })
  state: string;

  @Field()
  @Property({ required: true })
  country: string;

  @Field()
  @Property({ required: true })
  timezone: string;

  @Field(() => Coordinates, { nullable: true })
  @Property({ type: () => Coordinates })
  coordinates?: Coordinates;
}

@ObjectType()
export class CourseContact {
  @Field()
  @Property({ required: true })
  phone: string;

  @Field()
  @Property({ required: true })
  email: string;

  @Field({ nullable: true })
  @Property()
  website?: string;
}

@ObjectType()
export class HoursOfOperation {
  @Field()
  @Property()
  open: string;

  @Field()
  @Property()
  close: string;
}

@ObjectType()
export class OperatingHours {
  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  monday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  tuesday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  wednesday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  thursday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  friday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  saturday?: HoursOfOperation;

  @Field(() => HoursOfOperation, { nullable: true })
  @Property({ type: () => HoursOfOperation })
  sunday?: HoursOfOperation;
}

@ObjectType()
export class CourseSettings {
  @Field()
  @Property({ required: true, default: 10 })
  teeTimeInterval: number;

  @Field()
  @Property({ required: true, default: 4 })
  maxPlayersPerSlot: number;

  @Field({ nullable: true })
  @Property()
  cancellationPolicy?: string;

  @Field({ nullable: true })
  @Property()
  weatherPolicy?: string;

  @Field({ nullable: true })
  @Property({ default: false })
  isPrivate?: boolean;

  @Field({ nullable: true })
  @Property({ default: false })
  requiresMembership?: boolean;
}

@ObjectType({ description: 'Golf course entity' })
export class Course extends BaseModel {
  @Field()
  @Property({ required: true })
  name: string;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Corporate', type: Types.ObjectId })
  corporateId?: Ref<any>;

  @Field(() => ID, { nullable: true })
  @Property({ ref: 'Region', type: Types.ObjectId })
  regionId?: Ref<any>;

  @Field(() => CourseLocation)
  @Property({ type: () => CourseLocation, required: true })
  location: CourseLocation;

  @Field(() => CourseContact)
  @Property({ type: () => CourseContact, required: true })
  contact: CourseContact;

  @Field(() => OperatingHours, { nullable: true })
  @Property({ type: () => OperatingHours })
  operatingHours?: OperatingHours;

  @Field(() => [String])
  @Property({ type: () => [String], default: [] })
  features: string[];

  @Field(() => CourseSettings)
  @Property({ type: () => CourseSettings, required: true })
  settings: CourseSettings;
}

// Generate Mongoose models
export const UserModel = getModelForClass(User);
export const CourseModel = getModelForClass(Course);
```

## Mobile App Features

### Home Screen

**Vision:**
The Home Screen is the entry point to the mobile experience. It provides a personalized view for each golfer based on their play history and preferences. The focus is on making it easy to book a tee time and stay updated on golf news and events.

This screen uses AI to recommend tee times that match the golfer's typical play patterns. This helps courses fill available slots while giving golfers convenient options.

**Scaling Approach:**
- Personalized content delivery using caching
- Recommendation engine runs as background process
- Content feeds stored in CDN with edge caching
- Notifications delivered through push service
- User preferences drive content prioritization
- Home screen components load asynchronously

**Key Features:**
- Organized layout from top to bottom:
  - My Club section (Membership or Favorite course oriented)
  - Upcoming Tee Time with check-in and In-Play Mode access
  - News section with PGA/LIV tournament updates, scorecard, and voting for winners (earn Albatross Points)
  - Tournament section with geolocation-based listings (within 30 miles) for enrollment
- Private Club features in My Club:
  - Book Tee Time
  - Book Tennis Court
  - Reserve a Table
  - Buy a Day Pass
  - Club Events, Dinners, Tournaments, and News
- Personalized tee time recommendations
- Navigation to other modules

**GraphQL API:**
```graphql
type Query {
  recommendedTeeTimes(courseId: ID, date: Date): [TeeTime!]!
  golfNews(limit: Int): [NewsItem!]!
  upcomingEvents(limit: Int): [Event!]!
  upcomingTeeTimes(limit: Int): [TeeTime!]!
}
```

### Book a Tee Time

**Vision:**
The Book a Tee Time feature makes it easy for golfers to schedule play sessions. It shows available times based on course capacity and the golfer's membership status. The booking flow is streamlined to minimize steps while collecting necessary information.

This feature connects directly to the course's Calendar/Tee Sheet system. Changes made here appear immediately for course staff, ensuring everyone has the same information.

**Scaling Approach:**
- Search and availability checks optimized with indexes
- Booking process uses transaction guarantees
- Concurrent booking conflicts prevented with optimistic locking
- Course data cached with golf-specific geospatial index
- Membership validation against central database
- Payment processing through queue-based architecture
- Real-time availability updates via WebSockets

**Key Features:**
- Course search and filtering
- Tee time availability
- Group booking
- Payment processing
- Membership integration
- Split payment functionality (with 48-hour email/SMS/in-app payment notifications)

**Data Flow:**
- Location-based course search
- Membership validation
- Date/time selection
- Player selection
- Additional services
- Confirmation and payment

### In-Play Mode

**Vision:**
The In-Play Mode transforms the mobile app into a digital caddie during a round of golf. It provides a digital scorecard, tracks player positions on the course, and enables on-course services like food ordering.

This feature enhances the golfer experience while on the course. The feedback collection helps courses improve their service based on real-time player input.

**Scaling Approach:**
- GPS data processing optimized for battery usage
- Score data uses offline-first approach with sync
- Orders processed through queue system
- Real-time position updates use WebSockets
- Scorecard data cached on device with background sync
- Feedback processing through dedicated microservice
- Course map data pre-loaded and cached

**Key Features:**
- Digital scorecard
- Player management
- Food/beverage ordering
- Course experience feedback
- GPS tracking
- Game Mode (Scramble, Skins, Wolf, Closest to the Hole with wager and payment request capabilities)
- Student Mode (capture detailed play data by hole for sharing with coaches)
- Player addition (search or QR code for adding players to the group leaderboard)

**Data Model:**
```javascript
// Round
{
  id: ObjectId,
  teeTimeId: ObjectId,
  courseId: ObjectId,
  date: Date,
  players: [ObjectId], // References to Golfer
  scores: [{
    playerId: ObjectId,
    holeScores: [Number], // Array of 18 scores
    totalScore: Number
  }],
  weatherConditions: String,
  feedback: [{
    question: String,
    rating: Number,
    hole: Number
  }],
  orders: [ObjectId], // References to F&B orders
  status: String, // 'active', 'completed', 'cancelled'
  createdAt: Date,
  updatedAt: Date
}
```

### Marketplace

**Vision:**
The Marketplace connects golfers with products they need for their game. It shows recommended items based on upcoming play and course conditions. For example, rain gear before a wet round or sunscreen for hot days.

This feature extends the Pro Shop to the digital space. It helps courses sell inventory while providing convenience to golfers.

**Scaling Approach:**
- Product catalog uses MongoDB aggregation for category navigation
- Recommendation engine runs as background process
- Images stored in S3 with CloudFront CDN
- Weather-based suggestions processed by dedicated service
- Product data cached with invalidation on inventory changes
- Order processing through queue system
- Payment processing with retries and fallbacks

**Key Features:**
- Context-aware product recommendations
- Pro shop inventory integration
- Category-based browsing
- Purchase flow
- Weather-based suggestions

**GraphQL API:**
```graphql
type Query {
  recommendedProducts(teeTimeId: ID, weatherForecast: WeatherInput): [Product!]!
  marketplaceCategories: [Category!]!
  productsByCategory(categoryId: ID!, filter: ProductFilter): [Product!]!
}
```

### Golfer Profile

**Vision:**
The Golfer Profile stores a player's personal information, play history, and equipment details. It helps golfers track their game and manage their account settings. The profile data helps courses deliver personalized experiences and better understand their golfers.

**Scaling Approach:**
- Profile data cached for fast access
- Profile photos stored in S3 with thumbnail generation
- Play history uses time-series optimization
- Equipment data used for product recommendations

**Key Features:**
- Profile Management (Name/Phone/Email/Zip Code)
- Memberships (private/country club and public course verification)
- Round History (stored after completing tracked rounds)
- Payment & Subscriptions (payment methods, subscription tiers: Free/Amateur/Pro)
- Preferences (Waitlist, Favorite Course, Preferred Play Time/Days)
- Notifications (SMS/Email/In-App with at least one required)

**Data Model:**
```javascript
// Golfer Equipment
{
  id: ObjectId,
  golferId: ObjectId,
  clubs: [{
    type: String, // 'driver', 'iron', 'putter', etc.
    brand: String,
    model: String,
    yearReleased: Number
  }],
  balls: String,
  otherEquipment: [String],
  updatedAt: Date
}
```

### Rewards

**Vision:**
The Rewards program builds golfer loyalty through incentives and benefits. It tracks points earned from play, purchases, and special activities like tournament predictions. This gamification element encourages return visits and increases engagement with the platform.

**Scaling Approach:**
- Rewards calculations run as background jobs
- Transaction history partitioned by date
- Point redemption uses optimistic locking

**Key Features:**
- Points tracking and accumulation
- Tier-based benefits (Bronze, Silver, Gold)
- Points history and transaction details
- Reward redemption options
- Special points-earning activities
- Non-stackable with course-specific promotions

**Data Model:**
```javascript
// Rewards
{
  id: ObjectId,
  golferId: ObjectId,
  totalPoints: Number,
  tier: String, // 'bronze', 'silver', 'gold'
  pointsToNextTier: Number,
  transactions: [{
    date: Date,
    activity: String, 
    points: Number,
    balance: Number
  }],
  updatedAt: Date
}
```

## Coach Platform Features

### Calendar

**Vision:**
The Calendar helps golf instructors manage their teaching schedule. It shows available time slots, booked lessons, and student information. The system makes it easy for coaches to set their availability and for students to book lessons.

This feature connects directly with the Mobile App's coach booking system. This ensures coaches and students always see the same schedule information.

**Scaling Approach:**
- Lesson data includes denormalized coach information
- Availability checks optimized with indexes
- Booking process uses transaction guarantees
- Calendar data cached with time-based invalidation
- Recurring lessons use pattern-based storage
- Conflict detection with optimistic concurrency
- Background jobs handle reminder notifications

**Key Features:**
- Lesson scheduling
- Availability management
- Student booking
- Custom lesson durations

**Data Model:**
```javascript
// Lesson
{
  id: ObjectId,
  coachId: ObjectId,
  studentIds: [ObjectId], // References to Golfer
  date: Date,
  startTime: String,
  endTime: String,
  duration: Number, // in minutes
  lessonType: String, // 'individual', 'group', 'clinic'
  notes: String,
  location: String,
  status: String, // 'scheduled', 'completed', 'cancelled'
  paymentStatus: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Messenger

**Vision:**
The Messenger provides direct communication between coaches and students. It replaces personal phone numbers with an in-app messaging system. This keeps all golf-related communication in one place and protects privacy.

The system uses Twilio for SMS delivery, ensuring reliable message delivery even when students don't have the app open.

**Scaling Approach:**
- Twilio integration for reliable message delivery
- Conversation history partitioned by participants
- Message status tracking with delivery guarantees
- Media attachments stored in S3
- Background jobs handle message processing
- Real-time updates via WebSockets
- Conversation search uses text indexes

**Key Features:**
- Two-way SMS communication via Twilio
- Message history
- Template messages
- Media sharing

**Implementation:**
- Integration with Twilio API
- Conversation threading
- Message status tracking

### Payment

**Vision:**
The Payment module helps coaches collect and track lesson payments. It provides a simple checkout process that coaches can use after completing a lesson. The student verification ensures the right person is being charged.

This system streamlines the financial side of coaching. It helps coaches maintain professional billing practices and track their teaching revenue.

**Scaling Approach:**
- Payment processing through queue system
- Transaction records stored with idempotency guarantees
- Receipt generation handled by background workers
- Student verification with secure token system
- Payment history partitioned by date
- Financial data backup with additional safeguards
- Integration with third-party payment processors

**Key Features:**
- Lesson payment processing
- Service categorization
- Student check-in verification
- Receipt generation

**Data Model:**
```javascript
// Payment
{
  id: ObjectId,
  coachId: ObjectId,
  studentId: ObjectId,
  lessonId: ObjectId,
  amount: Number,
  serviceType: String,
  paymentMethod: String,
  transactionId: String,
  status: String,
  receiptUrl: String,
  createdAt: Date
}
```

### Academies & Clinics

**Vision:**
The Academies & Clinics module helps coaches run group teaching programs. It handles student enrollment, scheduling, and payment collection for multi-session teaching programs. This feature helps coaches scale their teaching beyond one-on-one lessons.

The capacity tracking helps coaches maximize their teaching time while maintaining quality. The group format makes golf instruction more affordable for students.

**Scaling Approach:**
- Clinic data includes denormalized information
- Enrollment process with capacity guarantees
- Student list with pagination for large groups
- Schedule pattern storage for recurring sessions
- Payment tracking with batch processing options
- Notifications handled by background workers
- Analytics processed as background jobs

**Key Features:**
- Group lesson management
- Student enrollment
- Capacity tracking
- Payment collection

**Data Model:**
```javascript
// Clinic
{
  id: ObjectId,
  coachId: ObjectId,
  name: String,
  description: String,
  startDate: Date,
  endDate: Date,
  schedule: [{
    day: String,
    startTime: String,
    endTime: String
  }],
  capacity: Number,
  enrolledCount: Number,
  price: Number,
  location: String,
  students: [ObjectId], // References to Golfer
  createdAt: Date,
  updatedAt: Date
}
```

## GraphQL and Express Integration

### Apollo Server and Express Relationship

The Albatros CRM API uses both Express and Apollo Server:

1. **Express Responsibilities**:
   - HTTP server management
   - Middleware processing (auth, logging, etc.)
   - Non-GraphQL endpoints (health checks, webhooks)
   - Error handling at the HTTP level

2. **Apollo Server Responsibilities**:
   - GraphQL schema definition and composition
   - Query parsing and validation
   - Resolver execution
   - GraphQL-specific optimizations
   - GraphQL Playground (development environment)

### Benefits of This Approach

- Uses existing Express ecosystem
- Allows mixed API approaches (GraphQL + REST where needed)
- Simplifies authentication with standard Express middleware
- Enables gradual migration from REST to GraphQL

## Module Structure

Each microservice has its own repository with this structure:

```
microservice-name/
├── src/
│   ├── config/             # Configuration files
│   ├── core/               # Core utilities
│   ├── graphql/            # GraphQL schema and resolvers
│   │   ├── schema.js       # Generated GraphQL type definitions 
│   │   ├── resolvers.js    # Resolvers for this module
│   │   └── directives.js   # Custom directives
│   ├── models/             # Database models
│   ├── services/           # Business logic services
│   ├── validation/         # Input validation
│   ├── utils/              # Utility functions
│   └── server.js           # Entry point
└── tests/                  # Module tests
    ├── unit/               # Unit tests
    └── integration/        # Integration tests
```

## Data Models and Schema Management

### Mongoose as Single Source of Truth

The system uses Mongoose models as the single source of truth. GraphQL schemas are automatically generated from these models.

1. **Schema Generation Approach**:
   - Define Mongoose schemas as the authoritative data model
   - Use `graphql-compose-mongoose` to generate GraphQL types
   - Extend generated GraphQL types with computed fields and custom operations

2. **Implementation Strategy**:
   - Put all Mongoose models in the `models/` directory of each microservice
   - Generate base GraphQL types in each module's schema file
   - Add custom fields and resolvers to extend the base types

3. **Schema Generation Workflow**:
   - Mongoose schema definition -> Type generation -> GraphQL schema composition

### Benefits of This Approach

- **Single Source of Truth**: Maintain only Mongoose schemas
- **DRY (Don't Repeat Yourself)**: No duplication between API and database schemas
- **Automatic Updates**: Changes to Mongoose models reflect in GraphQL
- **Extensibility**: Add custom fields and resolvers to generated types
- **Generated CRUD**: Auto-generate basic operations
- **Type Safety**: GraphQL types match Mongoose schema types

## Base Model Class and GraphQL API Structure

### Base Model Class

The system uses TypeScript decorators to create a single source of truth for both Mongoose models and GraphQL schemas. This approach leverages TypeGraphQL and TypeGoose to define models once and generate both database schemas and API types.

```typescript
// Base Model Class using TypeScript decorators
import { prop as Property, getModelForClass, modelOptions, Severity } from '@typegoose/typegoose';
import { Field, ObjectType, ID } from 'type-graphql';
import { Types } from 'mongoose';

@ObjectType({ description: 'Base model with common fields for all entities' })
@modelOptions({
  schemaOptions: {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  },
  options: {
    allowMixed: Severity.ALLOW
  }
})
export abstract class BaseModel {
  @Field(() => ID)
  @Property({ default: () => new Types.ObjectId() })
  _id: Types.ObjectId;

  @Field()
  id: string;

  @Field()
  @Property()
  createdAt: Date;

  @Field()
  @Property()
  updatedAt: Date;
}

// Example of a model extending the base model
@ObjectType({ description: 'Tee time booking model' })
export class TeeTime extends BaseModel {
  @Field()
  @Property({ required: true, ref: 'Course' })
  courseId: Types.ObjectId;

  @Field()
  @Property({ required: true })
  time: Date;

  @Field()
  @Property({ required: true })
  duration: number;

  @Field()
  @Property({ required: true })
  price: number;

  @Field({ nullable: true })
  @Property()
  dynamicPricing?: boolean;

  @Field({ nullable: true })
  @Property()
  memberPrice?: number;

  @Field()
  @Property({ required: true })
  maxPlayers: number;

  @Field(() => [Types.ObjectId])
  @Property({ type: () => [Types.ObjectId], ref: 'Golfer' })
  players: Types.ObjectId[];

  @Field()
  @Property({ 
    required: true, 
    enum: ['booked', 'available', 'completed', 'cancelled'],
    default: 'available' 
  })
  status: string;

  @Field(() => WeatherForecast, { nullable: true })
  @Property({ type: () => WeatherForecast })
  weatherForecast?: WeatherForecast;

  @Field({ nullable: true })
  @Property()
  revenue?: number;

  @Field(() => [String], { nullable: true })
  @Property({ type: () => [String] })
  specialRequests?: string[];

  @Field(() => Types.ObjectId, { nullable: true })
  @Property({ ref: 'User' })
  createdBy?: Types.ObjectId;
}

@ObjectType()
export class WeatherForecast {
  @Field({ nullable: true })
  @Property()
  temperature?: number;

  @Field({ nullable: true })
  @Property()
  condition?: string;

  @Field({ nullable: true })
  @Property()
  icon?: string;
}

// Creating the Mongoose model
export const TeeTimeModel = getModelForClass(TeeTime);

// Example of generating CRUD resolvers with TypeGraphQL
@Resolver(() => TeeTime)
export class TeeTimeResolver {
  @Query(() => TeeTime, { nullable: true })
  async teeTime(@Arg('id') id: string) {
    return TeeTimeModel.findById(id);
  }

  @Query(() => [TeeTime])
  async teeTimes(
    @Arg('limit', { nullable: true }) limit: number,
    @Arg('offset', { nullable: true }) offset: number,
    @Arg('filter', { nullable: true }) filter: TeeTimeFilter
  ) {
    const query = filter ? this.buildFilterQuery(filter) : {};
    return TeeTimeModel.find(query)
      .limit(limit || 10)
      .skip(offset || 0);
  }

  @Mutation(() => TeeTime)
  async createTeeTime(@Arg('input') input: TeeTimeInput) {
    const teeTime = new TeeTimeModel(input);
    return teeTime.save();
  }

  @Mutation(() => TeeTime, { nullable: true })
  async updateTeeTime(
    @Arg('id') id: string,
    @Arg('input') input: TeeTimeInput
  ) {
    return TeeTimeModel.findByIdAndUpdate(id, input, { new: true });
  }

  @Mutation(() => Boolean)
  async deleteTeeTime(@Arg('id') id: string) {
    const result = await TeeTimeModel.deleteOne({ _id: id });
    return result.deletedCount === 1;
  }

  private buildFilterQuery(filter: TeeTimeFilter): any {
    // Implementation for building MongoDB query from filter object
    const query: any = {};
    if (filter.courseId) query.courseId = filter.courseId;
    if (filter.status) query.status = filter.status;
    if (filter.dateFrom && filter.dateTo) {
      query.time = { $gte: filter.dateFrom, $lte: filter.dateTo };
    } else if (filter.dateFrom) {
      query.time = { $gte: filter.dateFrom };
    } else if (filter.dateTo) {
      query.time = { $lte: filter.dateTo };
    }
    return query;
  }
}

// Input types for mutations and queries
@InputType()
export class TeeTimeInput implements Partial<TeeTime> {
  @Field()
  courseId: Types.ObjectId;

  @Field()
  time: Date;

  @Field()
  duration: number;

  @Field()
  price: number;

  @Field({ nullable: true })
  dynamicPricing?: boolean;

  @Field({ nullable: true })
  memberPrice?: number;

  @Field()
  maxPlayers: number;

  @Field(() => [Types.ObjectId], { nullable: true })
  players?: Types.ObjectId[];

  @Field({ nullable: true })
  status?: string;

  @Field(() => WeatherForecastInput, { nullable: true })
  weatherForecast?: WeatherForecastInput;

  @Field(() => [String], { nullable: true })
  specialRequests?: string[];
}

@InputType()
export class WeatherForecastInput {
  @Field({ nullable: true })
  temperature?: number;

  @Field({ nullable: true })
  condition?: string;

  @Field({ nullable: true })
  icon?: string;
}

@InputType()
export class TeeTimeFilter {
  @Field({ nullable: true })
  courseId?: Types.ObjectId;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  dateFrom?: Date;

  @Field({ nullable: true })
  dateTo?: Date;
}
```

### GraphQL API Structure

Each module exposes its GraphQL schema through a unified API gateway. The API follows these principles:

1. **Single Source of Truth**: Models defined once using TypeScript classes with decorators
2. **Automatic Type Generation**: GraphQL types and MongoDB schemas generated from the same code
3. **Type Safety**: Full TypeScript support throughout the application
4. **Consistent Naming**: All queries and mutations follow a consistent naming pattern
5. **Pagination Support**: All list queries support pagination, filtering, and sorting
6. **Nested Resolvers**: Related data is accessible through nested resolvers
7. **Field Selection**: Clients can specify exactly which fields they need
8. **Batched Requests**: Multiple operations can be combined in a single request

## GraphQL API Endpoints by Module

### Core Services

#### Authentication/Authorization

```typescript
// Authentication models and resolvers with TypeScript decorators
import { Field, ObjectType, InputType, Resolver, Query, Mutation, Arg, Ctx, UseMiddleware } from 'type-graphql';
import { IsEmail, MinLength } from 'class-validator';
import { sign, verify } from 'jsonwebtoken';
import { hash, compare } from 'bcrypt';
import { User, UserModel, UserRole, UserStatus } from '../models/User';
import { AuthMiddleware } from '../middleware/AuthMiddleware';
import { Context } from '../types/Context';

@ObjectType()
export class AuthPayload {
  @Field()
  accessToken: string;

  @Field()
  refreshToken: string;

  @Field(() => User)
  user: User;
}

@InputType()
export class LoginInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @MinLength(6)
  password: string;
}

@InputType()
export class RegisterInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  firstName: string;

  @Field()
  lastName: string;

  @Field()
  @MinLength(6)
  password: string;

  @Field(() => UserRole, { nullable: true })
  role?: UserRole;
}

@InputType()
export class UpdateProfileInput {
  @Field({ nullable: true })
  firstName?: string;

  @Field({ nullable: true })
  lastName?: string;

  @Field({ nullable: true })
  @IsEmail()
  email?: string;
}

@InputType()
export class UserFilterInput {
  @Field({ nullable: true })
  role?: UserRole;

  @Field({ nullable: true })
  status?: UserStatus;

  @Field({ nullable: true })
  search?: string;

  @Field({ nullable: true })
  courseId?: string;
}

@InputType()
export class UserSortInput {
  @Field({ nullable: true })
  field?: string;

  @Field({ nullable: true })
  direction?: 'asc' | 'desc';
}

@ObjectType()
export class UserPagination {
  @Field(() => [User])
  items: User[];

  @Field()
  totalCount: number;

  @Field()
  page: number;

  @Field()
  perPage: number;

  @Field()
  totalPages: number;
}

@Resolver(() => User)
export class AuthResolver {
  @Query(() => User, { nullable: true })
  @UseMiddleware(AuthMiddleware)
  async me(@Ctx() ctx: Context): Promise<User | null> {
    if (!ctx.userId) return null;
    return UserModel.findById(ctx.userId);
  }

  @Query(() => User, { nullable: true })
  @UseMiddleware(AuthMiddleware)
  async user(
    @Arg('id') id: string,
    @Ctx() ctx: Context
  ): Promise<User | null> {
    // Check if user has admin permissions
    const currentUser = await UserModel.findById(ctx.userId);
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Not authorized');
    }
    return UserModel.findById(id);
  }

  @Query(() => UserPagination)
  @UseMiddleware(AuthMiddleware)
  async users(
    @Arg('filter', { nullable: true }) filter: UserFilterInput,
    @Arg('sort', { nullable: true }) sort: UserSortInput,
    @Arg('page', { nullable: true, defaultValue: 1 }) page: number,
    @Arg('perPage', { nullable: true, defaultValue: 20 }) perPage: number,
    @Ctx() ctx: Context
  ): Promise<UserPagination> {
    // Check if user has admin permissions
    const currentUser = await UserModel.findById(ctx.userId);
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Not authorized');
    }

    // Build query from filter
    const query: any = {};
    if (filter?.role) query.role = filter.role;
    if (filter?.status) query.status = filter.status;
    if (filter?.courseId) query.courses = { $in: [filter.courseId] };
    if (filter?.search) {
      query.$or = [
        { firstName: { $regex: filter.search, $options: 'i' } },
        { lastName: { $regex: filter.search, $options: 'i' } },
        { email: { $regex: filter.search, $options: 'i' } }
      ];
    }

    // Build sort
    const sortOptions: any = {};
    if (sort?.field && sort?.direction) {
      sortOptions[sort.field] = sort.direction === 'asc' ? 1 : -1;
    } else {
      sortOptions.createdAt = -1; // Default sort
    }

    const skip = (page - 1) * perPage;
    const [items, totalCount] = await Promise.all([
      UserModel.find(query).sort(sortOptions).skip(skip).limit(perPage),
      UserModel.countDocuments(query)
    ]);

    return {
      items,
      totalCount,
      page,
      perPage,
      totalPages: Math.ceil(totalCount / perPage)
    };
  }

  @Mutation(() => AuthPayload)
  async login(
    @Arg('input') { email, password }: LoginInput
  ): Promise<AuthPayload> {
    const user = await UserModel.findOne({ email });
    if (!user) {
      throw new Error('Invalid email or password');
    }

    const isValid = await compare(password, user.password);
    if (!isValid) {
      throw new Error('Invalid email or password');
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    return {
      accessToken: this.createAccessToken(user),
      refreshToken: this.createRefreshToken(user),
      user
    };
  }

  @Mutation(() => AuthPayload)
  async refreshToken(
    @Arg('refreshToken') refreshToken: string
  ): Promise<AuthPayload> {
    try {
      const payload = verify(refreshToken, process.env.REFRESH_TOKEN_SECRET!) as any;
      const user = await UserModel.findById(payload.userId);
      
      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new Error('Invalid refresh token');
      }

      return {
        accessToken: this.createAccessToken(user),
        refreshToken: this.createRefreshToken(user),
        user
      };
    } catch (err) {
      throw new Error('Invalid refresh token');
    }
  }

  @Mutation(() => AuthPayload)
  async register(
    @Arg('input') input: RegisterInput
  ): Promise<AuthPayload> {
    const existingUser = await UserModel.findOne({ email: input.email });
    if (existingUser) {
      throw new Error('Email already in use');
    }

    const hashedPassword = await hash(input.password, 10);

    const user = new UserModel({
      email: input.email,
      firstName: input.firstName,
      lastName: input.lastName,
      password: hashedPassword,
      role: input.role || UserRole.STAFF,
      status: UserStatus.ACTIVE
    });

    await user.save();

    return {
      accessToken: this.createAccessToken(user),
      refreshToken: this.createRefreshToken(user),
      user
    };
  }

  @Mutation(() => User)
  @UseMiddleware(AuthMiddleware)
  async updateProfile(
    @Arg('input') input: UpdateProfileInput,
    @Ctx() ctx: Context
  ): Promise<User> {
    const user = await UserModel.findById(ctx.userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (input.firstName) user.firstName = input.firstName;
    if (input.lastName) user.lastName = input.lastName;
    if (input.email) {
      const existingUser = await UserModel.findOne({ email: input.email });
      if (existingUser && existingUser.id !== user.id) {
        throw new Error('Email already in use');
      }
      user.email = input.email;
    }

    await user.save();
    return user;
  }

  @Mutation(() => Boolean)
  @UseMiddleware(AuthMiddleware)
  async changePassword(
    @Arg('oldPassword') oldPassword: string,
    @Arg('newPassword') @MinLength(6) newPassword: string,
    @Ctx() ctx: Context
  ): Promise<boolean> {
    const user = await UserModel.findById(ctx.userId);
    if (!user) {
      throw new Error('User not found');
    }

    const isValid = await compare(oldPassword, user.password);
    if (!isValid) {
      throw new Error('Invalid current password');
    }

    user.password = await hash(newPassword, 10);
    await user.save();

    return true;
  }

  @Mutation(() => Boolean)
  async requestPasswordReset(
    @Arg('email') email: string
  ): Promise<boolean> {
    const user = await UserModel.findOne({ email });
    if (!user) {
      // Return true even if user doesn't exist for security
      return true;
    }

    // TODO: Generate reset token and send email
    // For now, we just return success
    return true;
  }

  @Mutation(() => Boolean)
  async resetPassword(
    @Arg('token') token: string,
    @Arg('newPassword') @MinLength(6) newPassword: string
  ): Promise<boolean> {
    // TODO: Validate token and reset password
    // For now, we just return success
    return true;
  }

  private createAccessToken(user: User): string {
    return sign(
      { userId: user.id, role: user.role },
      process.env.ACCESS_TOKEN_SECRET!,
      { expiresIn: '15m' }
    );
  }

  private createRefreshToken(user: User): string {
    return sign(
      { userId: user.id },
      process.env.REFRESH_TOKEN_SECRET!,
      { expiresIn: '7d' }
    );
  }
}
```

#### User Management

```graphql
type User {
  id: ID!
  email: String!
  firstName: String!
  lastName: String!
  role: UserRole!
  permissions: [String!]!
  courses: [Course!]!
  corporateId: ID
  regionId: ID
  lastLogin: DateTime
  status: UserStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Mutation {
  # Create user (admin only)
  createUser(input: CreateUserInput!): User
  # Update user (admin only)
  updateUser(id: ID!, input: UpdateUserInput!): User
  # Delete user (admin only)
  deleteUser(id: ID!): Boolean
  # Assign user to course
  assignUserToCourse(userId: ID!, courseId: ID!): User
  # Remove user from course
  removeUserFromCourse(userId: ID!, courseId: ID!): User
}
```

#### Course Management

```graphql
type Query {
  # Get course by ID
  course(id: ID!): Course
  # List courses with pagination
  courses(
    filter: CourseFilterInput
    sort: CourseSortInput
    page: Int = 1
    perPage: Int = 20
  ): CoursePagination
  # Get courses by corporate ID
  coursesByCorporate(corporateId: ID!): [Course!]!
  # Get courses by region ID
  coursesByRegion(regionId: ID!): [Course!]!
}

type Mutation {
  # Create course (admin only)
  createCourse(input: CreateCourseInput!): Course
  # Update course
  updateCourse(id: ID!, input: UpdateCourseInput!): Course
  # Delete course (admin only)
  deleteCourse(id: ID!): Boolean
  # Update course settings
  updateCourseSettings(id: ID!, input: CourseSettingsInput!): Course
}
```

### Dashboard Module

```graphql
type Query {
  # Get dashboard overview data
  dashboardOverview(courseId: ID!, timeframe: TimeframeInput = MONTHLY): DashboardOverview
  # Get weather data for course
  weatherData(courseId: ID!, date: Date): WeatherData
  # Get notifications
  notifications(courseId: ID!, unreadOnly: Boolean = false): [Notification!]!
  # Get AI suggestions
  aiSuggestions(courseId: ID!, context: String): [AISuggestion!]!
}

type DashboardOverview {
  revenue: MetricWithTrend!
  teeTimeCount: MetricWithTrend!
  golferCount: MetricWithTrend!
  upcomingTeeTimesSummary: UpcomingTeeTimesSummary!
  inventoryAlerts: [InventoryAlert!]!
  weatherForecast: WeatherForecast!
  upcomingEvents: [Event!]!
}

type MetricWithTrend {
  value: Float!
  change: Float!
  trend: [Float!]!
}

type Mutation {
  # Mark notification as read
  markNotificationAsRead(id: ID!): Boolean
  # Execute AI suggestion
  executeAISuggestion(id: ID!, parameters: JSON): AISuggestionResult
}
```

### Calendar/Tee Sheet Module

```graphql
type Query {
  # Get tee times for a specific date
  teeTimes(
    courseId: ID!
    date: Date!
    view: CalendarView = DAILY
    status: TeeTimeStatus
  ): TeeTimesResponse
  
  # Get tee time by ID
  teeTime(id: ID!): TeeTime
  
  # Get available tee time slots
  availableTeeTimeSlots(
    courseId: ID!
    date: Date!
    playerCount: Int = 4
    timeRangeStart: String
    timeRangeEnd: String
  ): [TeeTimeSlot!]!
}

type TeeTimesResponse {
  teeTimeSlots: [TeeTime!]!
  summary: TeeTimeSummary!
}

type TeeTimeSummary {
  totalSlots: Int!
  bookedSlots: Int!
  availableSlots: Int!
  totalRevenue: Float!
  averagePlayersPerSlot: Float!
}

type Mutation {
  # Create a new tee time
  createTeeTime(input: CreateTeeTimeInput!): TeeTime
  
  # Update an existing tee time
  updateTeeTime(id: ID!, input: UpdateTeeTimeInput!): TeeTime
  
  # Cancel a tee time
  cancelTeeTime(id: ID!, reason: String): TeeTime
  
  # Add player to tee time
  addPlayerToTeeTime(teeTimeId: ID!, playerId: ID!): TeeTime
  
  # Remove player from tee time
  removePlayerFromTeeTime(teeTimeId: ID!, playerId: ID!): TeeTime
  
  # Generate tee time slots for a date range
  generateTeeTimeSlots(
    courseId: ID!
    startDate: Date!
    endDate: Date!
    settings: TeeTimeGenerationSettings
  ): GenerateTeeTimeSlotsResponse
}
```

### Roster Module

```graphql
type Query {
  # Get golfer by ID
  golfer(id: ID!): Golfer
  
  # List golfers with pagination
  golfers(
    courseId: ID!
    filter: GolferFilterInput
    sort: GolferSortInput
    page: Int = 1
    perPage: Int = 20
  ): GolferPagination
  
  # Search golfers
  searchGolfers(courseId: ID!, searchTerm: String!): [Golfer!]!
  
  # Get golfer play history
  golferPlayHistory(golferId: ID!, limit: Int = 10): [TeeTime!]!
}

type GolferPagination {
  items: [Golfer!]!
  totalCount: Int!
  pageInfo: PageInfo!
}

type Mutation {
  # Create a new golfer
  createGolfer(input: CreateGolferInput!): Golfer
  
  # Update an existing golfer
  updateGolfer(id: ID!, input: UpdateGolferInput!): Golfer
  
  # Delete a golfer
  deleteGolfer(id: ID!): Boolean
  
  # Update golfer star rating
  updateGolferStarRating(id: ID!, rating: Int!): Golfer
  
  # Import golfers from CSV
  importGolfers(courseId: ID!, file: Upload!): ImportGolfersResponse
}
```

### Pro Shop Module

```graphql
type Query {
  # Get product by ID
  product(id: ID!): Product
  
  # List products with pagination
  products(
    courseId: ID!
    filter: ProductFilterInput
    sort: ProductSortInput
    page: Int = 1
    perPage: Int = 20
  ): ProductPagination
  
  # Get product categories
  productCategories(courseId: ID!): [Category!]!
  
  # Get products by category
  productsByCategory(courseId: ID!, categoryId: ID!): [Product!]!
  
  # Search products
  searchProducts(courseId: ID!, searchTerm: String!): [Product!]!
}

type Mutation {
  # Create a new product
  createProduct(input: CreateProductInput!): Product
  
  # Update an existing product
  updateProduct(id: ID!, input: UpdateProductInput!): Product
  
  # Delete a product
  deleteProduct(id: ID!): Boolean
  
  # Update product inventory
  updateProductInventory(id: ID!, quantity: Int!): Product
  
  # Process product sale
  processProductSale(input: ProductSaleInput!): Sale
  
  # Create product category
  createProductCategory(input: CreateCategoryInput!): Category
}
```

### 19th Hole (F&B) Module

```graphql
type Query {
  # Get menu item by ID
  menuItem(id: ID!): MenuItem
  
  # List menu items with pagination
  menuItems(
    courseId: ID!
    filter: MenuItemFilterInput
    sort: MenuItemSortInput
    page: Int = 1
    perPage: Int = 20
  ): MenuItemPagination
  
  # Get menu categories
  menuCategories(courseId: ID!): [Category!]!
  
  # Get menu items by category
  menuItemsByCategory(courseId: ID!, categoryId: ID!): [MenuItem!]!
  
  # Get active orders
  activeOrders(courseId: ID!): [FoodOrder!]!
}

type Mutation {
  # Create a new menu item
  createMenuItem(input: CreateMenuItemInput!): MenuItem
  
  # Update an existing menu item
  updateMenuItem(id: ID!, input: UpdateMenuItemInput!): MenuItem
  
  # Delete a menu item
  deleteMenuItem(id: ID!): Boolean
  
  # Create a food order
  createFoodOrder(input: CreateFoodOrderInput!): FoodOrder
  
  # Update order status
  updateOrderStatus(id: ID!, status: OrderStatus!): FoodOrder
  
  # Add item to order
  addItemToOrder(orderId: ID!, menuItemId: ID!, quantity: Int!, customizations: [CustomizationInput!]): FoodOrder
}
```

### Events & Tournaments Module

```graphql
type Query {
  # Get event by ID
  event(id: ID!): Event
  
  # List events with pagination
  events(
    courseId: ID!
    filter: EventFilterInput
    sort: EventSortInput
    page: Int = 1
    perPage: Int = 20
  ): EventPagination
  
  # Get upcoming events
  upcomingEvents(courseId: ID!, limit: Int = 5): [Event!]!
  
  # Get event registrants
  eventRegistrants(eventId: ID!): [Golfer!]!
}

type Mutation {
  # Create a new event
  createEvent(input: CreateEventInput!): Event
  
  # Update an existing event
  updateEvent(id: ID!, input: UpdateEventInput!): Event
  
  # Delete an event
  deleteEvent(id: ID!): Boolean
  
  # Register golfer for event
  registerGolferForEvent(eventId: ID!, golferId: ID!): Event
  
  # Remove golfer from event
  removeGolferFromEvent(eventId: ID!, golferId: ID!): Event
  
  # Generate event microsite
  generateEventMicrosite(eventId: ID!): Event
}
```

### Analytics Module

```graphql
type Query {
  # Get analytics overview
  analyticsOverview(courseId: ID!, timeframe: TimeframeInput = MONTHLY): AnalyticsOverview
  
  # Run custom analytics query
  runAnalyticsQuery(query: AnalyticsQueryInput!): AnalyticsResult
  
  # Get tee time distribution
  teeTimeDistribution(courseId: ID!, timeframe: TimeframeInput = MONTHLY): [TimeSlotDistribution!]!
  
  # Get revenue breakdown
  revenueBreakdown(courseId: ID!, timeframe: TimeframeInput = MONTHLY): RevenueBreakdown!
  
  # Get golfer retention metrics
  golferRetentionMetrics(courseId: ID!, timeframe: TimeframeInput = MONTHLY): RetentionMetrics!
}

type AnalyticsOverview {
  metrics: {
    members: MetricWithTrend!
    golferReturnRate: MetricWithTrend!
    obsoleteGolfers: MetricWithTrend!
  }
  teeTimeDistribution: [TimeSlotDistribution!]!
  revenueBreakdown: RevenueBreakdown!
}

type Mutation {
  # Schedule report generation
  scheduleReport(input: ScheduleReportInput!): ScheduledReport
  
  # Export analytics data
  exportAnalyticsData(query: AnalyticsQueryInput!, format: ExportFormat!): ExportJob
}
```

### Back Office Module

```graphql
type Query {
  # Get employee by ID
  employee(id: ID!): Employee
  
  # List employees with pagination
  employees(
    courseId: ID!
    filter: EmployeeFilterInput
    sort: EmployeeSortInput
    page: Int = 1
    perPage: Int = 20
  ): EmployeePagination
  
  # Get shifts for a date range
  shifts(courseId: ID!, startDate: Date!, endDate: Date): [Shift!]!
  
  # Get marketing templates
  marketingTemplates(courseId: ID!, category: String): [MarketingTemplate!]!
  
  # Get payroll data
  payrollData(courseId: ID!, startDate: Date!, endDate: Date!): PayrollData!
}

type Mutation {
  # Create a new employee
  createEmployee(input: CreateEmployeeInput!): Employee
  
  # Update an existing employee
  updateEmployee(id: ID!, input: UpdateEmployeeInput!): Employee
  
  # Delete an employee
  deleteEmployee(id: ID!): Boolean
  
  # Create a shift
  createShift(input: CreateShiftInput!): Shift
  
  # Update a shift
  updateShift(id: ID!, input: UpdateShiftInput!): Shift
  
  # Clock in employee
  clockIn(employeeId: ID!, verificationCode: String): Shift
  
  # Clock out employee
  clockOut(shiftId: ID!): Shift
  
  # Create marketing template
  createMarketingTemplate(input: CreateTemplateInput!): MarketingTemplate
}
```

### Settings Module

```graphql
type Query {
  # Get course settings
  courseSettings(courseId: ID!): CourseSettings
  
  # Get user roles
  userRoles(courseId: ID!): [UserRole!]!
  
  # Get audit logs
  auditLogs(
    courseId: ID!
    filter: AuditLogFilterInput
    page: Int = 1
    perPage: Int = 20
  ): AuditLogPagination
  
  # Get integrations
  integrations(courseId: ID!): [Integration!]!
}

type Mutation {
  # Update course settings
  updateCourseSettings(courseId: ID!, input: UpdateCourseSettingsInput!): CourseSettings
  
  # Create user role
  createUserRole(input: CreateUserRoleInput!): UserRole
  
  # Update user role
  updateUserRole(id: ID!, input: UpdateUserRoleInput!): UserRole
  
  # Delete user role
  deleteUserRole(id: ID!): Boolean
  
  # Configure integration
  configureIntegration(courseId: ID!, integrationType: IntegrationType!, config: JSON!): Integration
  
  # Test integration
  testIntegration(integrationId: ID!): IntegrationTestResult
}
```

### Mobile App - Home Screen

```graphql
type Query {
  # Get recommended tee times
  recommendedTeeTimes(courseId: ID, date: Date): [TeeTime!]!
  
  # Get golf news
  golfNews(limit: Int = 5): [NewsItem!]!
  
  # Get upcoming events
  upcomingEvents(limit: Int = 5): [Event!]!
  
  # Get upcoming tee times for current user
  upcomingTeeTimes(limit: Int = 5): [TeeTime!]!
  
  # Get notifications for current user
  userNotifications(unreadOnly: Boolean = false): [Notification!]!
}

type Mutation {
  # Mark notification as read
  markNotificationAsRead(id: ID!): Boolean
  
  # Update user preferences
  updateUserPreferences(input: UserPreferencesInput!): UserPreferences
}
```

### Mobile App - Book a Tee Time

```graphql
type Query {
  # Search courses
  searchCourses(
    location: GeoPointInput
    radius: Float = 25
    filter: CourseFilterInput
  ): [Course!]!
  
  # Get available tee times
  availableTeeTimeSlots(
    courseId: ID!
    date: Date!
    playerCount: Int = 4
    timeRangeStart: String
    timeRangeEnd: String
  ): [TeeTimeSlot!]!
  
  # Get tee time details
  teeTimeDetails(id: ID!): TeeTime
}

type Mutation {
  # Book a tee time
  bookTeeTime(input: BookTeeTimeInput!): BookingResult
  
  # Cancel a booking
  cancelBooking(bookingId: ID!, reason: String): Boolean
  
  # Add player to booking
  addPlayerToBooking(bookingId: ID!, playerId: ID): BookingResult
}
```

### Mobile App - In-Play Mode

```graphql
type Query {
  # Get active round
  activeRound(teeTimeId: ID!): Round
  
  # Get course map
  courseMap(courseId: ID!): CourseMap
  
  # Get hole details
  holeDetails(courseId: ID!, holeNumber: Int!): HoleDetails
}

type Mutation {
  # Start round
  startRound(teeTimeId: ID!): Round
  
  # Update score
  updateScore(roundId: ID!, playerId: ID!, holeNumber: Int!, score: Int!): Round
  
  # Submit feedback
  submitFeedback(roundId: ID!, feedback: [FeedbackInput!]!): Boolean
  
  # Update player position
  updatePlayerPosition(roundId: ID!, position: GeoPointInput!): Boolean
  
  # Create food order during round
  createRoundFoodOrder(roundId: ID!, input: CreateFoodOrderInput!): FoodOrder
}
```

### Mobile App - Marketplace

```graphql
type Query {
  # Get recommended products
  recommendedProducts(
    teeTimeId: ID, 
    weatherForecast: WeatherInput
  ): [Product!]!
  
  # Get marketplace categories
  marketplaceCategories: [Category!]!
  
  # Get products by category
  productsByCategory(
    categoryId: ID!, 
    filter: ProductFilter
  ): [Product!]!
  
  # Search marketplace products
  searchMarketplaceProducts(searchTerm: String!): [Product!]!
}

type Mutation {
  # Add product to cart
  addToCart(productId: ID!, quantity: Int = 1, attributes: JSON): Cart
  
  # Remove product from cart
  removeFromCart(cartItemId: ID!): Cart
  
  # Checkout cart
  checkout(paymentMethodId: ID!, pickupDetails: PickupDetailsInput): Order
}
```

### Mobile App - Golfer Profile

```graphql
type Query {
  # Get golfer profile
  golferProfile: Golfer
  
  # Get play history
  playHistory(limit: Int = 10): [TeeTime!]!
  
  # Get golfer equipment
  golferEquipment: GolferEquipment
  
  # Get membership information
  membershipDetails: [Membership!]!
  
  # Get user preferences
  userPreferences: UserPreferences
}

type Mutation {
  # Update golfer profile
  updateGolferProfile(input: UpdateGolferProfileInput!): Golfer
  
  # Update golfer equipment
  updateGolferEquipment(input: UpdateEquipmentInput!): GolferEquipment
  
  # Update user preferences
  updateUserPreferences(input: UserPreferencesInput!): UserPreferences
  
  # Update notification settings
  updateNotificationSettings(input: NotificationSettingsInput!): NotificationSettings
  
  # Verify membership
  verifyMembership(membershipId: ID!, courseId: ID!): VerificationResult
}
```

### Mobile App - Rewards

```graphql
type Query {
  # Get rewards information
  rewardsInfo: Rewards
  
  # Get rewards transactions
  rewardsTransactions(limit: Int = 20): [RewardTransaction!]!
  
  # Get available rewards
  availableRewards: [Reward!]!
  
  # Get tournament predictions
  tournamentPredictions: [Prediction!]!
}

type Mutation {
  # Redeem reward
  redeemReward(rewardId: ID!): RedemptionResult
  
  # Submit tournament prediction
  submitTournamentPrediction(tournamentId: ID!, playerId: ID!): Prediction
}
```

### Coach Platform - Calendar

```graphql
type Query {
  # Get coach schedule
  coachSchedule(startDate: Date!, endDate: Date): [Lesson!]!
  
  # Get coach availability
  coachAvailability(date: Date!): [TimeSlot!]!
  
  # Get lesson details
  lessonDetails(id: ID!): Lesson
  
  # Get student list
  students(
    filter: StudentFilterInput
    sort: StudentSortInput
    page: Int = 1
    perPage: Int = 20
  ): StudentPagination
}

type Mutation {
  # Create lesson
  createLesson(input: CreateLessonInput!): Lesson
  
  # Update lesson
  updateLesson(id: ID!, input: UpdateLessonInput!): Lesson
  
  # Cancel lesson
  cancelLesson(id: ID!, reason: String): Lesson
  
  # Set availability
  setAvailability(input: SetAvailabilityInput!): [TimeSlot!]!
}
```

### Coach Platform - Messenger

```graphql
type Query {
  # Get conversations
  conversations: [Conversation!]!
  
  # Get conversation messages
  conversationMessages(
    conversationId: ID!,
    cursor: String,
    limit: Int = 20
  ): MessageConnection!
  
  # Get message templates
  messageTemplates: [MessageTemplate!]!
}

type MessageConnection {
  edges: [MessageEdge!]!
  pageInfo: PageInfo!
}

type MessageEdge {
  node: Message!
  cursor: String!
}

type Mutation {
  # Send message
  sendMessage(
    conversationId: ID!,
    content: String!,
    attachments: [Upload!]
  ): Message
  
  # Create conversation
  createConversation(studentId: ID!): Conversation
  
  # Create message template
  createMessageTemplate(name: String!, content: String!): MessageTemplate
}
```

### Coach Platform - Payment

```graphql
type Query {
  # Get payment history
  paymentHistory(
    filter: PaymentFilterInput,
    page: Int = 1,
    perPage: Int = 20
  ): PaymentPagination
  
  # Get payment details
  paymentDetails(id: ID!): Payment
  
  # Get service types
  serviceTypes: [ServiceType!]!
}

type Mutation {
  # Process payment
  processPayment(input: ProcessPaymentInput!): Payment
  
  # Issue refund
  issueRefund(paymentId: ID!, amount: Float, reason: String): Refund
  
  # Create service type
  createServiceType(name: String!, price: Float!): ServiceType
}
```

### Coach Platform - Academies & Clinics

```graphql
type Query {
  # Get clinic by ID
  clinic(id: ID!): Clinic
  
  # List clinics
  clinics(
    filter: ClinicFilterInput,
    sort: ClinicSortInput,
    page: Int = 1,
    perPage: Int = 20
  ): ClinicPagination
  
  # Get clinic students
  clinicStudents(clinicId: ID!): [Golfer!]!
}

type Mutation {
  # Create clinic
  createClinic(input: CreateClinicInput!): Clinic
  
  # Update clinic
  updateClinic(id: ID!, input: UpdateClinicInput!): Clinic
  
  # Delete clinic
  deleteClinic(id: ID!): Boolean
  
  # Enroll student in clinic
  enrollStudent(clinicId: ID!, studentId: ID!): Clinic
  
  # Remove student from clinic
  removeStudent(clinicId: ID!, studentId: ID!): Clinic
}
```

## Key Data Models

### Golfer/User
```javascript
{
  id: ObjectId,
  firstName: String,
  lastName: String,
  phoneNumber: String,
  email: String,
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String
  },
  lastPlayDate: Date,
  upcomingPlayDate: Date,
  membershipStatus: Boolean,
  membershipType: String,
  membershipId: String,
  satisfactionScore: Number,
  starRating: Number, // 1-3 crowns
  hasCardOnFile: Boolean,
  isStudent: Boolean,
  professorId: ObjectId, // Reference to Coach
  totalRounds: Number,
  averageScore: Number,
  createdAt: Date,
  updatedAt: Date
}
```

### TeeTime
```javascript
{
  id: ObjectId,
  courseId: ObjectId,
  time: Date,
  duration: Number,
  price: Number,
  dynamicPricing: Boolean,
  memberPrice: Number,
  maxPlayers: Number,
  players: [ObjectId], // References to Golfer
  status: String, // 'booked', 'available', 'completed', 'cancelled'
  weatherForecast: {
    temperature: Number,
    condition: String,
    icon: String
  },
  revenue: Number,
  specialRequests: [String],
  createdAt: Date,
  updatedAt: Date
}
```

### Course
```javascript
{
  id: ObjectId,
  name: String,
  corporateId: ObjectId, // For multi-location setup
  regionId: ObjectId,   // Optional: Region, State, Metro grouping
  location: {
    address: String,
    city: String,
    state: String,
    country: String,
    timezone: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contact: {
    phone: String,
    email: String,
    website: String
  },
  operatingHours: {
    monday: { open: String, close: String },
    tuesday: { open: String, close: String },
    // ... other days
  },
  features: [String],
  settings: {
    teeTimeInterval: Number,
    maxPlayersPerSlot: Number,
    cancellationPolicy: String,
    weatherPolicy: String,
    isPrivate: Boolean,
    requiresMembership: Boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

## AI Suggestive Action System

The AI Suggestive Action system provides quick actions based on user behavior and system state.

### Key AI-Driven Features

1. **Quick Add** - Intelligent golfer registration:
   - Auto-fill returning golfer details
   - Suggest appropriate tee times
   - Pre-select common preferences

2. **Quick Food/Drink** - Streamlined F&B ordering:
   - Category-based selection
   - Remember frequent orders
   - Intelligent combos and upselling

3. **Quick Message** - Templated communications:
   - Context-aware templates (weather delays, promotions)
   - Multi-channel delivery (SMS, email, in-app)
   - Personalization tokens

4. **Quick Check Out** - Efficient POS:
   - Product category navigation
   - Membership discount application
   - Payment processing

5. **Quick Clock-In/Out** - Employee time tracking:
   - Authentication via SMS verification
   - Shift recognition
   - Break tracking

6. **Help** - Contextual assistance:
   - Page-specific help content
   - Guided workflows
   - FAQ suggestions

## Authentication & Authorization

### Multi-Tiered Authentication Flow

1. **User Login**: 
   - User provides credentials
   - Server validates and issues JWT access token and refresh token
   - Token contains user role and access level information

2. **Authorization**:
   - Hierarchical Access Control (Corporate -> Regional -> Course)
   - Role-Based Access Control (Admin, Manager, Staff, etc.)
   - Field-Level Security for sensitive data

## Implementation Phases

### Phase 1: Foundation
- Project structure setup
- Mongoose model definitions
- GraphQL schema generation configuration
- Authentication implementation
- Core models and basic queries

### Phase 2: Core CRM Features
- Dashboard
- Calendar/Tee Sheet
- Roster
- Settings
- Basic AI suggestions

### Phase 3: Extended CRM Features
- Pro Shop
- 19th Hole
- Events & Tournaments
- Back Office
- Analytics

### Phase 4: Mobile Features
- Home Screen
- Tee Time Booking
- Golfer Profile
- In-Play Mode

### Phase 5: Coach Platform
- Calendar
- Messenger
- Payment
- Academies & Clinics

### Phase 6: Integration and Refinement
- Third-party integrations
- Advanced AI features
- Performance optimization
- Final user experience refinements

## Conclusion

This document outlines the architecture plan for the Albatros Golf Management System. The solution includes three connected applications built on a microservices architecture using Node.js, GraphQL, and MongoDB.

The modular, stateless design supports independent feature development with consistent data models. Using Mongoose as the single source of truth eliminates duplication while providing flexible data access.

The AI-driven quick action system streamlines operations for staff. The mobile app enhances golfer experience from booking to play. The coach platform provides specialized tools for instructors.

This architecture balances current needs with future growth, ensuring the system can scale as the business grows. 