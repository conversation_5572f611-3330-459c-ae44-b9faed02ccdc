.backOfficeContainer {
  background: #f8fafc;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  min-height: 80vh;
}

.tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.tab {
  background: none;
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  color: #64748b;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.activeTab {
  background: #fff;
  color: #0f172a;
  font-weight: 600;
  border-bottom: 2px solid #38bdf8;
}

.tabContent {
  background: #fff;
  border-radius: 0 0 12px 12px;
  padding: 24px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.summaryTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.heading {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0f172a;
}

.shiftsTableWrapper {
  overflow-x: auto;
}

.shiftsTable {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.shiftsTable th, .shiftsTable td {
  padding: 12px 16px;
  text-align: left;
}

.shiftsTable th {
  background: #f1f5f9;
  color: #64748b;
  font-weight: 500;
}

.shiftsTable tr:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.employeeInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #38bdf8;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
}

.employeeName {
  font-weight: 600;
  color: #0f172a;
}

.employeeRole {
  font-size: 0.9rem;
  color: #64748b;
}

.statusApproved {
  background: #d1fae5;
  color: #059669;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
}

.statusPending {
  background: #fef3c7;
  color: #b45309;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
}

.actionIcon {
  margin-right: 8px;
  cursor: pointer;
  font-size: 1.1rem;
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 20px;
}

.templateCard {
  background: #f1f5f9;
  border-radius: 10px;
  padding: 18px 20px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.templateHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.templateType {
  background: #bae6fd;
  color: #0369a1;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusActive {
  background: #d1fae5;
  color: #059669;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusDraft {
  background: #fef3c7;
  color: #b45309;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusInactive {
  background: #f1f5f9;
  color: #64748b;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateName {
  font-weight: 600;
  color: #0f172a;
  font-size: 1.05rem;
}

.templateMeta {
  color: #64748b;
  font-size: 0.92rem;
}

.marketingTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.templatesSection {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px 24px 18px 24px;
  margin-bottom: 0;
}

.templatesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.templatesActions {
  display: flex;
  gap: 12px;
}

.primaryButton {
  background: #38bdf8;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.primaryButton:hover {
  background: #0ea5e9;
}

.secondaryButton {
  background: #f1f5f9;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.secondaryButton:hover {
  background: #e0e7ef;
}

.analyticsSection {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px;
}

.analyticsTableWrapper {
  overflow-x: auto;
}

.analyticsTable {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}
.analyticsTable th, .analyticsTable td {
  padding: 12px 10px;
  text-align: left;
  font-size: 1rem;
}
.analyticsTable th {
  background: #f1f5f9;
  color: #64748b;
  font-weight: 500;
}
.analyticsTable tr:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.analyticsCircle {
  display: inline-block;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f1f5f9;
  color: #0ea5e9;
  font-weight: 700;
  font-size: 1.1rem;
  text-align: center;
  line-height: 1.2;
  padding-top: 10px;
  margin-bottom: 2px;
}
.analyticsLabel {
  display: block;
  color: #64748b;
  font-size: 0.85rem;
  font-weight: 400;
  margin-top: 2px;
}

.moreButton {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0 8px;
  border-radius: 6px;
  transition: background 0.2s;
}
.moreButton:hover {
  background: #f1f5f9;
}

.modalOverlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modalContent {
  background: #fff;
  border-radius: 12px;
  padding: 32px 28px;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.templateEditorContainer {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 32px 32px 24px 32px;
  max-width: 800px;
  margin: 32px auto;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.templateEditorHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.templateEditorActions {
  display: flex;
  gap: 12px;
}

.templateEditorMeta {
  color: #64748b;
  font-size: 0.95rem;
  margin-bottom: 8px;
}

.templateTypeSwitcher {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.templateTypeSelect {
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background: #f8fafc;
}

.templateSubjectInput {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 1.05rem;
  margin-bottom: 8px;
  background: #f8fafc;
}

.templateEditorBox {
  background: #f8fafc;
  border-radius: 8px;
  padding: 0 0 12px 0;
  margin-bottom: 8px;
}

.templateEditorToolbar {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 8px 8px 8px;
  border-bottom: 1px solid #e2e8f0;
  background: #f1f5f9;
  border-radius: 8px 8px 0 0;
}

.toolbarButton {
  background: none;
  border: none;
  color: #0f172a;
  font-size: 1.1rem;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}
.toolbarButton:hover {
  background: #e0e7ef;
}

.toolbarSelect {
  margin-left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
}

.toolbarSpacer {
  flex: 1;
}

.variableDropdown {
  display: flex;
  align-items: center;
  gap: 4px;
}

.templateEditorTextarea {
  width: 100%;
  min-height: 120px;
  border: none;
  border-radius: 0 0 8px 8px;
  padding: 14px 16px;
  font-size: 1.05rem;
  background: #f8fafc;
  color: #0f172a;
  resize: vertical;
}

.rulesSection {
  background: #f8fafc;
  border-radius: 8px;
  padding: 18px 18px 12px 18px;
  margin-top: 18px;
}

.rulesHeading {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #0f172a;
}

.rulesRow {
  display: flex;
  gap: 32px;
  margin-bottom: 8px;
}
.rulesLabel {
  color: #64748b;
  font-size: 0.98rem;
  font-weight: 500;
  min-width: 80px;
}

.rulesFlow {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-top: 8px;
}
.rulesNode {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 1rem;
  color: #0f172a;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}
.rulesArrow {
  font-size: 1.3rem;
  color: #64748b;
}
.rulesDelay {
  color: #38bdf8;
  font-size: 0.95rem;
  margin-left: 6px;
}

.rulesSelect {
  margin: 8px 0 0 0;
  padding: 4px 10px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background: #f8fafc;
  width: 140px;
}

.rulesMiniBtn {
  background: #f1f5f9;
  border: none;
  color: #64748b;
  font-size: 1rem;
  border-radius: 4px;
  padding: 2px 8px;
  cursor: pointer;
  transition: background 0.2s;
}
.rulesMiniBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.rulesMiniBtn:hover:not(:disabled) {
  background: #e0e7ef;
}

.rulesAddBtn {
  background: #38bdf8;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 16px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 12px;
  margin-top: 8px;
  transition: background 0.2s;
}
.rulesAddBtn:hover {
  background: #0ea5e9;
}

.templateEditorQuill {
  background: #fff;
  border-radius: 0 0 8px 8px;
  min-height: 180px;
  margin-top: 0;
}

.smsValidation {
  margin-top: 8px;
  color: #64748b;
  font-size: 0.98rem;
  display: flex;
  align-items: center;
  gap: 16px;
}

.smsWarning {
  color: #eab308;
  font-weight: 600;
  margin-left: 16px;
}

.smsPreview {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 18px 16px;
  font-size: 1.1rem;
  color: #0f172a;
  max-width: 350px;
  margin: 0 auto;
  border: 1px solid #e2e8f0;
}

.inAppPreview {
  background: #f8fafc;
  border-radius: 8px;
  padding: 18px 16px;
  font-size: 1.1rem;
  color: #0f172a;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid #e2e8f0;
}

.accountingTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 24px 0;
}

.metricsRow {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.metricCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px 32px;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}
.metricIcon {
  font-size: 2rem;
  margin-bottom: 6px;
}
.metricValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0f172a;
}
.metricLabel {
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}
.metricSub {
  color: #94a3b8;
  font-size: 0.95rem;
}
.metricChange {
  color: #22c55e;
  font-size: 0.95rem;
  font-weight: 600;
}
.metricExtra {
  color: #64748b;
  font-size: 0.95rem;
  margin-top: 2px;
}

.pnlCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px 32px;
  margin-bottom: 0;
}
.pnlHeader {
  font-size: 1.2rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 12px;
}
.pnlBody {
  min-height: 80px;
  color: #64748b;
}

.chartsRow {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}
.chartCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 18px 18px 12px 18px;
  flex: 1 1 320px;
  min-width: 320px;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.chartHeader {
  font-size: 1.05rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.timeframeSelect {
  margin-left: 8px;
  padding: 4px 10px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background: #f8fafc;
}
.reconcileBtn {
  background: #38bdf8;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 16px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 12px;
  transition: background 0.2s;
}
.reconcileBtn:hover {
  background: #0ea5e9;
}

.opexInsights {
  background: #f8fafc;
  border-radius: 8px;
  padding: 18px 18px 12px 18px;
  color: #64748b;
  font-size: 1rem;
  margin-top: 12px;
}

.reconcileModal {
  background: #fff;
  border-radius: 12px;
  padding: 40px 36px 32px 36px;
  min-width: 600px;
  min-height: 400px;
  max-width: 900px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.modalCloseBtn {
  position: absolute;
  top: 18px;
  right: 18px;
  background: none;
  border: none;
  font-size: 2rem;
  color: #64748b;
  cursor: pointer;
  z-index: 10;
}

.filtersRow {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 18px;
}

.filterSelect {
  margin-left: 8px;
  padding: 4px 10px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background: #f8fafc;
}

.exportBtn {
  background: #f1f5f9;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 6px 12px;
  font-weight: 500;
  font-size: 0.98rem;
  cursor: pointer;
  margin-left: 8px;
  transition: background 0.2s;
}
.exportBtn:hover {
  background: #bae6fd;
}

.dateRangeWrapper {
  position: relative;
  display: inline-block;
}
.dateRangeBtn {
  background: #f1f5f9;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 6px 16px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 8px;
  transition: background 0.2s;
}
.dateRangeBtn:hover {
  background: #bae6fd;
}
.dateRangePickerPopover {
  position: absolute;
  top: 40px;
  left: 0;
  z-index: 1001;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 12px 12px 8px 12px;
} 