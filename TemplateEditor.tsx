import React, { useState, useRef } from 'react';
import styles from './BackOffice.module.css';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

interface TemplateEditorProps {
  onClose: () => void;
}

type TemplateType = 'Email' | 'SMS' | 'In-App';

type RuleStep = {
  id: string;
  type: 'trigger' | 'action';
  label: string;
  detail?: string;
};

const initialContent = {
  Email: {
    subject: 'Welcome to Watters Creek',
    body: `<p>Hi {Golfer First Name},</p><p>Welcome to Watters Creek Golf Course, we know there are some world class courses in the area and thank you for choosing us! Our team would love to hear your experience on {date_played}. we strive to ensure the course and facilities are always in great shape. Click here {link_survey_welcome_email} and feel free to share any feedback you would like to.</p><p>As a courtesy, we would love to extend a $5 Discount for your next round, if played in the next 30 days, simply add the code {welcome_email_discount} and the amount will be deducted from your total amount.</p><p>Thanks again and we look forward to seeing you again soon!</p><p>{GM_Signature}<br/>{Course Logo}</p>`
  },
  SMS: {
    subject: '',
    body: 'Hi {Golfer First Name}, thanks for playing at Watters Creek! Click here {link_survey_welcome_email} to share feedback. Use code {welcome_email_discount} for $5 off your next round.'
  },
  'In-App': {
    subject: '',
    body: 'Welcome to Watters Creek! We value your feedback. {link_survey_welcome_email}'
  }
};

const variables = [
  '{Golfer First Name}',
  '{date_played}',
  '{link_survey_welcome_email}',
  '{welcome_email_discount}',
  '{GM_Signature}',
  '{Course Logo}'
];

const triggerOptions = [
  'User Added',
  'User Birthday',
  'User Books Tee Time',
  'User Joins Membership',
];
const actionOptions = [
  'Send Email',
  'Send SMS',
  'Send In-App',
  'Wait',
  'Stop',
];

function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

function getSmsSegments(text: string) {
  // GSM-7 encoding, 160 chars per segment, 153 for multipart
  const len = text.length;
  if (len <= 160) return 1;
  return Math.ceil(len / 153);
}

const INAPP_LIMIT = 1000;

const TemplateEditor: React.FC<TemplateEditorProps> = ({ onClose }) => {
  const [type, setType] = useState<TemplateType>('Email');
  const [status, setStatus] = useState<'Active' | 'Draft'>('Active');
  const [subject, setSubject] = useState(initialContent.Email.subject);
  const [body, setBody] = useState(initialContent.Email.body);
  const [showPreview, setShowPreview] = useState(false);
  const [lastModified] = useState('2024-01-15');

  // Dynamic rules builder
  const [rules, setRules] = useState<RuleStep[]>([
    { id: generateId(), type: 'trigger', label: 'User Added' },
    { id: generateId(), type: 'action', label: 'Send Email', detail: '24 Hrs Delay' },
    { id: generateId(), type: 'action', label: 'Stop' },
  ]);

  // Rich text editor ref
  const quillRef = useRef<any>(null);

  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value as TemplateType;
    setType(newType);
    setSubject(initialContent[newType].subject);
    setBody(initialContent[newType].body);
  };

  // Insert variable at cursor for Quill
  const handleVariableInsert = (v: string) => {
    if (!v) return;
    if (type === 'Email' && quillRef.current) {
      const quill = quillRef.current.getEditor();
      const range = quill.getSelection(true);
      quill.insertText(range.index, v);
      quill.setSelection(range.index + v.length, 0);
    } else {
      setBody(body + v);
    }
  };

  const handleSave = () => {
    // Mock save
    onClose();
  };

  // Rules builder handlers
  const addStep = (stepType: 'trigger' | 'action') => {
    const options = stepType === 'trigger' ? triggerOptions : actionOptions;
    const label = options[0];
    setRules(rules => [
      ...rules,
      { id: generateId(), type: stepType, label, detail: stepType === 'action' && label === 'Wait' ? '24 Hrs Delay' : undefined }
    ]);
  };

  const updateStep = (id: string, label: string) => {
    setRules(rules => rules.map(r => r.id === id ? { ...r, label, detail: label === 'Wait' ? '24 Hrs Delay' : undefined } : r));
  };

  const removeStep = (id: string) => {
    setRules(rules => rules.filter(r => r.id !== id));
  };

  const moveStep = (id: string, direction: 'left' | 'right') => {
    setRules(rules => {
      const idx = rules.findIndex(r => r.id === id);
      if (idx < 0) return rules;
      const newIdx = direction === 'left' ? idx - 1 : idx + 1;
      if (newIdx < 0 || newIdx >= rules.length) return rules;
      const newRules = [...rules];
      const [moved] = newRules.splice(idx, 1);
      newRules.splice(newIdx, 0, moved);
      return newRules;
    });
  };

  // Validation for SMS/In-App
  const smsCharCount = type === 'SMS' ? body.length : 0;
  const smsSegments = type === 'SMS' ? getSmsSegments(body) : 0;
  const smsOverLimit = type === 'SMS' && smsCharCount > 160;
  const inAppCharCount = type === 'In-App' ? body.length : 0;
  const inAppOverLimit = type === 'In-App' && inAppCharCount > INAPP_LIMIT;

  return (
    <div className={styles.templateEditorContainer}>
      {/* Header */}
      <div className={styles.templateEditorHeader}>
        <div>
          <h2 className={styles.heading}>Welcome {type}</h2>
          <span className={styles.templateType}>{type}</span>
          <span className={status === 'Active' ? styles.templateStatusActive : styles.templateStatusDraft}>{status}</span>
        </div>
        <div className={styles.templateEditorActions}>
          <button className={styles.secondaryButton} onClick={() => setShowPreview(true)}>Preview Message</button>
          <button className={styles.primaryButton} onClick={handleSave}>Save Template</button>
        </div>
      </div>
      <div className={styles.templateEditorMeta}>Last Modified: {lastModified}</div>

      {/* Type Switcher */}
      <div className={styles.templateTypeSwitcher}>
        <label>Type: </label>
        <select value={type} onChange={handleTypeChange} className={styles.templateTypeSelect}>
          <option value="Email">Email</option>
          <option value="SMS">SMS</option>
          <option value="In-App">In-App</option>
        </select>
      </div>

      {/* Subject line (for Email) */}
      {type === 'Email' && (
        <input
          className={styles.templateSubjectInput}
          value={subject}
          onChange={e => setSubject(e.target.value)}
          placeholder="Subject line"
        />
      )}

      {/* Message Editor */}
      <div className={styles.templateEditorBox}>
        <div className={styles.templateEditorToolbar}>
          {/* Quill toolbar is handled by the component */}
          <div className={styles.variableDropdown}>
            <span>Variable</span>
            <select onChange={e => handleVariableInsert(e.target.value)}>
              <option value="">Insert...</option>
              {variables.map(v => (
                <option key={v} value={v}>{v}</option>
              ))}
            </select>
          </div>
        </div>
        {type === 'Email' ? (
          <ReactQuill
            ref={quillRef}
            value={body}
            onChange={setBody}
            theme="snow"
            modules={{
              toolbar: [
                [{ 'header': [1, 2, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                ['link', 'image', 'code-block'],
                ['clean']
              ]
            }}
            className={styles.templateEditorQuill}
          />
        ) : (
          <>
            <textarea
              className={styles.templateEditorTextarea}
              value={body}
              onChange={e => setBody(e.target.value)}
              rows={5}
            />
            {type === 'SMS' && (
              <div className={styles.smsValidation}>
                <span>Characters: {smsCharCount} / 160</span>
                <span style={{ marginLeft: 16 }}>Segments: {smsSegments}</span>
                {smsOverLimit && <span className={styles.smsWarning}>Over 160 chars! Will be split into multiple SMS.</span>}
              </div>
            )}
            {type === 'In-App' && (
              <div className={styles.smsValidation}>
                <span>Characters: {inAppCharCount} / {INAPP_LIMIT}</span>
                {inAppOverLimit && <span className={styles.smsWarning}>Over {INAPP_LIMIT} chars! Message may be truncated.</span>}
              </div>
            )}
          </>
        )}
      </div>

      {/* Audience / Rules */}
      <div className={styles.rulesSection}>
        <h3 className={styles.rulesHeading}>Audience / Rules</h3>
        <div className={styles.rulesRow}>
          <span className={styles.rulesLabel}>Step</span>
          <span className={styles.rulesLabel}>Type</span>
          <span className={styles.rulesLabel}>Action</span>
        </div>
        <div className={styles.rulesFlow}>
          {rules.map((step, idx) => (
            <div key={step.id} className={styles.rulesNode}>
              <div style={{ fontWeight: 600 }}>{step.type === 'trigger' ? 'Trigger' : 'Action'}</div>
              <select
                value={step.label}
                onChange={e => updateStep(step.id, e.target.value)}
                className={styles.rulesSelect}
              >
                {(step.type === 'trigger' ? triggerOptions : actionOptions).map(opt => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
              {step.detail && <div className={styles.rulesDelay}>{step.detail}</div>}
              <div style={{ display: 'flex', gap: 4, marginTop: 6 }}>
                <button className={styles.rulesMiniBtn} onClick={() => moveStep(step.id, 'left')} disabled={idx === 0}>←</button>
                <button className={styles.rulesMiniBtn} onClick={() => moveStep(step.id, 'right')} disabled={idx === rules.length - 1}>→</button>
                <button className={styles.rulesMiniBtn} onClick={() => removeStep(step.id)}>✕</button>
              </div>
            </div>
          ))}
          <button className={styles.rulesAddBtn} onClick={() => addStep('trigger')}>+ Trigger</button>
          <button className={styles.rulesAddBtn} onClick={() => addStep('action')}>+ Action</button>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className={styles.modalOverlay} onClick={() => setShowPreview(false)}>
          <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
            <h3>Preview</h3>
            <div style={{ whiteSpace: 'pre-wrap', marginBottom: 16 }}>
              {type === 'Email' ? (
                <div dangerouslySetInnerHTML={{ __html: body }} />
              ) : (
                <div className={type === 'SMS' ? styles.smsPreview : styles.inAppPreview}>{body}</div>
              )}
            </div>
            <button className={styles.primaryButton} onClick={() => setShowPreview(false)}>Close</button>
          </div>
        </div>
      )}

      {/* Back button */}
      <button className={styles.secondaryButton} style={{ marginTop: 24 }} onClick={onClose}>&larr; Back to Templates</button>
    </div>
  );
};

export default TemplateEditor; 