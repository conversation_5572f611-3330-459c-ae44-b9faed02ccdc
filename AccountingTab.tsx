import React, { useState } from 'react';
import styles from './BackOffice.module.css';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { saveAs } from 'file-saver';
import { DateRange } from 'react-date-range';
import { format } from 'date-fns';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const departments = ['All', 'F&B', 'Pro Shop', 'Maintenance', 'Admin'];
const categories = ['All', 'Payroll', 'Benefits', 'R&D', 'Equipment'];
const timeframes = ['Months', 'Quarters', 'Year'];

const metrics = [
  { label: 'Cash Balance', value: '$2,378,466', sub: '4 hrs ago', change: '+3%', icon: '💰' },
  { label: 'Card Balance', value: '$40,267.00', sub: '4 hrs ago', change: '-1%', icon: '💳' },
  { label: 'Net Burn', value: '-$159,284', sub: '', change: '', icon: '🔥', extra: 'Runway 2 yrs, 11 mths' },
];

const baseLabels = ['Jan', 'Feb', 'Mar', 'Apr'];
const baseCashIn = [1000000, 900000, 1100000, 1200000];
const baseCashOut = [-800000, -950000, -1000000, -1050000];
const baseOpex = {
  Payroll: [200000, 220000, 210000, 230000],
  Benefits: [50000, 52000, 51000, 53000],
  'R&D': [30000, 35000, 32000, 34000],
  Equipment: [20000, 25000, 22000, 26000],
};

const dailyBreakdown = {
  Jan: [30000, 35000, 32000, 34000, 35000, 36000, 37000],
  Feb: [32000, 33000, 34000, 35000, 36000, 37000, 38000],
  Mar: [34000, 35000, 36000, 37000, 38000, 39000, 40000],
  Apr: [36000, 37000, 38000, 39000, 40000, 41000, 42000],
};

function exportChartToImage(chartId, filename) {
  const chart = ChartJS.getChart(chartId);
  if (chart) {
    const url = chart.toBase64Image();
    saveAs(url, filename);
  }
}

function exportDataToCSV(data, filename) {
  let csv = '';
  csv += data.labels.join(',') + '\n';
  data.datasets.forEach(ds => {
    csv += ds.label + ',' + ds.data.join(',') + '\n';
  });
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, filename);
}

const AccountingTab: React.FC = () => {
  const [timeframe, setTimeframe] = useState('Months');
  const [department, setDepartment] = useState('All');
  const [category, setCategory] = useState('All');
  const [showReconcile, setShowReconcile] = useState(false);
  const [drilldown, setDrilldown] = useState<{ label: string; data: number[] } | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState([
    {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
      key: 'selection',
    },
  ]);

  // Filtered data (mock logic)
  const cashData = {
    labels: baseLabels,
    datasets: [
      {
        label: 'Cash In',
        backgroundColor: '#4ade80',
        data: baseCashIn.map(v => department === 'All' ? v : v * 0.7),
      },
      {
        label: 'Cash Out',
        backgroundColor: '#f87171',
        data: baseCashOut.map(v => department === 'All' ? v : v * 0.7),
      },
    ],
  };

  const opexData = {
    labels: baseLabels,
    datasets: Object.entries(baseOpex)
      .filter(([cat]) => category === 'All' || cat === category)
      .map(([cat, data], idx) => ({
        label: cat,
        backgroundColor: ['#60a5fa', '#fbbf24', '#a78bfa', '#34d399'][idx],
        data: data.map(v => department === 'All' ? v : v * 0.7),
      })),
  };

  // Drilldown handler
  const handleBarClick = (elems, chart) => {
    if (elems.length > 0) {
      const idx = elems[0].index;
      const label = chart.data.labels[idx];
      setDrilldown({ label, data: dailyBreakdown[label] });
    }
  };

  return (
    <div className={styles.accountingTab}>
      {/* Filters */}
      <div className={styles.filtersRow}>
        <label>Department:
          <select className={styles.filterSelect} value={department} onChange={e => setDepartment(e.target.value)}>
            {departments.map(dep => <option key={dep} value={dep}>{dep}</option>)}
          </select>
        </label>
        <label>Category:
          <select className={styles.filterSelect} value={category} onChange={e => setCategory(e.target.value)}>
            {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
          </select>
        </label>
        <label>Timeframe:
          <select className={styles.filterSelect} value={timeframe} onChange={e => setTimeframe(e.target.value)}>
            {timeframes.map(tf => <option key={tf} value={tf}>{tf}</option>)}
          </select>
        </label>
        <div className={styles.dateRangeWrapper}>
          <button className={styles.dateRangeBtn} onClick={() => setShowDatePicker(v => !v)}>
            {format(dateRange[0].startDate, 'MMM d, yyyy')} - {format(dateRange[0].endDate, 'MMM d, yyyy')}
          </button>
          {showDatePicker && (
            <div className={styles.dateRangePickerPopover}>
              <DateRange
                editableDateInputs={true}
                onChange={item => setDateRange([item.selection])}
                moveRangeOnFirstSelection={false}
                ranges={dateRange}
                maxDate={new Date()}
              />
              <button className={styles.primaryButton} style={{ marginTop: 8 }} onClick={() => setShowDatePicker(false)}>Apply</button>
            </div>
          )}
        </div>
      </div>

      {/* Metrics Cards */}
      <div className={styles.metricsRow}>
        {metrics.map((m, idx) => (
          <div key={idx} className={styles.metricCard}>
            <div className={styles.metricIcon}>{m.icon}</div>
            <div className={styles.metricValue}>{m.value}</div>
            <div className={styles.metricLabel}>{m.label}</div>
            <div className={styles.metricSub}>{m.sub}</div>
            {m.change && <div className={styles.metricChange}>{m.change}</div>}
            {m.extra && <div className={styles.metricExtra}>{m.extra}</div>}
          </div>
        ))}
      </div>

      {/* Financial Overview / P&L */}
      <div className={styles.pnlCard}>
        <div className={styles.pnlHeader}>Financial Overview / P&amp;L</div>
        <div className={styles.pnlBody}>[P&amp;L summary or chart here]</div>
      </div>

      {/* Charts Row */}
      <div className={styles.chartsRow}>
        <div className={styles.chartCard}>
          <div className={styles.chartHeader}>
            Cash In, Cash Out
            <div>
              <button className={styles.exportBtn} onClick={() => exportChartToImage('cashChart', 'cash-in-out.png')}>Export PNG</button>
              <button className={styles.exportBtn} onClick={() => exportDataToCSV(cashData, 'cash-in-out.csv')}>Export CSV</button>
            </div>
          </div>
          <Bar
            id="cashChart"
            data={cashData}
            options={{
              responsive: true,
              plugins: { legend: { position: 'top' }, tooltip: { enabled: true } },
              onClick: (evt, elems, chart) => handleBarClick(elems, chart),
            }}
          />
        </div>
        <div className={styles.chartCard}>
          <div className={styles.chartHeader}>
            Operating Expenses - {timeframe}
            <div>
              <button className={styles.exportBtn} onClick={() => exportChartToImage('opexChart', 'opex.png')}>Export PNG</button>
              <button className={styles.exportBtn} onClick={() => exportDataToCSV(opexData, 'opex.csv')}>Export CSV</button>
            </div>
          </div>
          <Bar
            id="opexChart"
            data={opexData}
            options={{
              responsive: true,
              plugins: { legend: { position: 'top' }, tooltip: { enabled: true } },
              onClick: (evt, elems, chart) => handleBarClick(elems, chart),
            }}
          />
        </div>
        <div className={styles.chartCard}>
          <div className={styles.chartHeader}>Net Burn</div>
          <Bar
            data={{
              labels: baseLabels,
              datasets: [
                {
                  label: 'Net Burn',
                  backgroundColor: '#818cf8',
                  data: [-200000, -180000, -210000, -220000],
                },
              ],
            }}
            options={{ responsive: true, plugins: { legend: { position: 'top' }, tooltip: { enabled: true } } }}
          />
        </div>
        <div className={styles.chartCard}>
          <div className={styles.chartHeader}>
            Account Reconciliation
            <button className={styles.reconcileBtn} onClick={() => setShowReconcile(true)}>
              View Details
            </button>
          </div>
          <Bar data={cashData} options={{ responsive: true, plugins: { legend: { display: false } } }} />
        </div>
      </div>

      {/* OPEX Insights */}
      <div className={styles.opexInsights}>
        <b>OPEX Insights for Apr:</b> Operating expenses were $142K, an increase of 84% or $65K from Mar. <br />
        - Salary and Benefits related expenses increased by $48K majority driven by an increase of $30K in Employee Benefits - Medical.<br />
        - Research and Development related expenses increased by $25K due to an increase of $22K in Equipment &lt; USD $2,500 related expenses.
      </div>

      {/* Drilldown Modal */}
      {drilldown && (
        <div className={styles.modalOverlay}>
          <div className={styles.reconcileModal}>
            <button className={styles.modalCloseBtn} onClick={() => setDrilldown(null)}>×</button>
            <h2>Daily Breakdown for {drilldown.label}</h2>
            <Bar
              data={{
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                  {
                    label: 'Amount',
                    backgroundColor: '#60a5fa',
                    data: drilldown.data,
                  },
                ],
              }}
              options={{ responsive: true, plugins: { legend: { position: 'top' }, tooltip: { enabled: true } } }}
            />
          </div>
        </div>
      )}

      {/* Account Reconciliation Modal */}
      {showReconcile && (
        <div className={styles.modalOverlay}>
          <div className={styles.reconcileModal}>
            <button className={styles.modalCloseBtn} onClick={() => setShowReconcile(false)}>×</button>
            <h2>Account Reconciliation</h2>
            <Bar data={cashData} options={{ responsive: true, plugins: { legend: { position: 'top' } } }} />
            <div style={{ marginTop: 24 }}>
              <b>AP/AR Details:</b> <br />
              <ul>
                <li>Golfers: $12,000 Receivable</li>
                <li>Vendors: $8,000 Payable</li>
                <li>Other: $2,000 Receivable</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountingTab; 