// types/square.ts
export interface PaymentRequest {
  amount: number;
  currency: string;
  sourceId?: string;
  customerId?: string;
  locationId: string;
  referenceId?: string;
  note?: string;
  buyerEmailAddress?: string;
}

export interface PaymentResult {
  id: string;
  status: string;
  receiptNumber?: string;
  receiptUrl?: string;
  totalMoney: {
    amount: number;
    currency: string;
  };
}

export interface SquareConfig {
  applicationId: string;
  locationId: string;
  environment: 'sandbox' | 'production';
}

// services/squareService.ts
import { Client, Environment } from 'squareup';

export class SquarePaymentService {
  private client: Client;
  private locationId: string;

  constructor(config: SquareConfig) {
    this.client = new Client({
      environment: config.environment === 'production' 
        ? Environment.Production 
        : Environment.Sandbox,
      accessToken: process.env.SQUARE_ACCESS_TOKEN!,
    });
    this.locationId = config.locationId;
  }

  async createPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
    try {
      const { paymentsApi } = this.client;
      
      const requestBody = {
        sourceId: paymentRequest.sourceId!,
        idempotencyKey: this.generateIdempotencyKey(),
        amountMoney: {
          amount: BigInt(paymentRequest.amount),
          currency: paymentRequest.currency,
        },
        locationId: this.locationId,
        referenceId: paymentRequest.referenceId,
        note: paymentRequest.note,
        buyerEmailAddress: paymentRequest.buyerEmailAddress,
      };

      const response = await paymentsApi.createPayment(requestBody);
      
      if (response.result.payment) {
        const payment = response.result.payment;
        return {
          id: payment.id!,
          status: payment.status!,
          receiptNumber: payment.receiptNumber,
          receiptUrl: payment.receiptUrl,
          totalMoney: {
            amount: Number(payment.totalMoney?.amount || 0),
            currency: payment.totalMoney?.currency || 'USD',
          },
        };
      }
      
      throw new Error('Payment creation failed');
    } catch (error) {
      console.error('Square payment error:', error);
      throw error;
    }
  }

  async getPayment(paymentId: string): Promise<PaymentResult | null> {
    try {
      const { paymentsApi } = this.client;
      const response = await paymentsApi.getPayment(paymentId);
      
      if (response.result.payment) {
        const payment = response.result.payment;
        return {
          id: payment.id!,
          status: payment.status!,
          receiptNumber: payment.receiptNumber,
          receiptUrl: payment.receiptUrl,
          totalMoney: {
            amount: Number(payment.totalMoney?.amount || 0),
            currency: payment.totalMoney?.currency || 'USD',
          },
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching payment:', error);
      return null;
    }
  }

  private generateIdempotencyKey(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// hooks/useSquarePayment.ts (for React Native/Expo)
import { useState, useCallback } from 'react';
import { Alert } from 'react-native';

interface UseSquarePaymentReturn {
  isLoading: boolean;
  processPayment: (amount: number, cardDetails: any) => Promise<PaymentResult | null>;
  error: string | null;
}

export const useSquarePayment = (config: SquareConfig): UseSquarePaymentReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const processPayment = useCallback(async (
    amount: number, 
    cardDetails: any
  ): Promise<PaymentResult | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // This would typically call your backend API
      // which then processes the payment with Square
      const response = await fetch('/api/payments/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount * 100, // Convert to cents
          currency: 'USD',
          cardDetails,
          locationId: config.locationId,
        }),
      });

      if (!response.ok) {
        throw new Error('Payment processing failed');
      }

      const result = await response.json();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      setError(errorMessage);
      Alert.alert('Payment Error', errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [config.locationId]);

  return { isLoading, processPayment, error };
};

// components/SquarePaymentForm.tsx (Web CRM)
import React, { useState, useEffect, useRef } from 'react';

interface SquarePaymentFormProps {
  amount: number;
  onPaymentSuccess: (result: PaymentResult) => void;
  onPaymentError: (error: string) => void;
  config: SquareConfig;
}

declare global {
  interface Window {
    Square: any;
  }
}

export const SquarePaymentForm: React.FC<SquarePaymentFormProps> = ({
  amount,
  onPaymentSuccess,
  onPaymentError,
  config,
}) => {
  const [payments, setPayments] = useState<any>(null);
  const [card, setCard] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const cardContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initializeSquare = async () => {
      if (!window.Square) {
        console.error('Square.js not loaded');
        return;
      }

      try {
        const paymentsInstance = window.Square.payments(
          config.applicationId,
          config.locationId
        );
        setPayments(paymentsInstance);

        const cardInstance = await paymentsInstance.card();
        await cardInstance.attach(cardContainerRef.current);
        setCard(cardInstance);
      } catch (error) {
        console.error('Error initializing Square:', error);
        onPaymentError('Failed to initialize payment form');
      }
    };

    initializeSquare();

    return () => {
      if (card) {
        card.destroy();
      }
    };
  }, [config, onPaymentError]);

  const handlePayment = async () => {
    if (!card) {
      onPaymentError('Payment form not initialized');
      return;
    }

    setIsLoading(true);

    try {
      const result = await card.tokenize();
      
      if (result.status === 'OK') {
        // Send the token to your backend
        const response = await fetch('/api/payments/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sourceId: result.token,
            amount: amount * 100, // Convert to cents
            currency: 'USD',
            locationId: config.locationId,
          }),
        });

        if (!response.ok) {
          throw new Error('Payment processing failed');
        }

        const paymentResult = await response.json();
        onPaymentSuccess(paymentResult);
      } else {
        onPaymentError(result.errors?.[0]?.message || 'Tokenization failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      onPaymentError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="square-payment-form">
      <div className="payment-amount">
        <h3>Amount: ${amount.toFixed(2)}</h3>
      </div>
      
      <div 
        ref={cardContainerRef}
        id="card-container"
        style={{
          minHeight: '200px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          marginBottom: '20px'
        }}
      />
      
      <button
        onClick={handlePayment}
        disabled={isLoading || !card}
        style={{
          backgroundColor: '#0066cc',
          color: 'white',
          padding: '12px 24px',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          opacity: isLoading ? 0.6 : 1,
        }}
      >
        {isLoading ? 'Processing...' : `Pay $${amount.toFixed(2)}`}
      </button>
    </div>
  );
};

// components/MobilePaymentScreen.tsx (React Native)
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';

interface MobilePaymentScreenProps {
  amount: number;
  onPaymentSuccess: (result: PaymentResult) => void;
  config: SquareConfig;
}

export const MobilePaymentScreen: React.FC<MobilePaymentScreenProps> = ({
  amount,
  onPaymentSuccess,
  config,
}) => {
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { processPayment } = useSquarePayment(config);

  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\s/g, '');
    const match = cleaned.match(/(\d{0,4})(\d{0,4})(\d{0,4})(\d{0,4})/);
    if (match) {
      return [match[1], match[2], match[3], match[4]]
        .filter(Boolean)
        .join(' ');
    }
    return text;
  };

  const formatExpiryDate = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      return `${cleaned.slice(0, 2)}/${cleaned.slice(2, 4)}`;
    }
    return cleaned;
  };

  const handlePayment = async () => {
    if (!cardNumber || !expiryDate || !cvv) {
      Alert.alert('Error', 'Please fill in all card details');
      return;
    }

    setIsLoading(true);

    try {
      const cardDetails = {
        cardNumber: cardNumber.replace(/\s/g, ''),
        expiryMonth: expiryDate.split('/')[0],
        expiryYear: expiryDate.split('/')[1],
        cvv,
      };

      const result = await processPayment(amount, cardDetails);
      
      if (result) {
        onPaymentSuccess(result);
        Alert.alert('Success', 'Payment processed successfully!');
      }
    } catch (error) {
      Alert.alert('Error', 'Payment processing failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Payment Details</Text>
      <Text style={styles.amount}>Amount: ${amount.toFixed(2)}</Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Card Number</Text>
        <TextInput
          style={styles.input}
          value={cardNumber}
          onChangeText={(text) => setCardNumber(formatCardNumber(text))}
          placeholder="1234 5678 9012 3456"
          keyboardType="numeric"
          maxLength={19}
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputContainer, styles.halfWidth]}>
          <Text style={styles.label}>Expiry Date</Text>
          <TextInput
            style={styles.input}
            value={expiryDate}
            onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
            placeholder="MM/YY"
            keyboardType="numeric"
            maxLength={5}
          />
        </View>

        <View style={[styles.inputContainer, styles.halfWidth]}>
          <Text style={styles.label}>CVV</Text>
          <TextInput
            style={styles.input}
            value={cvv}
            onChangeText={setCvv}
            placeholder="123"
            keyboardType="numeric"
            maxLength={4}
            secureTextEntry
          />
        </View>
      </View>

      <TouchableOpacity
        style={[styles.payButton, isLoading && styles.payButtonDisabled]}
        onPress={handlePayment}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.payButtonText}>
            Pay ${amount.toFixed(2)}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  amount: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  payButton: {
    backgroundColor: '#0066cc',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  payButtonDisabled: {
    opacity: 0.6,
  },
  payButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

// api/payments.ts (Backend API route example)
import { NextApiRequest, NextApiResponse } from 'next.js';
import { SquarePaymentService } from '../services/squareService';

const squareService = new SquarePaymentService({
  applicationId: process.env.SQUARE_APPLICATION_ID!,
  locationId: process.env.SQUARE_LOCATION_ID!,
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { sourceId, amount, currency = 'USD', locationId, referenceId, note } = req.body;

    const paymentRequest: PaymentRequest = {
      amount,
      currency,
      sourceId,
      locationId,
      referenceId,
      note,
    };

    const result = await squareService.createPayment(paymentRequest);
    res.status(200).json(result);
  } catch (error) {
    console.error('Payment processing error:', error);
    res.status(500).json({ 
      error: 'Payment processing failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Example usage in CRM component
/*
import { SquarePaymentForm } from './components/SquarePaymentForm';

const CRMPaymentPage = () => {
  const config = {
    applicationId: 'your-square-app-id',
    locationId: 'your-location-id',
    environment: 'sandbox' as const,
  };

  const handlePaymentSuccess = (result: PaymentResult) => {
    console.log('Payment successful:', result);
    // Update CRM records, send confirmation, etc.
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    // Show error message to user
  };

  return (
    <div>
      <h1>Process Payment</h1>
      <SquarePaymentForm
        amount={100.00}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
        config={config}
      />
    </div>
  );
};
*/