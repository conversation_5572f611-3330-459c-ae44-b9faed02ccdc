import axios from 'axios';

const XANO_BASE_URL = 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ';

export async function xanoLogin(email: string, password: string) {
  const res = await axios.post(`${XANO_BASE_URL}/auth/login`, { email, password });
  return res.data; // Should include authToken or JWT
}

export async function xanoGetMe(token: string) {
  const res = await axios.get(`${XANO_BASE_URL}/auth/me`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
} 