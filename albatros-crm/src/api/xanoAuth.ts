import axios from 'axios';
import { LoginRequest, LoginResponse, User } from '../types/xano';

const XANO_BASE_URL = 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ';

export async function xanoLogin(email: string, password: string): Promise<LoginResponse> {
  try {
    const res = await axios.post(`${XANO_BASE_URL}/auth/login`, { email, password });
    return {
      success: true,
      token: res.data.authToken || res.data.token || res.data.access_token,
      user: res.data.user || res.data
    };
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Login failed');
  }
}

export async function xanoGetMe(token: string): Promise<User> {
  try {
    const res = await axios.get(`${XANO_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return res.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to get user info');
  }
}

export async function xanoSignup(email: string, password: string): Promise<LoginResponse> {
  try {
    const res = await axios.post(`${XANO_BASE_URL}/auth/signup`, { email, password });
    return {
      success: true,
      token: res.data.authToken || res.data.token || res.data.access_token,
      user: res.data.user || res.data
    };
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Signup failed');
  }
}

export async function xanoForgotPassword(email: string): Promise<void> {
  try {
    await axios.post(`${XANO_BASE_URL}/auth/forgot-password`, { email });
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Password reset failed');
  }
}