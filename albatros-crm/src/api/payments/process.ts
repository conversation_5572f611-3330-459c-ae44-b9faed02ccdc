import { Client, Environment } from 'square';
import { NextApiRequest, NextApiResponse } from 'next';

// Type declaration for the 'square' module
declare module 'square' {
  export class Client {
    constructor(config: { accessToken: string; environment: Environment });
    paymentsApi: {
      createPayment(payment: any): Promise<{ result: any }>;
    };
  }
  export enum Environment {
    Sandbox = 'sandbox',
    Production = 'production',
  }
}

// Initialize Square client
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN || 'EAAAl7LWueAF8jPW7mvI8ju8WvIwrUTTwGlIA1agrlkx_rTjiOtYJIXk-yVFud2a',
  environment: Environment.Sandbox,
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { sourceId, amount, currency, locationId } = req.body;

  if (!sourceId || !amount || !currency || !locationId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const payment = await squareClient.paymentsApi.createPayment({
      sourceId,
      amountMoney: {
        amount,
        currency,
      },
      locationId,
      idempotencyKey: Date.now().toString(),
    });

    return res.status(200).json(payment.result);
  } catch (error) {
    console.error('Payment processing error:', error);
    return res.status(500).json({ error: 'Payment processing failed' });
  }
} 