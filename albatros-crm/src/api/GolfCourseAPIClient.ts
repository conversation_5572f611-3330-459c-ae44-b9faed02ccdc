// GolfCourseAPIClient.ts
// XANO API Client for Albatross Golf CRM
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  User,
  Golfer,
  GolfCourse,
  TeeTime,
  ProShopItem,
  FoodAndBeverageItem,
  Transaction,
  XanoApiResponse,
  XanoApiError,
  CreateGolferRequest,
  UpdateGolferRequest,
  CreateUserRequest,
  UpdateUserRequest,
  BookTeeTimeRequest,
  CreateProShopItemRequest,
  CreateFoodItemRequest,
  BaseQueryParams,
  DateRangeParams,
  GolfCourseParams,
  LoginRequest,
  LoginResponse,
  UserPermissions
} from '../types/xano';

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: {
    'Content-Type': string;
    'Authorization'?: string;
  };
}

const DEFAULT_CONFIG: ApiConfig = {
  baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export class GolfCourseAPIClient {
  private config: ApiConfig;
  private axiosInstance;

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.axiosInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });
  }

  setAuthToken(token: string) {
    this.config.headers.Authorization = `Bearer ${token}`;
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.config.headers.Authorization;
    delete this.axiosInstance.defaults.headers['Authorization'];
  }

  getAuthToken(): string | undefined {
    const authHeader = this.config.headers.Authorization;
    return authHeader ? authHeader.replace('Bearer ', '') : undefined;
  }

  // --- Core request method ---
  async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    options: {
      data?: any;
      params?: Record<string, any>;
      headers?: Record<string, string>;
      timeout?: number;
    } = {}
  ): Promise<XanoApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method,
        url: endpoint,
        data: options.data,
        params: options.params,
        headers: options.headers,
        timeout: options.timeout,
      });

      // Handle XANO response format - data might be directly in response.data
      const responseData = response.data;

      return {
        success: true,
        data: responseData,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: response.headers['x-request-id'] || '',
          version: response.headers['x-api-version'] || '1.0',
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.handleApiError(error),
      };
    }
  }

  // --- Auth Endpoints ---
  async login(data: LoginRequest): Promise<XanoApiResponse<LoginResponse>> {
    return this.request('POST', '/auth/login', { data });
  }

  async refresh(data: { refresh_token: string }): Promise<XanoApiResponse<LoginResponse>> {
    return this.request('POST', '/auth/refresh', { data });
  }

  async logout(): Promise<XanoApiResponse<void>> {
    return this.request('POST', '/auth/logout');
  }

  async forgotPassword(data: { email: string }): Promise<XanoApiResponse<void>> {
    return this.request('POST', '/auth/forgot-password', { data });
  }

  async resetPassword(data: { email: string; token: string; password: string }): Promise<XanoApiResponse<void>> {
    return this.request('POST', '/auth/reset-password', { data });
  }

  async verifyToken(): Promise<XanoApiResponse<User>> {
    return this.request('GET', '/auth/me');
  }

  // --- User Endpoints ---
  async getUsers(params: BaseQueryParams & GolfCourseParams = {}): Promise<XanoApiResponse<User[]>> {
    return this.request('GET', '/user', { params });
  }

  async getUser(id: number): Promise<XanoApiResponse<User>> {
    return this.request('GET', `/user/${id}`);
  }

  async createUser(data: CreateUserRequest): Promise<XanoApiResponse<User>> {
    return this.request('POST', '/user', { data });
  }

  async updateUser(id: number, data: UpdateUserRequest): Promise<XanoApiResponse<User>> {
    return this.request('PUT', `/user/${id}`, { data });
  }

  async deleteUser(id: number): Promise<XanoApiResponse<void>> {
    return this.request('DELETE', `/user/${id}`);
  }

  async updateUserPermissions(id: number, permissions: UserPermissions): Promise<XanoApiResponse<User>> {
    return this.request('PUT', `/user/${id}`, { data: permissions });
  }

  // --- Golfer Endpoints ---
  async getGolfers(params: BaseQueryParams & GolfCourseParams = {}): Promise<XanoApiResponse<Golfer[]>> {
    return this.request('GET', '/golfer', { params });
  }

  async getGolfer(id: number): Promise<XanoApiResponse<Golfer>> {
    return this.request('GET', `/golfer/${id}`);
  }

  async createGolfer(data: CreateGolferRequest): Promise<XanoApiResponse<Golfer>> {
    return this.request('POST', '/golfer', { data });
  }

  async updateGolfer(id: number, data: UpdateGolferRequest): Promise<XanoApiResponse<Golfer>> {
    return this.request('PUT', `/golfer/${id}`, { data });
  }

  async getGolferTeeTimes(id: number, params: DateRangeParams = {}): Promise<XanoApiResponse<TeeTime[]>> {
    return this.request('GET', `/golfer/${id}/tee_time`, { params });
  }

  async getGolferTransactions(id: number, params: BaseQueryParams & DateRangeParams = {}): Promise<XanoApiResponse<Transaction[]>> {
    return this.request('GET', `/golfer/${id}/transaction`, { params });
  }

  async addGolferFunds(id: number, data: { amount: number; payment_method: string; description?: string }): Promise<XanoApiResponse<Transaction>> {
    return this.request('POST', `/golfer/${id}/funds`, { data });
  }

  // --- Tee Time Endpoints ---
  async getTeeTimes(params: BaseQueryParams & GolfCourseParams & DateRangeParams = {}): Promise<XanoApiResponse<TeeTime[]>> {
    return this.request('GET', '/tee_time', { params });
  }

  async getTeeTime(id: number): Promise<XanoApiResponse<TeeTime>> {
    return this.request('GET', `/tee_time/${id}`);
  }

  async getTeeTimeAvailability(params: { golf_course_id: number; date: string }): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/tee_time/availability', { params });
  }

  async getAvailableTeeTimes(params: { golf_course_id: number; date: string }): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/tee_time/available', { params });
  }

  async bookTeeTime(data: BookTeeTimeRequest): Promise<XanoApiResponse<TeeTime>> {
    return this.request('POST', '/tee_time', { data });
  }

  async updateTeeTime(id: number, data: Partial<BookTeeTimeRequest>): Promise<XanoApiResponse<TeeTime>> {
    return this.request('PUT', `/tee_time/${id}`, { data });
  }

  async cancelTeeTime(id: number, reason?: string): Promise<XanoApiResponse<void>> {
    return this.request('PUT', `/tee_time/${id}/cancel`, { data: { reason } });
  }

  async modifyTeeTime(id: number, data: any): Promise<XanoApiResponse<TeeTime>> {
    return this.request('PUT', `/tee_time/${id}`, { data });
  }

  async getWaitlist(golf_course_id: number, date?: string): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/tee_time/waitlist', { params: { golf_course_id, date } });
  }

  async joinWaitlist(data: any): Promise<XanoApiResponse<any>> {
    return this.request('POST', '/tee_time/waitlist', { data });
  }

  async getTeeTimeAnalytics(params: GolfCourseParams & DateRangeParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/tee_time/analytics', { params });
  }

  // --- Pro Shop Endpoints ---
  async getProShopItems(params: BaseQueryParams & GolfCourseParams = {}): Promise<XanoApiResponse<ProShopItem[]>> {
    return this.request('GET', '/pro_shop_item', { params });
  }

  async getProShopItem(id: number): Promise<XanoApiResponse<ProShopItem>> {
    return this.request('GET', `/pro_shop_item/${id}`);
  }

  async createProShopItem(data: CreateProShopItemRequest): Promise<XanoApiResponse<ProShopItem>> {
    return this.request('POST', '/pro_shop_item', { data });
  }

  async updateProShopItem(id: number, data: Partial<CreateProShopItemRequest>): Promise<XanoApiResponse<ProShopItem>> {
    return this.request('PUT', `/pro_shop_item/${id}`, { data });
  }

  async purchaseProShopItems(data: any): Promise<XanoApiResponse<Transaction>> {
    return this.request('POST', '/pro_shop_item/purchase', { data });
  }

  async getProShopInventoryReport(golf_course_id: number): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/pro_shop_item/inventory', { params: { golf_course_id } });
  }

  async updateProShopInventory(id: number, data: any): Promise<XanoApiResponse<ProShopItem>> {
    return this.request('PUT', `/pro_shop_item/${id}/inventory`, { data });
  }

  // --- Food & Beverage Endpoints ---
  async getFoodBeverageItems(params: BaseQueryParams & GolfCourseParams = {}): Promise<XanoApiResponse<FoodAndBeverageItem[]>> {
    return this.request('GET', '/food_and_beverage_item', { params });
  }

  async createFoodItem(data: CreateFoodItemRequest): Promise<XanoApiResponse<FoodAndBeverageItem>> {
    return this.request('POST', '/food_and_beverage_item', { data });
  }

  async createFoodOrder(data: any): Promise<XanoApiResponse<any>> {
    return this.request('POST', '/food_and_beverage_order', { data });
  }

  async getFoodOrders(params: BaseQueryParams & GolfCourseParams = {}): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/food_and_beverage_order', { params });
  }

  async updateFoodOrderStatus(id: number, status: string, employee_id?: number): Promise<XanoApiResponse<any>> {
    return this.request('PUT', `/food_and_beverage_order/${id}`, { data: { status, employee_id } });
  }

  async getFoodMenu(golf_course_id: number): Promise<XanoApiResponse<any[]>> {
    return this.request('GET', '/food_and_beverage_item', { params: { golf_course_id } });
  }

  // --- Dashboard Endpoints ---
  async getDashboardOverview(params: GolfCourseParams & { timeframe?: string }): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/dashboard/overview', { params });
  }

  async getDashboardMetrics(params: GolfCourseParams & DateRangeParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/dashboard/metrics', { params });
  }

  async getRealtimeDashboard(params: GolfCourseParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/dashboard/realtime', { params });
  }

  async getWeatherInfo(params: { golf_course_id: number }): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/dashboard/weather', { params });
  }

  // --- Analytics Endpoints ---
  async getAnalytics(params: BaseQueryParams & GolfCourseParams & DateRangeParams = {}): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics', { params });
  }

  async getRevenueAnalytics(params: GolfCourseParams & DateRangeParams & { timeView?: string }): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics/revenue', { params });
  }

  async getRevenueReport(params: GolfCourseParams & DateRangeParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics/revenue-report', { params });
  }

  async getBookingAnalytics(params: GolfCourseParams & DateRangeParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics/bookings', { params });
  }

  async getGolferAnalytics(params: GolfCourseParams & DateRangeParams): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics/golfers', { params });
  }

  async getCustomerInsights(golf_course_id: number): Promise<XanoApiResponse<any>> {
    return this.request('GET', '/analytics/customer-insights', { params: { golf_course_id } });
  }

  async generateCustomReport(data: any): Promise<XanoApiResponse<any>> {
    return this.request('POST', '/analytics/custom-report', { data });
  }

  // --- Error Handling ---
  handleApiError(error: any): XanoApiError {
    if (error.response) {
      return {
        code: error.response.data?.code || 'API_ERROR',
        message: error.response.data?.message || 'An API error occurred',
        details: error.response.data?.details,
        field_errors: error.response.data?.field_errors,
      };
    } else if (error.request) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network error occurred',
      };
    } else {
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred',
      };
    }
  }

  // --- Add more modules/methods as needed, following the spec ---
}

// --- Usage Example ---
// const apiClient = new GolfCourseAPIClient({ baseURL: 'https://api.golfcourse.com/v1' });
// apiClient.setAuthToken('your-jwt-token');
// const users = await apiClient.getUsers(); 