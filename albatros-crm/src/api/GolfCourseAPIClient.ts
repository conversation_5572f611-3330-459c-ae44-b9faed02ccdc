// GolfCourseAPIClient.ts
// Auto-generated from golf_course_api_spec.md
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

// --- Type Definitions (imported from spec) ---
// (For brevity, only a few are shown here. In a real file, paste all from the spec.)
// ... (Paste all interfaces/enums from the spec here) ...

// (For demonstration, a few are included below. You should paste all from the spec.)
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: {
    'Content-Type': string;
    'Authorization'?: string;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  pagination?: PaginationInfo;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  field_errors?: FieldError[];
}

export interface FieldError {
  field: string;
  message: string;
  code: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ResponseMeta {
  timestamp: string;
  request_id: string;
  version: string;
}

// ... (Paste all other interfaces/enums from the spec here) ...

const DEFAULT_CONFIG: ApiConfig = {
  baseURL: 'https://api.golfcourse.com/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export class GolfCourseAPIClient {
  private config: ApiConfig;
  private axiosInstance;

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.axiosInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });
  }

  setAuthToken(token: string) {
    this.config.headers.Authorization = `Bearer ${token}`;
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.config.headers.Authorization;
    delete this.axiosInstance.defaults.headers['Authorization'];
  }

  // --- Core request method ---
  async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    options: {
      data?: any;
      params?: Record<string, any>;
      headers?: Record<string, string>;
      timeout?: number;
    } = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method,
        url: endpoint,
        data: options.data,
        params: options.params,
        headers: options.headers,
        timeout: options.timeout,
      });
      return {
        success: true,
        data: response.data,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: response.headers['x-request-id'] || '',
          version: response.headers['x-api-version'] || '1.0',
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: this.handleApiError(error),
      };
    }
  }

  // --- Auth Endpoints ---
  async login(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/login', { data });
  }
  async refresh(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/refresh', { data });
  }
  async logout(): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/logout');
  }
  async forgotPassword(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/forgot-password', { data });
  }
  async resetPassword(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/reset-password', { data });
  }
  async verifyToken(): Promise<ApiResponse<any>> {
    return this.request('GET', '/auth/verify-token');
  }

  // --- User Endpoints ---
  async getUsers(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/users', { params });
  }
  async getUser(id: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/users/${id}`);
  }
  async createUser(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/users', { data });
  }
  async updateUser(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/users/${id}`, { data });
  }
  async deleteUser(id: string): Promise<ApiResponse<any>> {
    return this.request('DELETE', `/users/${id}`);
  }
  async updateUserPermissions(id: string, permissions: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/users/${id}/permissions`, { data: permissions });
  }

  // --- Golfer Endpoints ---
  async getGolfers(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/golfers', { params });
  }
  async getGolfer(id: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/golfers/${id}`);
  }
  async createGolfer(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/golfers', { data });
  }
  async updateGolfer(id: number, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/golfers/${id}`, { data });
  }
  async deleteGolfer(id: number): Promise<ApiResponse<any>> {
    return this.request('DELETE', `/golfers/${id}`);
  }
  async getGolferTeeTimes(id: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', `/golfers/${id}/tee-times`, { params });
  }
  async getGolferTransactions(id: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', `/golfers/${id}/transactions`, { params });
  }
  async addGolferFunds(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/golfers/${id}/funds`, { data });
  }

  // --- Tee Time Endpoints ---
  async getTeeTimes(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times', { params });
  }
  async getTeeTimeAvailability(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times/availability', { params });
  }
  async bookTeeTime(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/tee-times/book', { data });
  }
  async cancelTeeTime(id: string, reason?: string): Promise<ApiResponse<any>> {
    return this.request('PUT', `/tee-times/${id}/cancel`, { data: { reason } });
  }
  async modifyTeeTime(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/tee-times/${id}/modify`, { data });
  }
  async getWaitlist(golf_course_id: string, date?: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times/waitlist', { params: { golf_course_id, date } });
  }
  async joinWaitlist(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/tee-times/waitlist', { data });
  }

  // --- Pro Shop Endpoints ---
  async getProShopItems(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/pro-shop/items', { params });
  }
  async getProShopItem(id: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/pro-shop/items/${id}`);
  }
  async createProShopItem(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/pro-shop/items', { data });
  }
  async updateProShopItem(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/pro-shop/items/${id}`, { data });
  }
  async purchaseProShopItems(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/pro-shop/purchase', { data });
  }
  async getProShopInventoryReport(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/pro-shop/inventory', { params: { golf_course_id } });
  }
  async updateProShopInventory(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/pro-shop/items/${id}/inventory`, { data });
  }

  // --- Food & Beverage Endpoints ---
  async getFoodBeverageItems(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/items', { params });
  }
  async createFoodOrder(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/food-beverage/order', { data });
  }
  async getFoodOrders(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/orders', { params });
  }
  async updateFoodOrderStatus(id: string, status: string, employee_id?: string): Promise<ApiResponse<any>> {
    return this.request('PUT', `/food-beverage/orders/${id}/status`, { data: { status, employee_id } });
  }
  async getFoodMenu(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/menu', { params: { golf_course_id } });
  }

  // --- Analytics Endpoints ---
  async getRevenueReport(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/revenue', { params });
  }
  async getTeeTimeAnalytics(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/tee-times', { params });
  }
  async getCustomerInsights(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/customer-insights', { params: { golf_course_id } });
  }
  async generateCustomReport(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/analytics/custom-report', { data });
  }

  // --- Error Handling ---
  handleApiError(error: any): ApiError {
    if (error.response) {
      return {
        code: error.response.data?.code || 'API_ERROR',
        message: error.response.data?.message || 'An API error occurred',
        details: error.response.data?.details,
      };
    } else if (error.request) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network error occurred',
      };
    } else {
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred',
      };
    }
  }

  // --- Add more modules/methods as needed, following the spec ---
}

// --- Usage Example ---
// const apiClient = new GolfCourseAPIClient({ baseURL: 'https://api.golfcourse.com/v1' });
// apiClient.setAuthToken('your-jwt-token');
// const users = await apiClient.getUsers(); 