// Centralized API Client Instance
import { GolfCourseAPIClient } from './GolfCourseAPIClient';

// Create a singleton instance of the API client
export const apiClient = new GolfCourseAPIClient({
  baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper function to set auth token across the app
export const setAuthToken = (token: string) => {
  apiClient.setAuthToken(token);
};

// Helper function to clear auth token
export const clearAuthToken = () => {
  apiClient.clearAuthToken();
};

// Export the client for use in hooks and components
export default apiClient;
