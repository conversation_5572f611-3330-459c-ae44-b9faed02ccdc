import React, { useState, useEffect, createContext, useContext } from 'react';
import { xanoLogin, xanoGetMe } from '../xanoAuth';

const TOKEN_KEY = 'xano_token';

export interface XanoAuthContextType {
  token: string | null;
  user: any;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  signup: (email: string, password: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  fetchMe: () => Promise<void>;
}

const XanoAuthContext = createContext<XanoAuthContextType | undefined>(undefined);

export function XanoAuthProvider({ children }: { children: React.ReactNode }) {
  const [token, setToken] = useState<string | null>(() => localStorage.getItem(TOKEN_KEY));
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (token) {
      localStorage.setItem(TOKEN_KEY, token);
      fetchMe();
    } else {
      localStorage.removeItem(TOKEN_KEY);
      setUser(null);
    }
    // eslint-disable-next-line
  }, [token]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await xanoLogin(email, password);
      setToken(data.authToken || data.token || data.jwt || data.access_token);
      setUser(data.user || null);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message);
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem(TOKEN_KEY);
  };

  const signup = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      });
      if (!res.ok) throw new Error('Signup failed');
      await login(email, password);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });
      if (!res.ok) throw new Error('Password reset failed');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchMe = async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const data = await xanoGetMe(token);
      setUser(data);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message);
      setToken(null);
      setUser(null);
      localStorage.removeItem(TOKEN_KEY);
    } finally {
      setLoading(false);
    }
  };

  const value: XanoAuthContextType = {
    token, user, loading, error, login, logout, signup, resetPassword, fetchMe
  };

  return <XanoAuthContext.Provider value={value}>{children}</XanoAuthContext.Provider>;
}

export function useXanoAuth() {
  const ctx = useContext(XanoAuthContext);
  if (!ctx) throw new Error('useXanoAuth must be used within XanoAuthProvider');
  return ctx;
} 