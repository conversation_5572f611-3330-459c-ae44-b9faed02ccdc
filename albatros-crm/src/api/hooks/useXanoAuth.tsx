import React, { useState, useEffect, createContext, useContext } from 'react';
import { xanoLogin, xanoGetMe, xanoSignup, xanoForgotPassword } from '../xanoAuth';
import { User } from '../../types/xano';
import { setAuthToken, clearAuthToken } from '../apiClient';

const TOKEN_KEY = 'xano_token';

export interface XanoAuthContextType {
  token: string | null;
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  signup: (email: string, password: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  fetchMe: () => Promise<void>;
  isAuthenticated: boolean;
}

const XanoAuthContext = createContext<XanoAuthContextType | undefined>(undefined);

export function XanoAuthProvider({ children }: { children: React.ReactNode }) {
  const [token, setToken] = useState<string | null>(() => localStorage.getItem(TOKEN_KEY));
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (token) {
      localStorage.setItem(TOKEN_KEY, token);
      setAuthToken(token); // Set token in API client
      fetchMe();
    } else {
      localStorage.removeItem(TOKEN_KEY);
      clearAuthToken(); // Clear token from API client
      setUser(null);
    }
    // eslint-disable-next-line
  }, [token]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await xanoLogin(email, password);
      if (response.token) {
        setToken(response.token);
        setUser(response.user);
      } else {
        throw new Error('No token received from login');
      }
    } catch (err: any) {
      setError(err.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem(TOKEN_KEY);
    clearAuthToken(); // Clear token from API client
  };

  const signup = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await xanoSignup(email, password);
      if (response.token) {
        setToken(response.token);
        setUser(response.user);
      } else {
        throw new Error('No token received from signup');
      }
    } catch (err: any) {
      setError(err.message || 'Signup failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    setLoading(true);
    setError(null);
    try {
      await xanoForgotPassword(email);
    } catch (err: any) {
      setError(err.message || 'Password reset failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchMe = async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const data = await xanoGetMe(token);
      setUser(data);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message);
      setToken(null);
      setUser(null);
      localStorage.removeItem(TOKEN_KEY);
    } finally {
      setLoading(false);
    }
  };

  const value: XanoAuthContextType = {
    token,
    user,
    loading,
    error,
    login,
    logout,
    signup,
    resetPassword,
    fetchMe,
    isAuthenticated: !!token && !!user
  };

  return <XanoAuthContext.Provider value={value}>{children}</XanoAuthContext.Provider>;
}

export function useXanoAuth() {
  const ctx = useContext(XanoAuthContext);
  if (!ctx) throw new Error('useXanoAuth must be used within XanoAuthProvider');
  return ctx;
} 