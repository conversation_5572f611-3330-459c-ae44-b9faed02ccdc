import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../apiClient';
import { ProShopItem, BaseQueryParams, GolfCourseParams, CreateProShopItemRequest } from '../../types/xano';

export function useProShopItems(params: BaseQueryParams & GolfCourseParams = {}) {
  return useQuery({
    queryKey: ['pro-shop-items', params],
    queryFn: async () => {
      const res = await apiClient.getProShopItems(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch pro shop items');
      return res.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProShopItem(id: number) {
  return useQuery({
    queryKey: ['proShopItem', id],
    queryFn: async () => {
      const res = await apiClient.getProShopItem(id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch pro shop item');
      return res.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateProShopItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateProShopItemRequest) => {
      const res = await apiClient.createProShopItem(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to create pro shop item');
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proShopItems'] });
    },
  });
}

export function useUpdateProShopItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<CreateProShopItemRequest> }) => {
      const res = await apiClient.updateProShopItem(id, data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to update pro shop item');
      return res.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['proShopItems'] });
      queryClient.invalidateQueries({ queryKey: ['proShopItem', variables.id] });
    },
  });
}