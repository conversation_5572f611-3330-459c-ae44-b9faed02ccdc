import { useQuery } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

const apiClient = new GolfCourseAPIClient();

export function useProShopItems(params: Record<string, any> = {}) {
  return useQuery({
    queryKey: ['pro-shop-items', params],
    queryFn: async () => {
      const res = await apiClient.getProShopItems(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch pro shop items');
      return res.data;
    },
  });
} 