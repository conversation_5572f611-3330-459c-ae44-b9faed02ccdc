import { useQuery } from '@tanstack/react-query';
import { apiClient } from '../apiClient';
import { BaseQueryParams, GolfCourseParams, DateRangeParams } from '../../types/xano';

export interface DashboardMetrics {
  totalRevenue: number;
  totalBookings: number;
  totalGolfers: number;
  averageRating: number;
  bookingRate: number;
  memberCount: number;
  revenueChange: number;
  bookingChange: number;
  golferChange: number;
}

export interface DashboardOverview {
  metrics: DashboardMetrics;
  recentTeeTimes: any[];
  upcomingEvents: any[];
  inventoryAlerts: any[];
  weatherInfo?: {
    temperature: number;
    conditions: string;
    windSpeed: number;
    precipitation: number;
  };
}

export function useDashboardOverview(params: GolfCourseParams & { timeframe?: 'daily' | 'weekly' | 'monthly' }) {
  return useQuery({
    queryKey: ['dashboardOverview', params],
    queryFn: async () => {
      const res = await apiClient.getDashboardOverview(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch dashboard overview');
      return res.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000,
    refetchInterval: 60 * 1000, // Refetch every minute for real-time dashboard
  });
}

export function useDashboardMetrics(params: GolfCourseParams & DateRangeParams) {
  return useQuery({
    queryKey: ['dashboardMetrics', params],
    queryFn: async () => {
      const res = await apiClient.getDashboardMetrics(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch dashboard metrics');
      return res.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,
  });
}

export function useRevenueAnalytics(params: GolfCourseParams & DateRangeParams & { timeView?: 'daily' | 'weekly' | 'monthly' }) {
  return useQuery({
    queryKey: ['revenueAnalytics', params],
    queryFn: async () => {
      const res = await apiClient.getRevenueAnalytics(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch revenue analytics');
      return res.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for analytics
    gcTime: 30 * 60 * 1000,
  });
}

export function useBookingAnalytics(params: GolfCourseParams & DateRangeParams) {
  return useQuery({
    queryKey: ['bookingAnalytics', params],
    queryFn: async () => {
      const res = await apiClient.getBookingAnalytics(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch booking analytics');
      return res.data;
    },
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

export function useGolferAnalytics(params: GolfCourseParams & DateRangeParams) {
  return useQuery({
    queryKey: ['golferAnalytics', params],
    queryFn: async () => {
      const res = await apiClient.getGolferAnalytics(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch golfer analytics');
      return res.data;
    },
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

export function useWeatherInfo(params: { golf_course_id: number }) {
  return useQuery({
    queryKey: ['weatherInfo', params],
    queryFn: async () => {
      const res = await apiClient.getWeatherInfo(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch weather info');
      return res.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for weather
    gcTime: 30 * 60 * 1000,
    refetchInterval: 15 * 60 * 1000, // Update weather every 15 minutes
  });
}

// Hook for real-time dashboard updates
export function useRealtimeDashboard(params: GolfCourseParams) {
  return useQuery({
    queryKey: ['realtimeDashboard', params],
    queryFn: async () => {
      const res = await apiClient.getRealtimeDashboard(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch realtime dashboard');
      return res.data;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000,
    refetchInterval: 30 * 1000, // Update every 30 seconds
  });
}
