import { useQuery } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

const apiClient = new GolfCourseAPIClient();

export function useTeeTimes(params: Record<string, any> = {}) {
  return useQuery({
    queryKey: ['tee-times', params],
    queryFn: async () => {
      const res = await apiClient.getTeeTimes(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch tee times');
      return res.data;
    },
  });
} 