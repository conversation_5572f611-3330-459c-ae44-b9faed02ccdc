import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../apiClient';
import { TeeTime, BaseQueryParams, GolfCourseParams, DateRangeParams, BookTeeTimeRequest } from '../../types/xano';

export function useTeeTimes(params: BaseQueryParams & GolfCourseParams & DateRangeParams = {}) {
  return useQuery({
    queryKey: ['teeTimes', params],
    queryFn: async () => {
      const res = await apiClient.getTeeTimes(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch tee times');
      return res.data || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for real-time booking data
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for live availability
  });
}

export function useTeeTime(id: number) {
  return useQuery({
    queryKey: ['teeTime', id],
    queryFn: async () => {
      const res = await apiClient.getTeeTime(id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch tee time');
      return res.data;
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useAvailableTeeTimes(params: { golf_course_id: number; date: string }) {
  return useQuery({
    queryKey: ['availableTeeTimes', params],
    queryFn: async () => {
      const res = await apiClient.getAvailableTeeTimes(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch available tee times');
      return res.data || [];
    },
    enabled: !!params.golf_course_id && !!params.date,
    staleTime: 1 * 60 * 1000, // 1 minute for availability
    refetchInterval: 15 * 1000, // Refetch every 15 seconds
  });
}

export function useBookTeeTime() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BookTeeTimeRequest) => {
      const res = await apiClient.bookTeeTime(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to book tee time');
      return res.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch tee times
      queryClient.invalidateQueries({ queryKey: ['teeTimes'] });
      queryClient.invalidateQueries({ queryKey: ['availableTeeTimes'] });
      // Invalidate golfer's tee times
      queryClient.invalidateQueries({ queryKey: ['golfer', variables.golfer_id, 'teeTimes'] });
    },
  });
}