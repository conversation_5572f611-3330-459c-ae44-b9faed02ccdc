import { useQuery } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

const apiClient = new GolfCourseAPIClient();

export function useFoodBeverageItems(params: Record<string, any> = {}) {
  return useQuery({
    queryKey: ['food-beverage-items', params],
    queryFn: async () => {
      const res = await apiClient.getFoodBeverageItems(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch food & beverage items');
      return res.data;
    },
  });
} 