import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../apiClient';
import { FoodAndBeverageItem, BaseQueryParams, GolfCourseParams, CreateFoodItemRequest } from '../../types/xano';

export function useFoodBeverageItems(params: BaseQueryParams & GolfCourseParams = {}) {
  return useQuery({
    queryKey: ['food-beverage-items', params],
    queryFn: async () => {
      const res = await apiClient.getFoodBeverageItems(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch food & beverage items');
      return res.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useFoodMenu(golf_course_id: number) {
  return useQuery({
    queryKey: ['foodMenu', golf_course_id],
    queryFn: async () => {
      const res = await apiClient.getFoodMenu(golf_course_id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch food menu');
      return res.data || [];
    },
    enabled: !!golf_course_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useCreateFoodItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFoodItemRequest) => {
      const res = await apiClient.createFoodItem(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to create food item');
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['foodBeverageItems'] });
      queryClient.invalidateQueries({ queryKey: ['foodMenu'] });
    },
  });
}

export function useFoodOrders(params: BaseQueryParams & GolfCourseParams = {}) {
  return useQuery({
    queryKey: ['foodOrders', params],
    queryFn: async () => {
      const res = await apiClient.getFoodOrders(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch food orders');
      return res.data || [];
    },
    staleTime: 1 * 60 * 1000, // 1 minute for real-time orders
    gcTime: 5 * 60 * 1000,
  });
}

export function useCreateFoodOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const res = await apiClient.createFoodOrder(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to create food order');
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['foodOrders'] });
    },
  });
}