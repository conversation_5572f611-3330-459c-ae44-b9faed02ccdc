import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

interface GoogleReview {
  id: string;
  author: string;
  rating: number;
  comment: string;
  date: string;
  helpful: number;
  flagged: boolean;
  source: 'google';
}

interface GoogleBusinessConfig {
  placeId: string;
  apiKey: string;
  businessName: string;
  lastSync: string;
  autoSync: boolean;
}

interface UseGoogleReviewsReturn {
  reviews: GoogleReview[];
  config: GoogleBusinessConfig;
  loading: boolean;
  error: string | null;
  syncReviews: () => Promise<void>;
  updateConfig: (config: Partial<GoogleBusinessConfig>) => void;
  sendReviewRequest: (golferId: string, type: 'email' | 'sms') => Promise<void>;
}

export const useGoogleReviews = (courseId: string): UseGoogleReviewsReturn => {
  const [reviews, setReviews] = useState<GoogleReview[]>([]);
  const [config, setConfig] = useState<GoogleBusinessConfig>({
    placeId: '',
    apiKey: '',
    businessName: 'Albatros Golf Course',
    lastSync: new Date().toISOString(),
    autoSync: true,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load configuration from localStorage or API
  useEffect(() => {
    const savedConfig = localStorage.getItem(`google-business-config-${courseId}`);
    if (savedConfig) {
      try {
        setConfig(JSON.parse(savedConfig));
      } catch (err) {
        console.error('Error loading Google Business config:', err);
      }
    }
  }, [courseId]);

  // Save configuration to localStorage
  const saveConfig = useCallback((newConfig: GoogleBusinessConfig) => {
    localStorage.setItem(`google-business-config-${courseId}`, JSON.stringify(newConfig));
  }, [courseId]);

  // Update configuration
  const updateConfig = useCallback((newConfig: Partial<GoogleBusinessConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
    saveConfig(updatedConfig);
  }, [config, saveConfig]);

  // Sync reviews from Google Business API
  const syncReviews = useCallback(async () => {
    if (!config.placeId || !config.apiKey) {
      setError('Google Business configuration is incomplete. Please add Place ID and API Key.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // In a real implementation, this would call the Google Business API
      // For now, we'll simulate the API call
      const response = await axios.get(`${process.env.REACT_APP_API_BASE_URL}/google-business/reviews`, {
        params: {
          courseId,
          placeId: config.placeId,
        },
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
        },
      });

      if (response.data.success) {
        setReviews(response.data.data);
        updateConfig({ lastSync: new Date().toISOString() });
      } else {
        setError('Failed to sync reviews from Google Business API');
      }
    } catch (err: any) {
      console.error('Error syncing Google reviews:', err);
      setError(err.response?.data?.message || 'Failed to sync reviews');
    } finally {
      setLoading(false);
    }
  }, [config.placeId, config.apiKey, courseId, updateConfig]);

  // Send review request to golfer
  const sendReviewRequest = useCallback(async (golferId: string, type: 'email' | 'sms') => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_BASE_URL}/reviews/request`, {
        courseId,
        golferId,
        type,
        googleReviewUrl: `https://www.google.com/search?q=${encodeURIComponent(config.businessName)}`,
      });

      if (!response.data.success) {
        setError('Failed to send review request');
      }
    } catch (err: any) {
      console.error('Error sending review request:', err);
      setError(err.response?.data?.message || 'Failed to send review request');
    } finally {
      setLoading(false);
    }
  }, [courseId, config.businessName]);

  // Auto-sync if enabled
  useEffect(() => {
    if (config.autoSync && config.placeId && config.apiKey) {
      const interval = setInterval(syncReviews, 300000); // Sync every 5 minutes
      return () => clearInterval(interval);
    }
  }, [config.autoSync, config.placeId, config.apiKey, syncReviews]);

  return {
    reviews,
    config,
    loading,
    error,
    syncReviews,
    updateConfig,
    sendReviewRequest,
  };
}; 