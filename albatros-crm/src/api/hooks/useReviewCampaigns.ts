import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

interface ReviewCampaign {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'in-app';
  template: string;
  trigger: 'round_completion' | 'manual' | 'scheduled';
  status: 'active' | 'inactive' | 'draft';
  sentCount: number;
  responseRate: number;
  createdAt: string;
  courseId: string;
}

interface CreateCampaignData {
  name: string;
  type: 'email' | 'sms' | 'in-app';
  template: string;
  trigger: 'round_completion' | 'manual' | 'scheduled';
  courseId: string;
}

interface UseReviewCampaignsReturn {
  campaigns: ReviewCampaign[];
  loading: boolean;
  error: string | null;
  createCampaign: (data: CreateCampaignData) => Promise<void>;
  updateCampaign: (id: string, data: Partial<ReviewCampaign>) => Promise<void>;
  deleteCampaign: (id: string) => Promise<void>;
  sendCampaign: (id: string) => Promise<void>;
  getCampaignAnalytics: (id: string) => Promise<any>;
}

export const useReviewCampaigns = (courseId: string): UseReviewCampaignsReturn => {
  const [campaigns, setCampaigns] = useState<ReviewCampaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load campaigns
  const loadCampaigns = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns`, {
        params: { courseId },
      });

      if (response.data.success) {
        setCampaigns(response.data.data);
      } else {
        setError('Failed to load campaigns');
      }
    } catch (err: any) {
      console.error('Error loading campaigns:', err);
      setError(err.response?.data?.message || 'Failed to load campaigns');
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  // Load campaigns on mount
  useEffect(() => {
    loadCampaigns();
  }, [loadCampaigns]);

  // Create new campaign
  const createCampaign = useCallback(async (data: CreateCampaignData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns`, data);

      if (response.data.success) {
        await loadCampaigns(); // Reload campaigns
      } else {
        setError('Failed to create campaign');
      }
    } catch (err: any) {
      console.error('Error creating campaign:', err);
      setError(err.response?.data?.message || 'Failed to create campaign');
    } finally {
      setLoading(false);
    }
  }, [loadCampaigns]);

  // Update campaign
  const updateCampaign = useCallback(async (id: string, data: Partial<ReviewCampaign>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.put(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns/${id}`, data);

      if (response.data.success) {
        setCampaigns(prev => prev.map(campaign => 
          campaign.id === id ? { ...campaign, ...data } : campaign
        ));
      } else {
        setError('Failed to update campaign');
      }
    } catch (err: any) {
      console.error('Error updating campaign:', err);
      setError(err.response?.data?.message || 'Failed to update campaign');
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete campaign
  const deleteCampaign = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.delete(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns/${id}`);

      if (response.data.success) {
        setCampaigns(prev => prev.filter(campaign => campaign.id !== id));
      } else {
        setError('Failed to delete campaign');
      }
    } catch (err: any) {
      console.error('Error deleting campaign:', err);
      setError(err.response?.data?.message || 'Failed to delete campaign');
    } finally {
      setLoading(false);
    }
  }, []);

  // Send campaign
  const sendCampaign = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns/${id}/send`, {
        courseId,
      });

      if (response.data.success) {
        await loadCampaigns(); // Reload to get updated stats
      } else {
        setError('Failed to send campaign');
      }
    } catch (err: any) {
      console.error('Error sending campaign:', err);
      setError(err.response?.data?.message || 'Failed to send campaign');
    } finally {
      setLoading(false);
    }
  }, [courseId, loadCampaigns]);

  // Get campaign analytics
  const getCampaignAnalytics = useCallback(async (id: string) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_BASE_URL}/review-campaigns/${id}/analytics`, {
        params: { courseId },
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error('Failed to get campaign analytics');
      }
    } catch (err: any) {
      console.error('Error getting campaign analytics:', err);
      throw err;
    }
  }, [courseId]);

  return {
    campaigns,
    loading,
    error,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    sendCampaign,
    getCampaignAnalytics,
  };
}; 