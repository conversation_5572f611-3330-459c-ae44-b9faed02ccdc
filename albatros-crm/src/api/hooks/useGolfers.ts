import { useQuery } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

// You may want to export a singleton client from a central file
const apiClient = new GolfCourseAPIClient();

export function useGolfers(params: Record<string, any> = {}) {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      const res = await apiClient.getGolfers(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch golfers');
      return res.data;
    },
  });
} 