import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../apiClient';
import {
  XanoGolfer,
  BaseQueryParams,
  GolfCourseParams,
  CreateGolferRequest,
  UpdateGolferRequest
} from '../../types/xano';

export function useGolfers(params: BaseQueryParams & GolfCourseParams = {}) {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      const res = await apiClient.getGolfers(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch golfers');
      return res.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useGolfer(id: number) {
  return useQuery({
    queryKey: ['golfer', id],
    queryFn: async () => {
      const res = await apiClient.getGolfer(id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch golfer');
      return res.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateGolferRequest) => {
      const res = await apiClient.createGolfer(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to create golfer');
      return res.data;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers list
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
  });
}

export function useUpdateGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateGolferRequest }) => {
      const res = await apiClient.updateGolfer(id, data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to update golfer');
      return res.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch golfers list
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
      // Update the specific golfer cache
      queryClient.invalidateQueries({ queryKey: ['golfer', variables.id] });
    },
  });
}

export function useDeleteGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const res = await apiClient.deleteGolfer(id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to delete golfer');
      return res.data;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers list
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
  });
}