import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

// Define types locally for now
interface XanoGolferCreate {
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

interface XanoGolferUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  address?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

// You may want to export a singleton client from a central file
const apiClient = new GolfCourseAPIClient();

export function useGolfers(params: Record<string, any> = {}) {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      const res = await apiClient.getGolfers(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch golfers');
      return res.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: XanoGolferCreate) => {
      const res = await apiClient.createGolfer(data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to create golfer');
      return res.data;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
  });
}

export function useUpdateGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: XanoGolferUpdate }) => {
      const res = await apiClient.updateGolfer(id, data);
      if (!res.success) throw new Error(res.error?.message || 'Failed to update golfer');
      return res.data;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
  });
}

export function useDeleteGolfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const res = await apiClient.deleteGolfer(id);
      if (!res.success) throw new Error(res.error?.message || 'Failed to delete golfer');
      return res.data;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
  });
}