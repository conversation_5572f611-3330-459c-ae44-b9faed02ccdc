import { useQuery } from '@tanstack/react-query';
import { GolfCourseAPIClient } from '../GolfCourseAPIClient';

const apiClient = new GolfCourseAPIClient();

export function useRevenueReport(params: Record<string, any>) {
  return useQuery({
    queryKey: ['revenue-report', params],
    queryFn: async () => {
      const res = await apiClient.getRevenueReport(params);
      if (!res.success) throw new Error(res.error?.message || 'Failed to fetch revenue report');
      return res.data;
    },
    enabled: !!params?.golf_course_id && !!params?.start_date && !!params?.end_date,
  });
} 