import { createTheme, Theme } from '@mui/material';

/**
 * Global theme configuration for the application
 * Defines colors, typography, and component style overrides
 */
const theme: Theme = createTheme({
  palette: {
    primary: {
      main: '#1ABC9C',
      light: '#20D7B2',      // Lighter shade for hover states
      dark: '#16A085',       // Darker shade for active/pressed states
      contrastText: '#FFFFFF', // Text color to use on primary color background
    },
    secondary: {
      main: '#2C3E50',
      light: '#34495E',
      dark: '#2C3E50',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#F8FAFC',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#1E293B',
      secondary: '#64748B',
    },
    action: {
      hover: 'rgba(26, 188, 156, 0.08)', // Primary color with 8% opacity
      selected: 'rgba(26, 188, 156, 0.16)', // Primary color with 16% opacity
      disabled: 'rgba(0, 0, 0, 0.26)',
      disabledBackground: 'rgba(0, 0, 0, 0.12)',
    },
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    h1: { 
      fontSize: '24px',
      fontWeight: 600,
    },
    h2: { 
      fontSize: '18px',
      fontWeight: 600,
    },
    body1: { fontSize: '14px' },
    button: { fontSize: '14px' },
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 8,
          marginBottom: 4,
          '&.Mui-selected': {
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            '& .MuiListItemIcon-root': {
              color: theme.palette.primary.contrastText,
            },
          },
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
        }),
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
      },
    },
  },
});

export default theme; 