import { useState, useCallback } from 'react';

interface UndoableState<T> {
  past: T[];
  present: T;
  future: T[];
  isInitialState: boolean;
}

export function useUndoableState<T>(initialPresent: T) {
  const [state, setState] = useState<UndoableState<T>>({
    past: [],
    present: initialPresent,
    future: [],
    isInitialState: true
  });

  const canUndo = state.past.length > 0 && !state.isInitialState;
  const canRedo = state.future.length > 0;

  const undo = useCallback(() => {
    setState(currentState => {
      if (!currentState.past.length || currentState.isInitialState) return currentState;

      const previous = currentState.past[currentState.past.length - 1];
      const newPast = currentState.past.slice(0, -1);

      return {
        past: newPast,
        present: previous,
        future: [currentState.present, ...currentState.future],
        isInitialState: false
      };
    });
  }, []);

  const redo = useCallback(() => {
    setState(currentState => {
      if (!currentState.future.length) return currentState;

      const next = currentState.future[0];
      const newFuture = currentState.future.slice(1);

      return {
        past: [...currentState.past, currentState.present],
        present: next,
        future: newFuture,
        isInitialState: false
      };
    });
  }, []);

  const set = useCallback((newPresent: T | ((current: T) => T), shouldReset = false) => {
    setState(currentState => {
      const resolvedNewPresent = 
        typeof newPresent === 'function'
          ? (newPresent as ((current: T) => T))(currentState.present)
          : newPresent;

      if (resolvedNewPresent === currentState.present) {
        return currentState;
      }

      return shouldReset ? {
        past: [],
        present: resolvedNewPresent,
        future: [],
        isInitialState: true
      } : {
        past: [...currentState.past, currentState.present],
        present: resolvedNewPresent,
        future: [],
        isInitialState: false
      };
    });
  }, []);

  return {
    state: state.present,
    set,
    undo,
    redo,
    canUndo,
    canRedo,
    history: state
  };
} 