import axios from 'axios';
import config from '../config';

const baseURL = config.apiBaseUrl;

interface POSTransaction {
  id: string;
  courseId: string;
  memberId?: string;
  items: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
  }>;
  subtotal: number;
  tax: number;
  memberDiscount?: number;
  total: number;
  paymentMethod: string;
  timestamp: string;
}

interface CreateTransactionResponse {
  success: boolean;
  data?: POSTransaction;
  error?: {
    message: string;
    code: string;
  };
}

export const posAPI = {
  createTransaction: async (courseId: string, transaction: Omit<POSTransaction, 'id' | 'timestamp'>): Promise<CreateTransactionResponse> => {
    try {
      const response = await axios.post(`${baseURL}/pos/transactions`, {
        ...transaction,
        courseId,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: {
            message: error.response?.data?.message || 'Failed to create transaction',
            code: error.response?.data?.code || 'UNKNOWN_ERROR'
          }
        };
      }
      return {
        success: false,
        error: {
          message: 'An unexpected error occurred',
          code: 'UNKNOWN_ERROR'
        }
      };
    }
  },

  getTransactions: async (courseId: string, startDate?: string, endDate?: string): Promise<POSTransaction[]> => {
    try {
      const params = new URLSearchParams();
      params.append('courseId', courseId);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axios.get(`${baseURL}/pos/transactions?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      return [];
    }
  }
}; 