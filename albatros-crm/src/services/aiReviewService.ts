// AI Review Response Service
export interface AIResponse {
  id: string;
  reviewId: string;
  response: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  generatedAt: string;
}

export interface Review {
  id: string;
  source: 'google' | 'internal' | 'email' | 'sms';
  author: string;
  rating: number;
  comment: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  golfCourseId: string;
  teeTimeId?: string;
  golferId?: string;
  response?: string;
  helpful: number;
  flagged: boolean;
}

export const generateAIResponse = async (review: Review): Promise<AIResponse> => {
  // Simulate AI processing time
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  const { rating, comment, author } = review;
  const isPositive = rating >= 4;
  const isNegative = rating <= 2;
  
  let response = '';
  let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
  
  if (isPositive) {
    sentiment = 'positive';
    const positiveKeywords = ['amazing', 'great', 'excellent', 'perfect', 'loved', 'wonderful', 'fantastic', 'outstanding'];
    const hasPositiveKeywords = positiveKeywords.some(keyword => 
      comment.toLowerCase().includes(keyword)
    );
    
    if (hasPositiveKeywords) {
      response = `Thank you so much for your wonderful review, ${author}! We're thrilled to hear that you had such a great experience at our course. Your kind words about our staff and course conditions mean the world to us. We look forward to welcoming you back for another round soon!`;
    } else {
      response = `Thank you for your positive feedback, ${author}! We're glad you enjoyed your time at our course. We appreciate you taking the time to share your experience and hope to see you again in the future.`;
    }
  } else if (isNegative) {
    sentiment = 'negative';
    const negativeKeywords = ['slow', 'poor', 'bad', 'terrible', 'awful', 'disappointing', 'unacceptable'];
    const hasNegativeKeywords = negativeKeywords.some(keyword => 
      comment.toLowerCase().includes(keyword)
    );
    
    if (hasNegativeKeywords) {
      response = `We sincerely apologize for the disappointing experience you had, ${author}. Your feedback is important to us, and we take these concerns seriously. We're working to address the issues you mentioned and would welcome the opportunity to make things right. Please reach out to us directly so we can discuss how we can improve your experience.`;
    } else {
      response = `Thank you for your honest feedback, ${author}. We're sorry that your experience didn't meet our usual standards. We value all feedback as it helps us improve, and we hope you'll give us another chance to provide you with the quality experience our golfers expect.`;
    }
  } else {
    // Neutral reviews (3 stars)
    response = `Thank you for your review, ${author}. We appreciate you taking the time to share your feedback. We're always working to improve our course and service, and your input helps us understand what we're doing well and where we can do better. We hope to see you again soon!`;
  }
  
  // Add course-specific elements based on review content
  if (comment.toLowerCase().includes('staff') || comment.toLowerCase().includes('service')) {
    response = `${response} Our team strives to provide excellent service to every golfer.`;
  }
  
  if (comment.toLowerCase().includes('course') || comment.toLowerCase().includes('greens') || comment.toLowerCase().includes('conditions')) {
    response = `${response} We work hard to maintain our course in top condition for our golfers.`;
  }
  
  if (comment.toLowerCase().includes('pro shop') || comment.toLowerCase().includes('restaurant')) {
    response = `${response} We're glad you enjoyed our facilities!`;
  }
  
  if (comment.toLowerCase().includes('pace') || comment.toLowerCase().includes('speed')) {
    response = `${response} We understand the importance of maintaining a good pace of play for all our golfers.`;
  }
  
  if (comment.toLowerCase().includes('price') || comment.toLowerCase().includes('value')) {
    response = `${response} We strive to provide excellent value for our golfers.`;
  }
  
  return {
    id: `ai-${Date.now()}`,
    reviewId: review.id,
    response,
    sentiment,
    confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
    generatedAt: new Date().toISOString()
  };
};

// Future enhancement: Connect to real AI service
export const generateAIResponseWithRealAI = async (review: Review): Promise<AIResponse> => {
  try {
    // In a real implementation, this would call an AI service like OpenAI, Claude, etc.
    const response = await fetch('/api/ai/generate-response', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        review: {
          rating: review.rating,
          comment: review.comment,
          author: review.author,
          source: review.source
        }
      })
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate AI response');
    }
    
    const data = await response.json();
    return {
      id: `ai-${Date.now()}`,
      reviewId: review.id,
      response: data.response,
      sentiment: data.sentiment,
      confidence: data.confidence,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error calling AI service:', error);
    // Fallback to local generation
    return generateAIResponse(review);
  }
}; 