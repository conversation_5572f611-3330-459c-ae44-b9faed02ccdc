// src/services/telnyxService.ts
// Telnyx SMS sending service (plug in your API key and from number when ready)

export interface TelnyxSendSMSOptions {
  to: string; // E.164 format, e.g. +15551234567
  text: string;
  from?: string; // Optional, fallback to default course number
}

type TelnyxConfig = {
  apiKey: string;
  from: string; // Default from number (can be overridden per message)
};

// Example config (replace with your real values or load from env)
const config: TelnyxConfig = {
  apiKey: 'YOUR_TELNYX_API_KEY',
  from: '+15551234567', // Default number
};

export async function sendSMS(options: TelnyxSendSMSOptions): Promise<{ success: boolean; message: string }> {
  const { to, text, from } = options;
  const endpoint = 'https://api.telnyx.com/v2/messages';

  const payload = {
    from: from || config.from,
    to,
    text,
  };

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, message: `Telnyx error: ${errorText}` };
    }
    return { success: true, message: 'SMS sent successfully' };
  } catch (error: any) {
    return { success: false, message: error?.message || 'Unknown error' };
  }
}

/*
Usage Example:

import { sendSMS } from './telnyxService';

sendSMS({
  to: '+15551234567',
  text: 'Your tee time is tomorrow at 10:00am!'
}).then(result => {
  if (result.success) {
    // Success logic
  } else {
    // Error logic
  }
});

// To use a course-specific number:
sendSMS({
  to: '+15551234567',
  text: 'You are invited to book your own tee time!',
  from: '+15557654321', // Course's local number
});
*/ 