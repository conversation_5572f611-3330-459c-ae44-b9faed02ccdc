import { api, ApiResponse, useMockData } from './config';
import { TeeTime } from './types';

export const teeTimesAPI = {
  getTeeTimesList: async (courseId: string, view: 'daily' | 'weekly' | 'monthly' = 'daily', date: string): Promise<ApiResponse<TeeTime[]>> => {
    if (useMockData) {
      return {
        data: [
          {
            id: '1',
            courseId: courseId,
            date: date,
            time: '08:00',
            players: [
              { id: '1', name: '<PERSON>', handicap: 12 }
            ],
            status: 'booked',
            price: 100,
            notes: 'Regular booking'
          },
          {
            id: '2',
            courseId: courseId,
            date: date,
            time: '09:00',
            players: [
              { id: '2', name: '<PERSON>', handicap: 15 }
            ],
            status: 'available',
            price: 100,
            notes: 'Available slot'
          }
        ],
        status: 200,
        message: 'OK'
      };
    }
    return api.get('/tee-times', { params: { courseId, view, date } });
  },

  createTeeTime: async (courseId: string, data: Partial<TeeTime>): Promise<ApiResponse<TeeTime>> => {
    return api.post('/tee-times', { courseId, ...data });
  },

  updateTeeTime: async (courseId: string, teeTimeId: string, data: Partial<TeeTime>): Promise<ApiResponse<TeeTime>> => {
    return api.put(`/tee-times/${teeTimeId}`, { courseId, ...data });
  },

  deleteTeeTime: async (courseId: string, teeTimeId: string): Promise<ApiResponse<void>> => {
    return api.delete(`/tee-times/${teeTimeId}`, { params: { courseId } });
  }
}; 