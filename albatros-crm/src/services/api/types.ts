// Dashboard Types
export interface DashboardOverview {
  totalRevenue: number;
  totalBookings: number;
  averageRating: number;
  weather: string;
}

// Tee Times Types
export interface TeeTime {
  id: string;
  courseId: string;
  date: string;
  time: string;
  players: Array<{
    id: string;
    name: string;
    handicap: number;
  }>;
  status: 'booked' | 'available' | 'cancelled';
  price: number;
  notes: string;
}

// Weather Types
export interface Weather {
  temperature: number;
  condition: string;
  windSpeed: number;
  humidity: number;
}

// Course Types
export interface CourseInfo {
  id: string;
  name: string;
  location: string;
  holes: number;
  par: number;
  rating: number;
  slope: number;
}

// Golfer Types
export interface Golfer {
  id: string;
  courseId: string;
  name: string;
  email: string;
  phone: string;
  handicap: number;
  membershipType: 'regular' | 'premium' | 'vip';
  status: 'active' | 'inactive' | 'suspended';
}

// Pro Shop Types
export interface InventoryItem {
  id: string;
  name: string;
  sku?: string;
  description?: string;
  image?: string;
  cost: number;
  price: number;
  quantity: number;
  category: string;
  mobileApp?: boolean;
  specifications?: {
    clubType?: string;
    brand?: string;
    model?: string;
    material?: string;
    flex?: string;
    loft?: string;
    shaft?: string;
  };
  sizes?: string[];
  colors?: string[];
  membershipDiscount?: number;
  reorderPoint?: number;
  supplier?: string;
  lastOrdered?: string;
  nextOrder?: string;
}

// Nineteenth Hole Types
export interface MenuItem {
  id: string;
  courseId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  ingredients?: string[];
  allergens?: string[];
  isAvailable: boolean;
  imageUrl?: string;
  preparationTime?: number;
  calories?: number;
  mobileApp: boolean;
}

export interface MenuCategory {
  id: string;
  courseId: string;
  name: string;
  description?: string;
  order: number;
  isActive: boolean;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  quantity: number;
  specialInstructions?: string;
  price: number;
}

export interface Order {
  id: string;
  courseId: string;
  items: OrderItem[];
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  totalAmount: number;
  customerName?: string;
  tableNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Analytics Types
export interface AnalyticsOverview {
  totalRounds: number;
  revenue: number;
  averageRating: number;
  peakHours: string[];
}

// Back Office Types
export interface EmployeeShift {
  id: string;
  employeeName: string;
  role: string;
  avatar?: string;
  department: string;
  clockIn: string;
  clockOut?: string;
  hoursWorked?: number;
  status: 'active' | 'completed' | 'upcoming';
}

export interface DepartmentPayroll {
  name: string;
  amount: number;
  hours: number;
  employeeCount: number;
}

export interface PayrollPeriod {
  start: string;
  end: string;
  departments: DepartmentPayroll[];
}

export interface MarketingTemplatePerformance {
  opens: number;
  clicks: number;
  conversions: number;
}

export interface MarketingTemplate {
  id: string;
  name: string;
  type: 'Email' | 'SMS';
  lastModified: string;
  performance?: MarketingTemplatePerformance;
}

export interface BackOfficeData {
  inventory: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  transactions: Array<{
    id: string;
    date: string;
    amount: number;
    type: string;
  }>;
  staff: Array<{
    id: string;
    name: string;
    role: string;
    status: string;
  }>;
}

export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
} 