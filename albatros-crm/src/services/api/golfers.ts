import { api, ApiResponse, useMockData } from './config';
import { Golfer } from './types';
import { mockAPI } from './mockData';

export const golfersAPI = {
  getGolfers: async (courseId: string): Promise<ApiResponse<Golfer[]>> => {
    if (useMockData) {
      return mockAPI.getGolfers(courseId);
    }
    return api.get('/golfers', { params: { courseId } });
  },

  addGolfer: async (courseId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    if (useMockData) {
      return mockAPI.addGolfer(courseId, golfer);
    }
    return api.post('/golfers', { courseId, ...golfer });
  },

  updateGolfer: async (courseId: string, golferId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    if (useMockData) {
      return mockAPI.updateGolfer(courseId, golferId, golfer);
    }
    return api.put(`/golfers/${golferId}`, { courseId, ...golfer });
  }
}; 