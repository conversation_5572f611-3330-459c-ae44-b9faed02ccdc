import { api, ApiResponse, useMockData } from './config';
import { DashboardOverview, AnalyticsOverview } from './types';
import { mockAPI } from './mockData';

export const dashboardAPI = {
  getOverview: async (courseId: string, timeframe: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<ApiResponse<DashboardOverview>> => {
    if (useMockData) {
      return {
        status: 200,
        message: 'Success',
        data: {
          totalRevenue: 15000,
          totalBookings: 50,
          weather: 'Sunny, 75°F',
          averageRating: 4.5
        }
      };
    }
    return api.get('/dashboard/overview', { params: { courseId, timeframe } });
  },

  getAnalytics: async (courseId: string, timeView: 'daily' | 'weekly' | 'monthly' = 'daily'): Promise<ApiResponse<AnalyticsOverview>> => {
    if (useMockData) {
      return mockAPI.getMetrics(courseId);
    }
    return api.get('/dashboard/analytics', { params: { courseId, timeView } });
  }
}; 