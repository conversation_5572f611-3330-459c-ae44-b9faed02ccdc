import { ApiResponse, TeeTime, Golfer, CourseInfo, Weather, AnalyticsOverview, BackOfficeData, MenuItem, MenuCategory, Order } from './types';

// Mock data generators
const generateMockTeeTimes = (count: number): TeeTime[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: (i + 1).toString(),
    courseId: '1',
    date: '2024-03-20',
    time: `${8 + i}:00`,
    players: [
      { id: '1', name: '<PERSON>', handicap: 12 },
      { id: '2', name: '<PERSON>', handicap: 15 }
    ],
    status: i % 2 === 0 ? 'booked' : 'available',
    price: 100,
    notes: `Tee time ${i + 1}`
  }));
};

const generateMockGolfers = (count: number): Golfer[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: (i + 1).toString(),
    courseId: '1',
    name: `<PERSON>er ${i + 1}`,
    email: `golfer${i + 1}@example.com`,
    phone: `123-456-${i.toString().padStart(4, '0')}`,
    handicap: 10 + i,
    membershipType: i % 3 === 0 ? 'regular' : i % 3 === 1 ? 'premium' : 'vip',
    status: 'active'
  }));
};

const mockMetrics: AnalyticsOverview = {
  totalRounds: 150,
  revenue: 15000,
  averageRating: 4.5,
  peakHours: ['09:00', '10:00', '11:00']
};

// Mock API responses
export const mockAPI = {
  // Tee Times
  getTeeTimesList: async (courseId: string, viewType: string, date: string): Promise<ApiResponse<TeeTime[]>> => {
    return {
      data: generateMockTeeTimes(5),
      status: 200,
      message: 'Success'
    };
  },

  createTeeTime: async (courseId: string, data: Partial<TeeTime>): Promise<ApiResponse<TeeTime>> => {
    const newTeeTime: TeeTime = {
      id: Date.now().toString(),
      courseId,
      date: data.date || new Date().toISOString().split('T')[0],
      time: data.time || '09:00',
      players: data.players || [],
      status: data.status || 'available',
      price: data.price || 100,
      notes: data.notes || ''
    };
    return {
      data: newTeeTime,
      status: 201,
      message: 'Tee time created successfully'
    };
  },

  updateTeeTime: async (id: string, data: Partial<TeeTime>): Promise<ApiResponse<TeeTime>> => {
    return {
      data: {
        id,
        courseId: data.courseId || '1',
        date: data.date || new Date().toISOString().split('T')[0],
        time: data.time || '09:00',
        players: data.players || [],
        status: data.status || 'available',
        price: data.price || 100,
        notes: data.notes || ''
      },
      status: 200,
      message: 'Tee time updated successfully'
    };
  },

  deleteTeeTime: async (id: string): Promise<ApiResponse<null>> => {
    return {
      data: null,
      status: 200,
      message: 'Tee time deleted successfully'
    };
  },

  // Golfers
  getGolfers: async (courseId: string): Promise<ApiResponse<Golfer[]>> => {
    return {
      data: generateMockGolfers(5),
      status: 200,
      message: 'Success'
    };
  },

  addGolfer: async (courseId: string, data: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    const newGolfer: Golfer = {
      id: Date.now().toString(),
      courseId,
      name: data.name || '',
      email: data.email || '',
      phone: data.phone || '',
      handicap: data.handicap || 0,
      membershipType: data.membershipType || 'regular',
      status: data.status || 'active'
    };
    return {
      data: newGolfer,
      status: 201,
      message: 'Golfer added successfully'
    };
  },

  updateGolfer: async (courseId: string, golferId: string, data: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    return {
      status: 200,
      message: 'Golfer updated successfully',
      data: {
        id: golferId,
        courseId: courseId,
        name: data.name || 'John Doe',
        email: data.email || '<EMAIL>',
        phone: data.phone || '************',
        handicap: data.handicap || 10,
        membershipType: data.membershipType || 'regular',
        status: data.status || 'active'
      }
    };
  },

  // Course Info
  getCourseInfo: async (courseId: string): Promise<ApiResponse<CourseInfo>> => {
    return {
      data: {
        id: courseId,
        name: 'Albatros Golf Club',
        location: '123 Golf Lane, Golf City',
        holes: 18,
        par: 72,
        rating: 74.2,
        slope: 135
      },
      status: 200,
      message: 'Success'
    };
  },

  // Weather
  getWeather: async (courseId: string): Promise<ApiResponse<Weather>> => {
    return {
      data: {
        temperature: 75,
        condition: 'Sunny',
        windSpeed: 5,
        humidity: 60
      },
      status: 200,
      message: 'Success'
    };
  },

  // Analytics
  getAnalytics: async (courseId: string): Promise<ApiResponse<AnalyticsOverview>> => {
    return {
      data: mockMetrics,
      status: 200,
      message: 'Success'
    };
  },

  getMetrics: async (courseId: string): Promise<ApiResponse<AnalyticsOverview>> => {
    return {
      data: mockMetrics,
      status: 200,
      message: 'Success'
    };
  },

  // Back Office
  getBackOfficeData: async (courseId: string): Promise<ApiResponse<BackOfficeData>> => {
    return {
      data: {
        inventory: [
          { id: '1', name: 'Golf Balls', quantity: 100, price: 25 },
          { id: '2', name: 'Tees', quantity: 500, price: 5 }
        ],
        transactions: [
          { id: '1', date: '2024-03-20', amount: 100, type: 'green_fee' },
          { id: '2', date: '2024-03-20', amount: 50, type: 'pro_shop' }
        ],
        staff: [
          { id: '1', name: 'Manager', role: 'manager', status: 'active' },
          { id: '2', name: 'Pro', role: 'pro', status: 'active' }
        ]
      },
      status: 200,
      message: 'Success'
    };
  },

  // Nineteenth Hole
  getMenuItems: async (courseId: string, category?: string): Promise<ApiResponse<MenuItem[]>> => {
    return {
      status: 200,
      message: 'Success',
      data: [
        {
          id: '1',
          courseId,
          name: 'Classic Burger',
          description: 'Juicy beef patty with lettuce, tomato, and special sauce',
          price: 12.99,
          category: 'sandwiches',
          ingredients: ['beef', 'lettuce', 'tomato', 'onion', 'pickles'],
          allergens: ['gluten', 'dairy'],
          isAvailable: true,
          imageUrl: 'https://example.com/burger.jpg',
          preparationTime: 15,
          calories: 850,
          mobileApp: true
        },
        {
          id: '2',
          courseId,
          name: 'Caesar Salad',
          description: 'Fresh romaine lettuce with parmesan and croutons',
          price: 9.99,
          category: 'salads',
          ingredients: ['romaine', 'parmesan', 'croutons', 'caesar dressing'],
          allergens: ['dairy', 'gluten'],
          isAvailable: true,
          imageUrl: 'https://example.com/salad.jpg',
          preparationTime: 10,
          calories: 450,
          mobileApp: true
        }
      ].filter(item => !category || item.category === category)
    };
  },

  getMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<MenuItem>> => {
    return {
      status: 200,
      message: 'Success',
      data: {
        id: itemId,
        courseId,
        name: 'Classic Burger',
        description: 'Juicy beef patty with lettuce, tomato, and special sauce',
        price: 12.99,
        category: 'sandwiches',
        ingredients: ['beef', 'lettuce', 'tomato', 'onion', 'pickles'],
        allergens: ['gluten', 'dairy'],
        isAvailable: true,
        imageUrl: 'https://example.com/burger.jpg',
        preparationTime: 15,
        calories: 850,
        mobileApp: true
      }
    };
  },

  createMenuItem: async (courseId: string, data: Partial<MenuItem>): Promise<ApiResponse<MenuItem>> => {
    return {
      status: 201,
      message: 'Menu item created successfully',
      data: {
        id: Date.now().toString(),
        courseId,
        name: data.name || 'New Item',
        description: data.description || '',
        price: data.price || 0,
        category: data.category || 'uncategorized',
        ingredients: data.ingredients || [],
        allergens: data.allergens || [],
        isAvailable: data.isAvailable ?? true,
        imageUrl: data.imageUrl,
        preparationTime: data.preparationTime,
        calories: data.calories,
        mobileApp: data.mobileApp ?? true
      }
    };
  },

  updateMenuItem: async (courseId: string, itemId: string, data: Partial<MenuItem>): Promise<ApiResponse<MenuItem>> => {
    return {
      status: 200,
      message: 'Menu item updated successfully',
      data: {
        id: itemId,
        courseId,
        name: data.name || 'Updated Item',
        description: data.description || '',
        price: data.price || 0,
        category: data.category || 'uncategorized',
        ingredients: data.ingredients || [],
        allergens: data.allergens || [],
        isAvailable: data.isAvailable ?? true,
        imageUrl: data.imageUrl,
        preparationTime: data.preparationTime,
        calories: data.calories,
        mobileApp: data.mobileApp ?? true
      }
    };
  },

  deleteMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<void>> => {
    return {
      status: 200,
      message: 'Menu item deleted successfully',
      data: undefined
    };
  },

  getCategories: async (courseId: string): Promise<ApiResponse<MenuCategory[]>> => {
    return {
      status: 200,
      message: 'Success',
      data: [
        {
          id: '1',
          courseId,
          name: 'Sandwiches',
          description: 'Delicious sandwiches and burgers',
          order: 1,
          isActive: true
        },
        {
          id: '2',
          courseId,
          name: 'Salads',
          description: 'Fresh and healthy salads',
          order: 2,
          isActive: true
        }
      ]
    };
  },

  createCategory: async (courseId: string, data: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> => {
    return {
      status: 201,
      message: 'Category created successfully',
      data: {
        id: Date.now().toString(),
        courseId,
        name: data.name || 'New Category',
        description: data.description,
        order: data.order || 0,
        isActive: data.isActive ?? true
      }
    };
  },

  updateCategory: async (courseId: string, categoryId: string, data: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> => {
    return {
      status: 200,
      message: 'Category updated successfully',
      data: {
        id: categoryId,
        courseId,
        name: data.name || 'Updated Category',
        description: data.description,
        order: data.order || 0,
        isActive: data.isActive ?? true
      }
    };
  },

  deleteCategory: async (courseId: string, categoryId: string): Promise<ApiResponse<void>> => {
    return {
      status: 200,
      message: 'Category deleted successfully',
      data: undefined
    };
  },

  getOrders: async (courseId: string, status?: Order['status']): Promise<ApiResponse<Order[]>> => {
    return {
      status: 200,
      message: 'Success',
      data: [
        {
          id: '1',
          courseId,
          items: [
            {
              id: '1',
              menuItemId: '1',
              quantity: 2,
              specialInstructions: 'No onions',
              price: 12.99
            }
          ],
          status: 'pending' as const,
          totalAmount: 25.98,
          customerName: 'John Doe',
          tableNumber: '12',
          notes: 'In a hurry',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ].filter(order => !status || order.status === status)
    };
  },

  getOrder: async (courseId: string, orderId: string): Promise<ApiResponse<Order>> => {
    return {
      status: 200,
      message: 'Success',
      data: {
        id: orderId,
        courseId,
        items: [
          {
            id: '1',
            menuItemId: '1',
            quantity: 2,
            specialInstructions: 'No onions',
            price: 12.99
          }
        ],
        status: 'pending',
        totalAmount: 25.98,
        customerName: 'John Doe',
        tableNumber: '12',
        notes: 'In a hurry',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  },

  createOrder: async (courseId: string, data: Partial<Order>): Promise<ApiResponse<Order>> => {
    return {
      status: 201,
      message: 'Order created successfully',
      data: {
        id: Date.now().toString(),
        courseId,
        items: data.items || [],
        status: data.status || 'pending',
        totalAmount: data.totalAmount || 0,
        customerName: data.customerName,
        tableNumber: data.tableNumber,
        notes: data.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  },

  updateOrder: async (courseId: string, orderId: string, data: Partial<Order>): Promise<ApiResponse<Order>> => {
    return {
      status: 200,
      message: 'Order updated successfully',
      data: {
        id: orderId,
        courseId,
        items: data.items || [],
        status: data.status || 'pending',
        totalAmount: data.totalAmount || 0,
        customerName: data.customerName,
        tableNumber: data.tableNumber,
        notes: data.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  },

  deleteOrder: async (courseId: string, orderId: string): Promise<ApiResponse<void>> => {
    return {
      status: 200,
      message: 'Order deleted successfully',
      data: undefined
    };
  }
}; 