import { api, ApiResponse, useMockData } from './config';
import { mockAPI } from './mockData';

export interface MenuItem {
  id: string;
  courseId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  ingredients?: string[];
  allergens?: string[];
  isAvailable: boolean;
  imageUrl?: string;
  preparationTime?: number;
  calories?: number;
  mobileApp: boolean;
}

export interface MenuCategory {
  id: string;
  courseId: string;
  name: string;
  description?: string;
  order: number;
  isActive: boolean;
}

export interface Order {
  id: string;
  courseId: string;
  items: OrderItem[];
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  totalAmount: number;
  customerName?: string;
  tableNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  quantity: number;
  specialInstructions?: string;
  price: number;
}

export const nineteenthHoleAPI = {
  // Menu Items
  getMenuItems: async (courseId: string, category?: string): Promise<ApiResponse<MenuItem[]>> => {
    if (useMockData) {
      return mockAPI.getMenuItems(courseId, category);
    }
    return api.get('/nineteenth-hole/menu-items', { params: { courseId, category } });
  },

  getMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<MenuItem>> => {
    if (useMockData) {
      return mockAPI.getMenuItem(courseId, itemId);
    }
    return api.get(`/nineteenth-hole/menu-items/${itemId}`, { params: { courseId } });
  },

  createMenuItem: async (courseId: string, data: Partial<MenuItem>): Promise<ApiResponse<MenuItem>> => {
    if (useMockData) {
      return mockAPI.createMenuItem(courseId, data);
    }
    return api.post('/nineteenth-hole/menu-items', { courseId, ...data });
  },

  updateMenuItem: async (courseId: string, itemId: string, data: Partial<MenuItem>): Promise<ApiResponse<MenuItem>> => {
    if (useMockData) {
      return mockAPI.updateMenuItem(courseId, itemId, data);
    }
    return api.patch(`/nineteenth-hole/menu-items/${itemId}`, { courseId, ...data });
  },

  deleteMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<void>> => {
    if (useMockData) {
      return mockAPI.deleteMenuItem(courseId, itemId);
    }
    return api.delete(`/nineteenth-hole/menu-items/${itemId}`, { params: { courseId } });
  },

  // Categories
  getCategories: async (courseId: string): Promise<ApiResponse<MenuCategory[]>> => {
    if (useMockData) {
      return mockAPI.getCategories(courseId);
    }
    return api.get('/nineteenth-hole/categories', { params: { courseId } });
  },

  createCategory: async (courseId: string, data: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> => {
    if (useMockData) {
      return mockAPI.createCategory(courseId, data);
    }
    return api.post('/nineteenth-hole/categories', { courseId, ...data });
  },

  updateCategory: async (courseId: string, categoryId: string, data: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> => {
    if (useMockData) {
      return mockAPI.updateCategory(courseId, categoryId, data);
    }
    return api.patch(`/nineteenth-hole/categories/${categoryId}`, { courseId, ...data });
  },

  deleteCategory: async (courseId: string, categoryId: string): Promise<ApiResponse<void>> => {
    if (useMockData) {
      return mockAPI.deleteCategory(courseId, categoryId);
    }
    return api.delete(`/nineteenth-hole/categories/${categoryId}`, { params: { courseId } });
  },

  // Orders
  getOrders: async (courseId: string, status?: Order['status']): Promise<ApiResponse<Order[]>> => {
    if (useMockData) {
      return mockAPI.getOrders(courseId, status);
    }
    return api.get('/nineteenth-hole/orders', { params: { courseId, status } });
  },

  getOrder: async (courseId: string, orderId: string): Promise<ApiResponse<Order>> => {
    if (useMockData) {
      return mockAPI.getOrder(courseId, orderId);
    }
    return api.get(`/nineteenth-hole/orders/${orderId}`, { params: { courseId } });
  },

  createOrder: async (courseId: string, data: Partial<Order>): Promise<ApiResponse<Order>> => {
    if (useMockData) {
      return mockAPI.createOrder(courseId, data);
    }
    return api.post('/nineteenth-hole/orders', { courseId, ...data });
  },

  updateOrder: async (courseId: string, orderId: string, data: Partial<Order>): Promise<ApiResponse<Order>> => {
    if (useMockData) {
      return mockAPI.updateOrder(courseId, orderId, data);
    }
    return api.patch(`/nineteenth-hole/orders/${orderId}`, { courseId, ...data });
  },

  deleteOrder: async (courseId: string, orderId: string): Promise<ApiResponse<void>> => {
    if (useMockData) {
      return mockAPI.deleteOrder(courseId, orderId);
    }
    return api.delete(`/nineteenth-hole/orders/${orderId}`, { params: { courseId } });
  }
}; 