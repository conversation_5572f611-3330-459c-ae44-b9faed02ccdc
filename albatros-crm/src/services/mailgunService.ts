// src/services/mailgunService.ts
// Mailgun email sending service (plug in your API key/domain when ready)

export interface MailgunSendEmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string; // Optional, fallback to default
}

// You will need to set these values before using in production
type MailgunConfig = {
  apiKey: string;
  domain: string;
  from: string;
};

// Example config (replace with your real values or load from env)
const config: MailgunConfig = {
  apiKey: 'YOUR_MAILGUN_API_KEY',
  domain: 'YOUR_DOMAIN_NAME',
  from: '<EMAIL>',
};

export async function sendEmail(options: MailgunSendEmailOptions): Promise<{ success: boolean; message: string }> {
  const { to, subject, text, html, from } = options;
  const formData = new URLSearchParams();
  formData.append('from', from || config.from);
  formData.append('to', to);
  formData.append('subject', subject);
  if (text) formData.append('text', text);
  if (html) formData.append('html', html);

  const endpoint = `https://api.mailgun.net/v3/${config.domain}/messages`;

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        Authorization: 'Basic ' + btoa('api:' + config.apiKey),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, message: `Mailgun error: ${errorText}` };
    }
    return { success: true, message: 'Email sent successfully' };
  } catch (error: any) {
    return { success: false, message: error?.message || 'Unknown error' };
  }
}

/*
Usage Example:

import { sendEmail } from './mailgunService';

sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  text: 'Hello from Mailgun!',
  html: '<b>Hello from Mailgun!</b>',
}).then(result => {
  if (result.success) {
    // Success logic
  } else {
    // Error logic
  }
});
*/ 