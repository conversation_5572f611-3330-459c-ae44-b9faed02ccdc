// Remove theme import and use direct color values
const PRIMARY_COLOR = '#1976d2';
const PRIMARY_LIGHT = '#42a5f5';

interface Golfer {
  id: number;
  name: string;
  initials: string;
  avatar: string | null;
  phone: string;
  email: string;
  address: string;
  stars: number;
  lastPlayDate: string;
  upcomingPlayDate: string;
  isMember: boolean;
  avatarColor: string;
  firstName: string;
  lastName: string;
  albatrossStarScore: string;
  nps: string;
  events: string;
  foodDrink: string;
  student: string;
}

interface MetricValue {
  value: number;
  change: number;
}

interface MetricPercentage {
  percentage: number;
  change: number;
}

interface Metrics {
  members: MetricValue;
  golferReturnRate: MetricPercentage;
  obsoleteGolfers: MetricValue;
}

let mockGolfers: Golfer[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON>',
    initials: '<PERSON>',
    avatar: 'https://i.pravatar.cc/150?img=1',
    phone: '(*************',
    email: '<EMAIL>',
    address: '123 Market St, San Francisco, CA 94105',
    stars: 4,
    lastPlayDate: '12-11-2024',
    upcomingPlayDate: '12-13-2024',
    isMember: true,
    avatarColor: PRIMARY_COLOR,
    firstName: 'Abbi',
    lastName: 'Lopez',
    albatrossStarScore: '85',
    nps: '9',
    events: 'Monthly Tournament',
    foodDrink: 'Craft Beer',
    student: 'No'
  },
  {
    id: 2,
    name: '<PERSON>',
    initials: 'JS',
    avatar: 'https://i.pravatar.cc/150?img=2',
    phone: '(*************',
    email: '<EMAIL>',
    address: '456 Park Ave, New York, NY 10022',
    stars: 1,
    lastPlayDate: '12-11-2024',
    upcomingPlayDate: 'NONE',
    isMember: false,
    avatarColor: PRIMARY_LIGHT,
    firstName: 'Jake',
    lastName: 'Smith',
    albatrossStarScore: '72',
    nps: '7',
    events: 'Weekend Specials',
    foodDrink: 'Wine',
    student: 'Yes'
  },
  {
    id: 3,
    name: 'Dylan Chun',
    initials: 'DC',
    avatar: 'https://i.pravatar.cc/150?img=3',
    phone: '(*************',
    email: '<EMAIL>',
    address: '789 Ocean Dr, Miami Beach, FL 33139',
    stars: 3,
    lastPlayDate: '12-09-2024',
    upcomingPlayDate: '12-12-2024',
    isMember: true,
    avatarColor: '#3B82F6',
    firstName: 'Dylan',
    lastName: 'Chun',
    albatrossStarScore: '78',
    nps: '8',
    events: 'Corporate Outing',
    foodDrink: 'Cocktails',
    student: 'No'
  },
  {
    id: 4,
    name: 'Jane Doe',
    initials: 'JD',
    avatar: 'https://i.pravatar.cc/150?img=4',
    phone: '(*************',
    email: '<EMAIL>',
    address: '321 Michigan Ave, Chicago, IL 60601',
    stars: 1,
    lastPlayDate: '12-11-2024',
    upcomingPlayDate: 'NONE',
    isMember: false,
    avatarColor: '#EC4899',
    firstName: 'Jane',
    lastName: 'Doe',
    albatrossStarScore: '65',
    nps: '6',
    events: 'Beginner Clinic',
    foodDrink: 'Soft Drinks',
    student: 'Yes'
  },
  {
    id: 5,
    name: 'John Frank',
    initials: 'JF',
    avatar: 'https://i.pravatar.cc/150?img=5',
    phone: '(*************',
    email: '<EMAIL>',
    address: '654 Main St, Houston, TX 77002',
    stars: 2,
    lastPlayDate: '12-02-2024',
    upcomingPlayDate: '12-13-2024',
    isMember: false,
    avatarColor: '#8B5CF6',
    firstName: 'John',
    lastName: 'Frank',
    albatrossStarScore: '70',
    nps: '7',
    events: 'Senior League',
    foodDrink: 'Coffee',
    student: 'No'
  },
  {
    id: 6,
    name: 'Eddy Cusuma',
    initials: 'EC',
    avatar: 'https://i.pravatar.cc/150?img=6',
    phone: '(*************',
    email: '<EMAIL>',
    address: '987 Camelback Rd, Phoenix, AZ 85013',
    stars: 5,
    lastPlayDate: '12-11-2024',
    upcomingPlayDate: '12-12-2024',
    isMember: true,
    avatarColor: '#F59E0B',
    firstName: 'Eddy',
    lastName: 'Cusuma',
    albatrossStarScore: '92',
    nps: '10',
    events: 'Championship Series',
    foodDrink: 'Premium Spirits',
    student: 'No'
  },
  {
    id: 7,
    name: 'Lamar Dyer',
    initials: 'LD',
    avatar: 'https://i.pravatar.cc/150?img=7',
    phone: '(*************',
    email: '<EMAIL>',
    address: '147 Pike St, Seattle, WA 98101',
    stars: 3,
    lastPlayDate: '12-11-2024',
    upcomingPlayDate: '12-14-2024',
    isMember: true,
    avatarColor: '#10B981',
    firstName: 'Lamar',
    lastName: 'Dyer',
    albatrossStarScore: '80',
    nps: '8',
    events: 'Pro-Am Tournament',
    foodDrink: 'Local Brews',
    student: 'No'
  },
  {
    id: 8,
    name: 'Jaime Brown',
    initials: 'JB',
    avatar: 'https://i.pravatar.cc/150?img=8',
    phone: '(*************',
    email: '<EMAIL>',
    address: '852 16th St, Denver, CO 80202',
    stars: 4,
    lastPlayDate: '12-10-2024',
    upcomingPlayDate: '12-12-2024',
    isMember: true,
    avatarColor: '#6366F1',
    firstName: 'Jaime',
    lastName: 'Brown',
    albatrossStarScore: '88',
    nps: '9',
    events: 'Member Exclusive',
    foodDrink: 'Craft Cocktails',
    student: 'No'
  }
];

export const mockMetrics: Metrics = {
  members: {
    value: 124,
    change: 3
  },
  golferReturnRate: {
    percentage: 44,
    change: -2
  },
  obsoleteGolfers: {
    value: 202,
    change: 12
  }
};

// Function to get all golfers
export const getGolfers = (): Golfer[] => mockGolfers;

// Function to add a new golfer
export const addGolfer = (golferData: Partial<Golfer>): Golfer => {
  const newGolfer: Golfer = {
    id: Math.max(...mockGolfers.map(g => g.id)) + 1,
    initials: `${golferData.firstName?.[0] || ''}${golferData.lastName?.[0] || ''}`,
    name: `${golferData.firstName || ''} ${golferData.lastName || ''}`,
    avatar: golferData.avatar || null,
    email: golferData.email || '',
    phone: golferData.phone || '',
    address: golferData.address || '',
    stars: golferData.stars || 0,
    lastPlayDate: golferData.lastPlayDate || new Date().toLocaleDateString('en-US'),
    upcomingPlayDate: golferData.upcomingPlayDate || 'NONE',
    isMember: golferData.isMember || false,
    avatarColor: golferData.avatarColor || PRIMARY_COLOR,
    firstName: golferData.firstName || '',
    lastName: golferData.lastName || '',
    albatrossStarScore: golferData.albatrossStarScore || '',
    nps: golferData.nps || '',
    events: golferData.events || '',
    foodDrink: golferData.foodDrink || '',
    student: golferData.student || ''
  };
  mockGolfers = [...mockGolfers, newGolfer];
  return newGolfer;
};

// Function to update an existing golfer
export const updateGolfer = (id: number, golferData: Partial<Golfer>): Golfer | undefined => {
  mockGolfers = mockGolfers.map(golfer => {
    if (golfer.id === id) {
      return {
        ...golfer,
        firstName: golferData.firstName || '',
        lastName: golferData.lastName || '',
        name: `${golferData.firstName || ''} ${golferData.lastName || ''}`,
        initials: `${golferData.firstName?.[0] || ''}${golferData.lastName?.[0] || ''}`,
        email: golferData.email || '',
        phone: golferData.phone || '',
        address: golferData.address || '',
        stars: golferData.stars || 0,
        lastPlayDate: golferData.lastPlayDate || new Date().toLocaleDateString('en-US'),
        upcomingPlayDate: golferData.upcomingPlayDate || 'NONE',
        isMember: golferData.isMember || false,
        avatarColor: golferData.avatarColor || PRIMARY_COLOR,
        avatar: golferData.avatar || null,
        albatrossStarScore: golferData.albatrossStarScore || '',
        nps: golferData.nps || '',
        events: golferData.events || '',
        foodDrink: golferData.foodDrink || '',
        student: golferData.student || ''
      };
    }
    return golfer;
  });
  return mockGolfers.find(g => g.id === id);
};