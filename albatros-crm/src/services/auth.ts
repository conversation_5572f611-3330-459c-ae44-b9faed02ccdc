import axios from 'axios';
import config from '../config';

const API_BASE_URL = config.apiBaseUrl;

interface User {
  id: number;
  name: string;
  email: string;
  created_at: string;
}

interface LoginResponse {
  token: string;
  user: User;
}

interface SignupResponse {
  token: string;
  user: User;
}

class AuthService {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('authToken');
  }

  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email,
        password,
      });
      
      const { token, user } = response.data;
      this.token = token;
      localStorage.setItem('authToken', token);
      
      return { token, user };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async signup(name: string, email: string, password: string): Promise<SignupResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/signup`, {
        name,
        email,
        password,
      });
      
      const { token, user } = response.data;
      this.token = token;
      localStorage.setItem('authToken', token);
      
      return { token, user };
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }

  async me(): Promise<User> {
    if (!this.token) {
      throw new Error('No authentication token');
    }

    try {
      const response = await axios.get(`${API_BASE_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }

  logout(): void {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }
}

export const auth = new AuthService(); 