import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Chip,
  Tooltip,
  TextField,
  MenuItem,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Checkbox,
  FormControlLabel,
  Radio,
  RadioGroup,
  CircularProgress,
  Snackbar,
  Alert,
  Autocomplete,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import DeleteIcon from '@mui/icons-material/Delete';
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';
import LocalBarIcon from '@mui/icons-material/LocalBar';
import ReceiptIcon from '@mui/icons-material/Receipt';
import KitchenIcon from '@mui/icons-material/Kitchen';
import { menuItems, tables, orders, itemTransactions, transactions, users, transformMenuItem } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

// Mock data
const mockMenu = [
  { id: '1', name: 'Chicken Wings', price: 12, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['No sauce', 'Extra spicy', 'Ranch'] },
  { id: '2', name: 'Meat Sticks', price: 10, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['BBQ', 'Teriyaki'] },
  { id: '3', name: 'Carpaccio', price: 14, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['Extra lemon'] },
  { id: '4', name: 'Rump Steak', price: 24, category: 'Mains', color: '#ef4444', route: 'kitchen', modifiers: ['Rare', 'Medium', 'Well-done'] },
  { id: '5', name: 'Bruschetta', price: 9, category: 'Starters', color: '#22c55e', route: 'kitchen', modifiers: ['No garlic'] },
  { id: '6', name: 'Thai Salad', price: 10, category: 'Salads', color: '#22c55e', route: 'kitchen', modifiers: ['No peanuts'] },
  { id: '7', name: 'Spinach Salad', price: 11, category: 'Salads', color: '#22c55e', route: 'kitchen', modifiers: ['No onions'] },
  { id: '8', name: 'Fish Tacos', price: 14, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['No slaw'] },
  { id: '9', name: 'Grilled Tuna', price: 16, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['Rare', 'Medium', 'Well-done'] },
  { id: '10', name: 'Salmon', price: 16, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['No skin'] },
  { id: '11', name: 'Coquilles', price: 18, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: [] },
  { id: '12', name: 'Ice Cream', price: 6, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: ['Chocolate', 'Vanilla', 'Strawberry'] },
  { id: '13', name: 'Pancakes', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: ['Maple syrup', 'No butter'] },
  { id: '14', name: 'Choco Mousse', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: [] },
  { id: '15', name: 'Chocolate Cake', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: [] },
  { id: '16', name: 'Lunch Menu', price: 20, category: 'Menus', color: '#a78bfa', route: 'kitchen', modifiers: [] },
  { id: '17', name: 'Dinner Menu', price: 22, category: 'Menus', color: '#a78bfa', route: 'kitchen', modifiers: [] },
  { id: '18', name: 'Friends/Family 10%', price: -10, category: 'Discounts', color: '#22c55e', route: 'kitchen', modifiers: [] },
];

const mockDrinks = [
  { id: 'c1', name: 'Cola', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lemon'] },
  { id: 'c2', name: 'Diet Cola', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lime'] },
  { id: 'c3', name: 'Iced Tea', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lemon'] },
  { id: 'c4', name: 'Lemonade', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice'] },
];

const mockTables = [
  { id: 'BT-01', name: 'Bar Table 1' },
  { id: 'BT-02', name: 'Bar Table 2' },
  { id: 'T-01', name: 'Table 1' },
  { id: 'T-02', name: 'Table 2' },
  { id: 'T-03', name: 'Table 3' },
];

const mockSeats = [1, 2, 3, 4, 5, 6];

const allCategories = Array.from(new Set([...mockMenu, ...mockDrinks].map(i => i.category)));

interface OrderItem {
  id: string;
  name: string;
  price: number;
  qty: number;
  color?: string;
  route: 'kitchen' | 'bar';
  modifiers: string[];
  seat: number;
  status: 'Pending' | 'Sent' | 'Ready' | 'Served';
}

const initialOrder: OrderItem[] = [];

interface Golfer {
  id: number;
  name: string;
  email: string;
}

interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  color: string;
  route: 'kitchen' | 'bar';
  modifiers: string[];
}

interface Table {
  id: string;
  name: string;
}

interface ItemTransaction {
  id: number;
  quantity: number;
  food_and_beverage_item_id: number;
  transaction_id: number;
  order_status: 'Pending' | 'Ready' | 'Served' | 'Delivered';
  golfer_id?: number;
  claimed_by_employee_id: number;
  delivered_by_employee_id?: number;
  delivery_time?: number;
  location: 'Grill' | 'Bar';
}

// Update addDialog type
type AddDialogState = {
  open: boolean;
  item?: MenuItem;
  selectedModifiers?: string[];
  selectedSeat?: number;
};

const RestaurantPOSPage: React.FC = () => {
  const { user } = useAuth();
  const [selectedTable, setSelectedTable] = useState(mockTables[0].id);
  const [order, setOrder] = useState<OrderItem[]>(initialOrder);
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState<string>('All');
  const [addDialog, setAddDialog] = useState<AddDialogState>({ open: false });
  const [sent, setSent] = useState(false);
  const [orderState, setOrderState] = useState<'pending' | 'sent' | 'ready' | 'served'>('pending');
  const [checkoutDialog, setCheckoutDialog] = useState(false);
  const [splitType, setSplitType] = useState<'single' | 'by_seat'>('single');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null);
  const [menuItemsList, setMenuItemsList] = useState<MenuItem[]>([]);
  const [tablesList, setTablesList] = useState<Table[]>([]);
  const [selectedGolfer, setSelectedGolfer] = useState<Golfer | null>(null);
  const [golfers, setGolfers] = useState<Golfer[]>([]);
  const [currentTransactionId, setCurrentTransactionId] = useState<number | null>(null);

  // Load menu items, tables, and golfers on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [menuData, tablesData, golfersData] = await Promise.all([
          menuItems.getAll(),
          tables.getAll(),
          users.getAll()
        ]);
        setMenuItemsList(menuData.map(transformMenuItem));
        setTablesList(tablesData);
        setGolfers(golfersData);
      } catch (err) {
        setError('Failed to load data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const filteredMenu = [...mockMenu, ...mockDrinks].filter(i =>
    (category === 'All' || i.category === category) &&
    i.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddItem = (item: MenuItem) => {
    setAddDialog({ open: true, item, selectedModifiers: [], selectedSeat: mockSeats[0] });
  };

  const handleAddToOrder = (item: MenuItem, modifiers: string[], seat: number) => {
    setOrder(prev => {
      const found = prev.find(i => i.id === item.id && i.seat === seat && JSON.stringify(i.modifiers) === JSON.stringify(modifiers));
      if (found) {
        return prev.map(i =>
          i.id === item.id && i.seat === seat && JSON.stringify(i.modifiers) === JSON.stringify(modifiers)
            ? { ...i, qty: i.qty + 1 }
            : i
        );
      }
      return [...prev, { ...item, qty: 1, modifiers, seat, status: 'Pending' }];
    });
    setAddDialog({ open: false });
  };

  const handleRemoveItem = (idx: number) => {
    setOrder(prev => prev.map((i, iidx) => iidx === idx ? { ...i, qty: Math.max(1, i.qty - 1) } : i));
  };

  const handleDeleteItem = (idx: number) => {
    setOrder(prev => prev.filter((_, iidx) => iidx !== idx));
  };

  const handleTableChange = (id: string) => {
    setSelectedTable(id);
    setOrder([]); // Reset order for new table (mock logic)
    setSent(false);
  };

  const handleSend = async () => {
    try {
      setLoading(true);
      // Log transaction payload
      const transactionPayload = {
        golf_course_id: 1, // This should come from your app config
        transaction_type: 'food_and_beverage',
        status: 'pending'
      };
      console.log('Creating transaction with payload:', transactionPayload);
      // Create transaction
      const transaction = await transactions.create(transactionPayload);
      setCurrentTransactionId(transaction.id);

      // Log item transaction payloads
      const itemTxPayloads = order.map(item => ({
        quantity: item.qty,
        food_and_beverage_item_id: parseInt(item.id),
        transaction_id: transaction.id,
        order_status: 'Pending',
        golfer_id: selectedGolfer?.id,
        claimed_by_employee_id: user?.id || 1,
        location: item.route === 'kitchen' ? 'Grill' : 'Bar'
      }));
      console.log('Creating item transactions with payloads:', itemTxPayloads);
      // Create item transactions
      await Promise.all(itemTxPayloads.map(payload => itemTransactions.create(payload)));

      setOrderState('sent');
      setOrder(prev => prev.map(i => ({ ...i, status: 'Sent' })));
    } catch (err) {
      setError('Failed to send order to kitchen');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleKitchenReady = async () => {
    if (!currentTransactionId) return;
    try {
      setLoading(true);
      const transactions = await itemTransactions.getByTransactionId(currentTransactionId);
      const updatePromises = transactions.map((item: ItemTransaction) => 
        itemTransactions.update(item.id, { 
          order_status: 'Ready',
          claimed_by_employee_id: user?.id || 1
        })
      );
      await Promise.all(updatePromises);
      setOrderState('ready');
      setOrder(prev => prev.map(i => ({ ...i, status: 'Ready' })));
    } catch (err) {
      setError('Failed to update order status');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleServed = async () => {
    if (!currentTransactionId) return;
    try {
      setLoading(true);
      const transactions = await itemTransactions.getByTransactionId(currentTransactionId);
      const updatePromises = transactions.map((item: ItemTransaction) => 
        itemTransactions.update(item.id, { 
          order_status: 'Served',
          delivered_by_employee_id: user?.id || 1,
          delivery_time: Date.now()
        })
      );
      await Promise.all(updatePromises);
      setOrderState('served');
      setOrder(prev => prev.map(i => ({ ...i, status: 'Served' })));
    } catch (err) {
      setError('Failed to update order status');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckout = () => {
    setCheckoutDialog(true);
  };

  const handleCheckoutComplete = async () => {
    if (!currentTransactionId) return;
    try {
      setLoading(true);
      const itemTxs: ItemTransaction[] = await itemTransactions.getByTransactionId(currentTransactionId);
      await Promise.all(itemTxs.map((item: ItemTransaction) => 
        itemTransactions.update(item.id, { 
          order_status: 'Delivered',
          delivered_by_employee_id: user?.id || 1,
          delivery_time: Date.now()
        })
      ));
      await transactions.update(currentTransactionId, { status: 'completed' });
      setOrder([]);
      setOrderState('pending');
      setCurrentTransactionId(null);
      setCheckoutDialog(false);
    } catch (err) {
      setError('Failed to complete checkout');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getOrderBySeat = () => {
    const seats = Array.from(new Set(order.map(item => item.seat)));
    return seats.map(seat => ({
      seat,
      items: order.filter(item => item.seat === seat),
      total: order
        .filter(item => item.seat === seat)
        .reduce((sum, item) => sum + (item.price * item.qty), 0)
    }));
  };

  const renderBottomButton = () => {
    switch (orderState) {
      case 'pending':
        return (
          <Button
            variant="contained"
            color="success"
            size="large"
            startIcon={<ReceiptIcon />}
            sx={{ borderRadius: 2 }}
            onClick={handleSend}
            disabled={order.length === 0}
          >
            Send to Kitchen/Bar
          </Button>
        );
      case 'sent':
        return (
          <Button
            variant="contained"
            color="warning"
            size="large"
            startIcon={<KitchenIcon />}
            sx={{ borderRadius: 2 }}
            onClick={handleKitchenReady}
          >
            Simulate Kitchen Ready
          </Button>
        );
      case 'ready':
        return (
          <Button
            variant="contained"
            color="info"
            size="large"
            startIcon={<RestaurantMenuIcon />}
            sx={{ borderRadius: 2 }}
            onClick={handleServed}
          >
            Simulate Served to Table
          </Button>
        );
      case 'served':
        return (
          <Button
            variant="contained"
            color="primary"
            size="large"
            startIcon={<ReceiptIcon />}
            sx={{ borderRadius: 2 }}
            onClick={handleCheckout}
          >
            Checkout
          </Button>
        );
    }
  };

  const total = order.reduce((sum, i) => sum + i.price * i.qty, 0);

  return (
    <Box sx={{ display: 'flex', height: '100vh', bgcolor: '#181f2a' }}>
      {loading && (
        <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, bgcolor: 'rgba(0,0,0,0.5)', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 9999 }}>
          <CircularProgress />
        </Box>
      )}
      {error && (
        <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
          <Alert severity="error" onClose={() => setError(null)}>{error}</Alert>
        </Snackbar>
      )}
      {/* Left: Menu Grid */}
      <Box sx={{ width: 340, bgcolor: '#232b3b', p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h6" color="white" sx={{ mb: 1 }}>Menu</Typography>
        <Autocomplete
          options={golfers}
          getOptionLabel={(option) => `${option.name} (${option.email})`}
          value={selectedGolfer}
          onChange={(_, newValue) => setSelectedGolfer(newValue)}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Select Golfer (Optional)"
              variant="outlined"
              size="small"
              sx={{ bgcolor: '#fff', borderRadius: 1 }}
            />
          )}
        />
        <Box sx={{ mb: 2 }}>
          <Grid container spacing={1}>
            <Grid item xs={3}>
              <Button
                fullWidth
                variant={category === 'All' ? 'contained' : 'outlined'}
                onClick={() => setCategory('All')}
                sx={{
                  color: category === 'All' ? 'white' : '#fff',
                  borderColor: '#fff',
                  '&:hover': { borderColor: 'primary.main' }
                }}
              >
                All
              </Button>
            </Grid>
            {allCategories.map(cat => (
              <Grid item xs={3} key={cat}>
                <Button
                  fullWidth
                  variant={category === cat ? 'contained' : 'outlined'}
                  onClick={() => setCategory(cat)}
                  sx={{
                    color: category === cat ? 'white' : '#fff',
                    borderColor: '#fff',
                    '&:hover': { borderColor: 'primary.main' }
                  }}
                >
                  {cat}
                </Button>
              </Grid>
            ))}
          </Grid>
        </Box>
        <TextField
          size="small"
          placeholder="Search menu..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          sx={{ mb: 2, bgcolor: '#fff', borderRadius: 1 }}
        />
        <SelectTable
          selectedTable={selectedTable}
          onChange={handleTableChange}
          tables={tablesList}
        />
        <Grid container spacing={1}>
          {filteredMenu.map(item => (
            <Grid item xs={6} key={item.id}>
              <Button
                fullWidth
                sx={{
                  bgcolor: item.color,
                  color: '#fff',
                  fontWeight: 600,
                  fontSize: 16,
                  borderRadius: 2,
                  minHeight: 56,
                  boxShadow: '0 2px 8px #0002',
                  '&:hover': { bgcolor: item.color, opacity: 0.9 },
                }}
                onClick={() => !sent && handleAddItem({
                  id: item.id,
                  name: item.name,
                  price: item.price,
                  category: (item as any).category ?? '',
                  color: (item as any).color ?? '',
                  route: item.route as 'kitchen' | 'bar',
                  modifiers: item.modifiers,
                })}
                disabled={sent}
              >
                {item.name}
                <Box component="span" sx={{ float: 'right', fontWeight: 400, fontSize: 14, ml: 1 }}>
                  {item.price > 0 ? `$${item.price.toFixed(2)}` : item.price < 0 ? `-${Math.abs(item.price)}%` : ''}
                </Box>
              </Button>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Center: Table/Order Tab */}
      <Box sx={{ flex: 1, bgcolor: '#181f2a', p: 3, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <RestaurantMenuIcon sx={{ color: '#fff' }} />
          <Typography variant="h5" color="white">Table Order</Typography>
          <Box sx={{ flex: 1 }} />
          <SelectTable selectedTable={selectedTable} onChange={handleTableChange} tables={tablesList} />
        </Box>
        <Paper sx={{ flex: 1, p: 2, bgcolor: '#232b3b', borderRadius: 3, boxShadow: 3, mb: 2, overflow: 'auto' }}>
          <List>
            {order.length === 0 && (
              <ListItem>
                <ListItemText primary={<Typography color="text.secondary">No items added yet.</Typography>} />
              </ListItem>
            )}
            {order.map((item, idx) => (
              <ListItem key={idx}>
                <Chip label={item.qty} color="primary" sx={{ mr: 2, minWidth: 32 }} />
                <ListItemText
                  primary={<Box sx={{ color: 'white', display: 'flex', alignItems: 'center', gap: 1 }}>
                    {item.name}
                    {item.route === 'kitchen' ? <Tooltip title="Kitchen"><KitchenIcon fontSize="small" sx={{ color: '#f59e42' }} /></Tooltip> : <Tooltip title="Bar"><LocalBarIcon fontSize="small" sx={{ color: '#3b82f6' }} /></Tooltip>}
                    <Chip label={`Seat ${item.seat}`} size="small" sx={{ ml: 1, bgcolor: '#334155', color: '#fff' }} />
                  </Box>}
                  secondary={<Box sx={{ color: '#a3a3a3', display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                    <span>${item.price.toFixed(2)} each</span>
                    {item.modifiers && item.modifiers.length > 0 && (
                      <span>Modifiers: {item.modifiers.join(', ')}</span>
                    )}
                  </Box>}
                />
                <Chip
                  label={item.status}
                  size="small"
                  color={item.status === 'Pending' ? 'warning' : item.status === 'Sent' ? 'info' : 'success'}
                  sx={{ mr: 1 }}
                />
                {!sent && (
                  <ListItemSecondaryAction>
                    <IconButton onClick={() => handleRemoveItem(idx)} size="small" color="primary"><RemoveIcon /></IconButton>
                    <IconButton onClick={() => handleAddItem({
                      id: item.id,
                      name: item.name,
                      price: item.price,
                      category: (item as any).category ?? '',
                      color: (item as any).color ?? '',
                      route: item.route as 'kitchen' | 'bar',
                      modifiers: item.modifiers,
                    })} size="small" color="primary"><AddIcon /></IconButton>
                    <IconButton onClick={() => handleDeleteItem(idx)} size="small" color="error"><DeleteIcon /></IconButton>
                  </ListItemSecondaryAction>
                )}
              </ListItem>
            ))}
          </List>
        </Paper>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
          <Typography variant="h6" color="white">Total: ${total.toFixed(2)}</Typography>
          {renderBottomButton()}
        </Box>
      </Box>

      {/* Add Item Dialog for Modifiers/Seat */}
      <Dialog open={addDialog.open} onClose={() => setAddDialog({ open: false })}>
        <DialogTitle>Add to Order</DialogTitle>
        <DialogContent>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>{addDialog.item?.name}</Typography>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>Modifiers</Typography>
            {addDialog.item?.modifiers?.length ? (
              addDialog.item.modifiers.map((mod: string) => (
                <FormControlLabel
                  key={mod}
                  control={
                    <Checkbox
                      checked={addDialog.selectedModifiers?.includes(mod) || false}
                      onChange={e => {
                        setAddDialog(prev => ({
                          ...prev,
                          selectedModifiers: e.target.checked
                            ? [...(prev.selectedModifiers || []), mod]
                            : (prev.selectedModifiers || []).filter((m: string) => m !== mod),
                        }));
                      }}
                    />
                  }
                  label={mod}
                />
              ))
            ) : (
              <Typography variant="body2" color="text.secondary">No modifiers</Typography>
            )}
          </Box>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Seat</InputLabel>
            <Select
              value={addDialog.selectedSeat || mockSeats[0]}
              label="Seat"
              onChange={e => setAddDialog(prev => ({ ...prev, selectedSeat: Number(e.target.value) }))}
            >
              {mockSeats.map(seat => (
                <MenuItem key={seat} value={seat}>Seat {seat}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialog({ open: false })}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => handleAddToOrder(
              {
                id: addDialog.item!.id,
                name: addDialog.item!.name,
                price: addDialog.item!.price,
                category: (addDialog.item as any).category ?? '',
                color: (addDialog.item as any).color ?? '',
                route: addDialog.item!.route as 'kitchen' | 'bar',
                modifiers: addDialog.selectedModifiers || [],
              },
              addDialog.selectedModifiers || [],
              addDialog.selectedSeat || mockSeats[0]
            )}
          >
            Add to Order
          </Button>
        </DialogActions>
      </Dialog>

      {/* Checkout Dialog */}
      <Dialog open={checkoutDialog} onClose={() => setCheckoutDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Checkout</DialogTitle>
        <DialogContent>
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <RadioGroup
              value={splitType}
              onChange={(e) => setSplitType(e.target.value as 'single' | 'by_seat')}
            >
              <FormControlLabel
                value="single"
                control={<Radio />}
                label="Single Check"
              />
              <FormControlLabel
                value="by_seat"
                control={<Radio />}
                label="Split by Seat"
              />
            </RadioGroup>
          </FormControl>

          {splitType === 'single' ? (
            <Box>
              <Typography variant="h6" gutterBottom>Total: ${total.toFixed(2)}</Typography>
              <List>
                {order.map((item, idx) => (
                  <ListItem key={idx}>
                    <ListItemText
                      primary={item.name}
                      secondary={`Seat ${item.seat} - ${item.modifiers?.join(', ') || 'No modifiers'}`}
                    />
                    <Typography>${(item.price * item.qty).toFixed(2)}</Typography>
                  </ListItem>
                ))}
              </List>
            </Box>
          ) : (
            <Box>
              {getOrderBySeat().map(({ seat, items, total }) => (
                <Box key={seat} sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>Seat {seat}</Typography>
                  <List>
                    {items.map((item, idx) => (
                      <ListItem key={idx}>
                        <ListItemText
                          primary={item.name}
                          secondary={item.modifiers?.join(', ') || 'No modifiers'}
                        />
                        <Typography>${(item.price * item.qty).toFixed(2)}</Typography>
                      </ListItem>
                    ))}
                  </List>
                  <Typography variant="subtitle1" sx={{ textAlign: 'right', mt: 1 }}>
                    Subtotal: ${total.toFixed(2)}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCheckoutDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleCheckoutComplete}
          >
            Complete Checkout
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const SelectTable: React.FC<{ selectedTable: string; onChange: (id: string) => void; tables: { id: string; name: string }[] }> = ({ selectedTable, onChange, tables }) => (
  <TextField
    select
    size="small"
    value={selectedTable}
    onChange={e => onChange(e.target.value)}
    sx={{ minWidth: 140, bgcolor: '#fff', borderRadius: 1 }}
  >
    {tables.map(t => (
      <MenuItem key={t.id} value={t.id}>{t.name}</MenuItem>
    ))}
  </TextField>
);

export default RestaurantPOSPage; 