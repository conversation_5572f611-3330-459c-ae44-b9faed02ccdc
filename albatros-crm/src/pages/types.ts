import { IconType } from '../components/MetricsCard/types';

export interface DashboardPageProps {
  courseId: string;
  timeView: 'daily' | 'weekly' | 'monthly';
  setTimeView: (view: 'daily' | 'weekly' | 'monthly') => void;
}

export interface SetupFormData {
  timeInterval: string;
  maxGolfers: string;
  daysOpen: string;
  holidays: string;
  rules: string;
}

export interface IconCircleProps {
  icon: React.ReactElement<{ bgcolor: string }>;
}

export interface MetricTextProps {
  title: string;
  value: string | number;
}

export interface ChangeIndicatorProps {
  change: string;
  period: string;
}

export interface MetricCardProps {
  icon: React.ReactElement<{ bgcolor: string }>;
  title: string;
  value: string | number;
  change: string;
  period: string;
}

export interface MetricGridProps {
  metrics: MetricCardProps[];
}

export interface TeeTimeSlot {
  time: string;
  bookings: number;
  percentage: number;
}

export interface TeeTimeSummary {
  bookedSlots: number;
  availableSlots: number;
}

export interface TeeTimeData {
  summary: TeeTimeSummary;
}

export interface AnalyticsMetrics {
  members: {
    total: number;
    change: number;
  };
  golferReturnRate: {
    percentage: number;
    change: number;
  };
  obsoleteGolfers: {
    total: number;
    change: number;
  };
  revenue: {
    total: number;
    change: number;
  };
}

export interface RevenueBreakdown {
  proShop: number;
  teeTime: number;
  foodBeverage: number;
  events: number;
  other: number;
}

export interface AnalyticsData {
  trends: {
    daily: number[];
  };
  metrics: AnalyticsMetrics;
  teeTimeDistribution: TeeTimeSlot[];
  revenueBreakdown: RevenueBreakdown;
}

export interface AnalyticsPageProps {
  courseId: string;
}

export interface User {
  id: number;
  name: string;
  role: string;
  lastLogin: string;
  nextSession: string;
  scoreImprovement: string;
  avatar?: string;
}

export interface ModulePermission {
  module: string;
  role: string;
  enabled: boolean;
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  emailAddress: string;
  role: string;
  permissions: ModulePermission[];
}

export interface SettingsPageProps {
  courseId: string;
}

export type TabValue = 'profile' | 'users' | 'waitlist' | 'integrations' | 'billing' | 'audit' | 'blank';

export interface Golfer {
  id: string | number;
  name: string;
  phone: string;
  email?: string;
  stars: number;
  isMember: boolean;
  lastPlayDate: string;
  upcomingPlayDate: string;
  avatar?: string | null;
  avatarColor?: string;
  initials?: string;
  address?: string;
  albatrossStarScore?: string;
  nps?: string;
  events?: string;
  foodDrink?: string;
  student?: string;
}

export interface GolferFormData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  stars: number;
  isMember: boolean;
  upcomingPlay: string;
  lastSeen: string;
  albatrossStarScore: string;
  nps: string;
  events: string;
  foodDrink: string;
  student: string;
  avatar?: string;
  avatarColor?: string;
}

export interface Filters {
  stars: number[];
  membership: ('member' | 'non-member')[];
  playStatus: ('upcoming' | 'recent')[];
}

export interface SortOption {
  id: string;
  label: string;
  value: 'name' | 'stars' | 'lastPlayDate';
  direction: 'asc' | 'desc';
}

export interface RosterPageProps {
  courseId: string;
}

export interface GolferMetrics {
  members: {
    value: number;
    change: number;
  };
  golferReturnRate: {
    percentage: number;
    change: number;
  };
  obsoleteGolfers: {
    value: number;
    change: number;
  };
}

export interface MenuItemCustomization {
  name: string;
  options: {
    label: string;
    price?: number;
  }[];
}

export interface MenuItemNutritionalInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

export interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  description: string;
  image?: string;
  mobileApp?: boolean;
  customizations?: MenuItemCustomization[];
  ingredients?: string[];
  allergens?: string[];
  membershipDiscount?: number;
  preparationTime?: number;
  nutritionalInfo?: MenuItemNutritionalInfo;
}

export interface NineteenthHolePageProps {
  courseId: string;
}

export interface ProShopItemSpecifications {
  clubType?: string;
  brand?: string;
  model?: string;
  material?: string;
  flex?: string;
  loft?: string;
  shaft?: string;
}

export interface ProShopItem {
  id: string;
  name: string;
  sku?: string;
  description?: string;
  image?: string;
  cost: number;
  price: number;
  quantity: number;
  category: string;
  mobileApp?: boolean;
  specifications?: ProShopItemSpecifications;
  sizes?: string[];
  colors?: string[];
  membershipDiscount?: number;
  reorderPoint?: number;
  supplier?: string;
  lastOrdered?: string;
  nextOrder?: string;
}

export interface ProShopPageProps {
  courseId: string;
}

export type ProShopTabValue = 'inventory' | 'add' | 'edit' | 'order';

export interface BackOfficePageProps {
  courseId: string;
}

export interface EmployeeShift {
  id: string;
  employeeName: string;
  role: string;
  avatar?: string;
  department: string;
  clockIn: string;
  clockOut?: string;
  hoursWorked?: number;
  status: 'active' | 'completed' | 'upcoming';
}

export interface DepartmentPayroll {
  name: string;
  amount: number;
  hours: number;
  employeeCount: number;
}

export interface PayrollPeriod {
  start: string;
  end: string;
  departments: DepartmentPayroll[];
}

export interface MarketingTemplatePerformance {
  opens: number;
  clicks: number;
  conversions: number;
}

export interface MarketingTemplate {
  id: string;
  name: string;
  type: 'Email' | 'SMS';
  lastModified: string;
  performance?: MarketingTemplatePerformance;
}

export interface BackOfficeData {
  shifts: EmployeeShift[];
  payroll: {
    currentPeriod: PayrollPeriod;
  };
  marketingTemplates: MarketingTemplate[];
}

export interface Metric {
  icon: IconType;
  title: string;
  value: string;
  change: string;
  period: string;
}

export type CalendarViewMode = 'daily' | 'weekly' | 'monthly';

export interface CalendarPageProps {
  courseId: string;
}

export interface QuickAction {
  icon: React.ReactNode;
  tooltip: string;
  action: () => void;
}

export interface MonthlyData {
  value: number;
  percentage: string;
}

export interface QuarterlyData {
  quarter: string;
  value: number;
  percentage: string;
  revenue: string;
}

export interface YearlyData {
  year: string;
  progress: number;
  revenue: string;
  growth: string;
}

export interface TeeTimesPageProps {
  courseId: string;
}

export interface AISuggestiveModalProps {
  open: boolean;
  onClose: () => void;
  onAction: (action: QuickAction) => void;
}

export interface OverviewProps {
  data: MonthlyData | QuarterlyData | YearlyData;
  onTimeRangeChange: (range: 'monthly' | 'quarterly' | 'yearly') => void;
} 