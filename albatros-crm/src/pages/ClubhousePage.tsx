import React, { useState, useRef } from 'react';
import {
  Box, Typography, Tabs, Tab, Paper, Container, styled, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Modal, Card, CardContent, TextField, IconButton, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3),
  '& .MuiTab-root': {
    textTransform: 'none',
    minWidth: 120,
    padding: theme.spacing(2),
    color: theme.palette.text.secondary,
    fontSize: '1rem',
    fontWeight: 500,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

// Generate more mock members with membership numbers
const generateMembers = (count: number) => {
  const names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Lily Carter', 'Jacob Mitchell', 'Zoe Perez', 'Ethan Roberts',
    'Layla Turner', 'Michael Phillips', 'Scarlett Campbell', 'Jackson Parker'
  ];
  return Array.from({ length: count }).map((_, i) => ({
    id: i + 1,
    membershipNumber: `M-${1000 + i}`,
    name: names[i % names.length],
    startDate: `202${i % 5}-0${(i % 9) + 1}-1${i % 9}`,
    family: i % 3 === 0 ? [
      { name: `${names[(i + 1) % names.length]}`, relation: 'Spouse' },
      { name: `${names[(i + 2) % names.length]}`, relation: 'Child' }
    ] : [],
    email: `${names[i % names.length].toLowerCase().replace(' ', '.')}@email.com`,
    phone: `555-1${i % 10}${i % 10}-${1000 + i}`,
    type: i % 3 === 0 ? 'Family' : 'Individual',
    status: 'Active',
  }));
};

const mockMembers = generateMembers(40);

const mockPending = [
  { id: 101, name: 'Alex Green', requested: '2025-06-01', email: '<EMAIL>', type: 'Individual' },
  { id: 102, name: 'The Patel Family', requested: '2025-06-02', email: '<EMAIL>', type: 'Family' },
];

const mockBenefits = [
  { id: 1, title: '10% Off Pro Shop', description: 'All members receive 10% off in the Pro Shop.' },
  { id: 2, title: 'Priority Tee Times', description: 'Members can book tee times 14 days in advance.' },
  { id: 3, title: 'Free Range Balls', description: 'Unlimited range balls for all members.' },
];

const PAGE_SIZE = 10;

const ClubhousePage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState('member');
  const [members, setMembers] = useState(mockMembers);
  const [pending, setPending] = useState(mockPending);
  const [benefits, setBenefits] = useState(mockBenefits);
  const [selectedMember, setSelectedMember] = useState<any | null>(null);
  const [benefitForm, setBenefitForm] = useState({ title: '', description: '' });
  const [search, setSearch] = useState('');
  const [visibleCount, setVisibleCount] = useState(PAGE_SIZE);
  const rosterRef = useRef<HTMLDivElement>(null);
  const [confirmDeactivateOpen, setConfirmDeactivateOpen] = useState(false);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setCurrentTab(newValue);
  };

  // Member Modal
  const handleOpenMember = (member: any) => setSelectedMember(member);
  const handleCloseMember = () => setSelectedMember(null);

  // Pending Actions
  const handleAccept = (id: number) => {
    const member = pending.find(m => m.id === id);
    if (member) {
      setMembers([
        ...members,
        {
          id: Date.now(),
          membershipNumber: `M-${1000 + members.length}`,
          name: member.name,
          startDate: new Date().toISOString().slice(0, 10),
          family: [],
          email: member.email,
          phone: '',
          type: member.type,
          status: 'Active',
        },
      ]);
      setPending(pending.filter(m => m.id !== id));
    }
  };
  const handleDeny = (id: number) => setPending(pending.filter(m => m.id !== id));

  // Benefits Actions
  const handleAddBenefit = () => {
    if (benefitForm.title && benefitForm.description) {
      setBenefits([...benefits, { id: Date.now(), ...benefitForm }]);
      setBenefitForm({ title: '', description: '' });
    }
  };
  const handleDeleteBenefit = (id: number) => setBenefits(benefits.filter(b => b.id !== id));

  // Infinite scroll logic
  const filteredMembers = members.filter(m => m.name.toLowerCase().includes(search.toLowerCase()));
  const visibleMembers = filteredMembers.slice(0, visibleCount);
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 10 && visibleCount < filteredMembers.length) {
      setVisibleCount(v => Math.min(v + PAGE_SIZE, filteredMembers.length));
    }
  };
  // Reset visibleCount on search
  React.useEffect(() => { setVisibleCount(PAGE_SIZE); }, [search, members]);

  // Deactivate membership
  const handleRequestDeactivate = () => {
    setConfirmDeactivateOpen(true);
  };
  const handleConfirmDeactivate = () => {
    setConfirmDeactivateOpen(false);
    if (selectedMember && selectedMember.status !== 'Inactive') {
      setMembers(members => members.map(m => m.id === selectedMember.id ? { ...m, status: 'Inactive' } : m));
      setSelectedMember({ ...selectedMember, status: 'Inactive' });
    }
  };
  const handleCancelDeactivate = () => {
    setConfirmDeactivateOpen(false);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Paper sx={{ p: 3, background: '#fff' }}>
          {/* Top Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <StyledTabs value={currentTab} onChange={handleTabChange}>
              <Tab label="Member Services" value="member" />
              <Tab label="Spa" value="spa" />
              <Tab label="Tennis" value="tennis" />
            </StyledTabs>
          </Box>

          {/* Member Services Tab */}
          {currentTab === 'member' && (
            <>
              {/* Member Roster */}
              <Box sx={{ mb: 5 }}>
                <Typography variant="h6" fontWeight={700} mb={2}>Member Roster</Typography>
                <TextField
                  placeholder="Search members by name..."
                  value={search}
                  onChange={e => setSearch(e.target.value)}
                  size="small"
                  sx={{ mb: 2, width: 320 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
                <Box
                  ref={rosterRef}
                  sx={{ maxHeight: 340, overflow: 'auto', border: '1px solid #e5e7eb', borderRadius: 2, background: '#f8fafc' }}
                  onScroll={handleScroll}
                >
                  <TableContainer component={Box} sx={{ minWidth: 400 }}>
                    <Table size="small" stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell>Membership #</TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Membership Start</TableCell>
                          <TableCell>Family Size</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {visibleMembers.map(member => (
                          <TableRow key={member.id} hover sx={{ cursor: 'pointer' }} onClick={() => handleOpenMember(member)}>
                            <TableCell>{member.membershipNumber}</TableCell>
                            <TableCell>{member.name}</TableCell>
                            <TableCell>{new Date(member.startDate).toLocaleDateString()}</TableCell>
                            <TableCell>{1 + (member.family?.length || 0)}</TableCell>
                          </TableRow>
                        ))}
                        {visibleMembers.length === 0 && (
                          <TableRow><TableCell colSpan={4} align="center">No members found</TableCell></TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
                {/* Member Details Modal */}
                <Modal open={!!selectedMember} onClose={handleCloseMember}>
                  <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', bgcolor: '#fff', p: 4, borderRadius: 2, minWidth: 340, boxShadow: 24 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" fontWeight={700}>Member Details</Typography>
                      <IconButton onClick={handleCloseMember}><CloseIcon /></IconButton>
                    </Box>
                    {selectedMember && (
                      <>
                        <Typography><b>Membership #:</b> {selectedMember.membershipNumber}</Typography>
                        <Typography><b>Name:</b> {selectedMember.name}</Typography>
                        <Typography><b>Email:</b> {selectedMember.email}</Typography>
                        <Typography><b>Phone:</b> {selectedMember.phone || 'N/A'}</Typography>
                        <Typography><b>Membership Type:</b> {selectedMember.type}</Typography>
                        <Typography><b>Status:</b> {selectedMember.status}</Typography>
                        <Typography><b>Start Date:</b> {new Date(selectedMember.startDate).toLocaleDateString()}</Typography>
                        <Box mt={2}>
                          <Typography fontWeight={600}>Family Members:</Typography>
                          {selectedMember.family && selectedMember.family.length > 0 ? (
                            <ul style={{ margin: 0, paddingLeft: 18 }}>
                              {selectedMember.family.map((f: any, idx: number) => (
                                <li key={idx}>{f.name} ({f.relation})</li>
                              ))}
                            </ul>
                          ) : (
                            <Typography color="text.secondary">No family members tied to this membership.</Typography>
                          )}
                        </Box>
                        <Box mt={3} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <Button
                            variant="contained"
                            color="error"
                            onClick={handleRequestDeactivate}
                            disabled={selectedMember.status === 'Inactive'}
                          >
                            Deactivate Membership
                          </Button>
                        </Box>
                      </>
                    )}
                  </Box>
                </Modal>
              </Box>

              {/* Pending Membership Confirmations */}
              <Box sx={{ mb: 5 }}>
                <Typography variant="h6" fontWeight={700} mb={2}>Pending Membership Confirmations</Typography>
                <TableContainer component={Paper} sx={{ mb: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Requested</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {pending.length === 0 && (
                        <TableRow><TableCell colSpan={4} align="center">No pending confirmations</TableCell></TableRow>
                      )}
                      {pending.map(member => (
                        <TableRow key={member.id}>
                          <TableCell>{member.name}</TableCell>
                          <TableCell>{new Date(member.requested).toLocaleDateString()}</TableCell>
                          <TableCell>{member.type}</TableCell>
                          <TableCell>
                            <Button size="small" color="success" variant="contained" sx={{ mr: 1 }} onClick={() => handleAccept(member.id)}>Accept</Button>
                            <Button size="small" color="error" variant="outlined" onClick={() => handleDeny(member.id)}>Deny</Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>

              {/* Member Benefits */}
              <Box>
                <Typography variant="h6" fontWeight={700} mb={2}>Member Benefits</Typography>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <TextField
                    label="Benefit Title"
                    value={benefitForm.title}
                    onChange={e => setBenefitForm({ ...benefitForm, title: e.target.value })}
                    size="small"
                  />
                  <TextField
                    label="Description"
                    value={benefitForm.description}
                    onChange={e => setBenefitForm({ ...benefitForm, description: e.target.value })}
                    size="small"
                  />
                  <Button variant="contained" color="primary" onClick={handleAddBenefit} disabled={!benefitForm.title || !benefitForm.description}>
                    Add Benefit
                  </Button>
                </Box>
                <Box>
                  {benefits.length === 0 && <Typography color="text.secondary">No member benefits set up yet.</Typography>}
                  {benefits.map(benefit => (
                    <Card key={benefit.id} sx={{ mb: 2, background: '#f8fafc' }}>
                      <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography fontWeight={600}>{benefit.title}</Typography>
                          <Typography color="text.secondary">{benefit.description}</Typography>
                        </Box>
                        <Button color="error" onClick={() => handleDeleteBenefit(benefit.id)}>Delete</Button>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              </Box>
            </>
          )}

          {/* Spa Tab */}
          {currentTab === 'spa' && (
            <Box sx={{ py: 6, textAlign: 'center' }}>
              <Typography variant="h5" color="primary" fontWeight={700}>
                Spa Services Coming Soon
              </Typography>
            </Box>
          )}
          {/* Tennis Tab */}
          {currentTab === 'tennis' && (
            <Box sx={{ py: 6, textAlign: 'center' }}>
              <Typography variant="h5" color="primary" fontWeight={700}>
                Tennis Services Coming Soon
              </Typography>
            </Box>
          )}
        </Paper>
      </Box>
      <Dialog open={confirmDeactivateOpen} onClose={handleCancelDeactivate}>
        <DialogTitle>Confirm Deactivation</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to deactivate this membership? This action cannot be undone.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDeactivate}>Cancel</Button>
          <Button onClick={handleConfirmDeactivate} color="error" variant="contained">Deactivate</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ClubhousePage; 