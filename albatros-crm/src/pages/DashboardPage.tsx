import React from 'react';
import { Box, Grid, Typography } from '@mui/material';
import TeeTimesList from '../components/TeeTimesList';
import Overview from '../components/Overview/Overview';
import SatisfactionDonut from '../components/SatisfactionDonut/SatisfactionDonut';
import { DashboardPageProps } from './types';
import { MetricsCard } from '../components/MetricsCard';

const mockData = {
  value: 45,
  percentage: '+2.3%'
};

/**
 * Dashboard page component
 * Displays overview of golf course metrics and activities
 */
const DashboardPage: React.FC<DashboardPageProps> = ({ courseId, timeView, setTimeView }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="money"
            label="Earnings"
            value="$45,678"
            subtitle="Last 30 days"
            change={8}
            colorScheme="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="trending_up"
            label="Book Percentage"
            value="78%"
            subtitle="Booking rate"
            change={12}
            colorScheme="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="store"
            label="Total Sales"
            value="$12,890"
            subtitle="This month"
            change={5}
            colorScheme="info"
          />
        </Grid>
      </Grid>
      
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Overview 
            data={mockData} 
            onTimeRangeChange={(range) => console.log('Time range changed:', range)} 
            courseId={courseId}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SatisfactionDonut courseId={courseId} />
        </Grid>
        <Grid item xs={12}>
          <TeeTimesList courseId={courseId} />
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage; 