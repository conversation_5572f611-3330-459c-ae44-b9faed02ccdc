import React from 'react';
import { Box, Grid, Typography, Alert } from '@mui/material';
import TeeTimesList from '../components/TeeTimesList';
import Overview from '../components/Overview/Overview';
import SatisfactionDonut from '../components/SatisfactionDonut/SatisfactionDonut';
import { DashboardPageProps } from './types';
import { MetricsCard } from '../components/MetricsCard';
import { useDashboardOverview, useDashboardMetrics, useWeatherInfo } from '../api/hooks/useDashboard';
import { useXanoAuth } from '../api/hooks/useXanoAuth';
import LoadingState from '../components/shared/LoadingState';
import ErrorBoundary from '../components/shared/ErrorBoundary';
import config from '../config';

/**
 * Dashboard page component
 * Displays overview of golf course metrics and activities
 */
const DashboardPage: React.FC<DashboardPageProps> = ({ courseId, timeView, setTimeView }) => {
  const { isAuthenticated } = useXanoAuth();
  const golf_course_id = parseInt(config.courseId);

  // XANO API hooks
  const {
    data: dashboardOverview,
    isLoading: overviewLoading,
    error: overviewError
  } = useDashboardOverview({
    golf_course_id,
    timeframe: timeView
  });

  const {
    data: dashboardMetrics,
    isLoading: metricsLoading,
    error: metricsError
  } = useDashboardMetrics({
    golf_course_id,
    start_date: getStartDate(timeView),
    end_date: new Date().toISOString().split('T')[0]
  });

  const {
    data: weatherInfo,
    isLoading: weatherLoading
  } = useWeatherInfo({ golf_course_id });

  // Helper function to get start date based on time view
  function getStartDate(timeView: string): string {
    const now = new Date();
    switch (timeView) {
      case 'daily':
        return now.toISOString().split('T')[0];
      case 'weekly':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return weekAgo.toISOString().split('T')[0];
      case 'monthly':
      default:
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        return monthAgo.toISOString().split('T')[0];
    }
  }

  // Show authentication required state
  if (!isAuthenticated) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please log in to view the dashboard.
        </Alert>
      </Box>
    );
  }

  // Show loading state
  if (overviewLoading || metricsLoading) {
    return <LoadingState message="Loading dashboard..." />;
  }

  // Show error state
  if (overviewError || metricsError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load dashboard: {overviewError?.message || metricsError?.message}
        </Alert>
      </Box>
    );
  }

  // Extract metrics with fallbacks
  const metrics = dashboardMetrics || {};
  const overview = dashboardOverview || {};

  return (
    <ErrorBoundary>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <MetricsCard
              icon="money"
              label="Earnings"
              value={`$${(metrics.totalRevenue || 0).toLocaleString()}`}
              subtitle={`Last ${timeView === 'daily' ? 'day' : timeView === 'weekly' ? 'week' : 'month'}`}
              change={metrics.revenueChange || 0}
              colorScheme="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricsCard
              icon="trending_up"
              label="Book Percentage"
              value={`${Math.round(metrics.bookingRate || 0)}%`}
              subtitle="Booking rate"
              change={metrics.bookingChange || 0}
              colorScheme="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricsCard
              icon="store"
              label="Total Bookings"
              value={(metrics.totalBookings || 0).toString()}
              subtitle="This period"
              change={metrics.golferChange || 0}
              colorScheme="info"
            />
          </Grid>
        </Grid>

        <Grid container spacing={3} sx={{ mt: 3 }}>
          <Grid item xs={12} md={6}>
            <Overview
              data={overview.revenueData || { value: 0, percentage: '0%' }}
              onTimeRangeChange={(range) => setTimeView(range as any)}
              courseId={courseId}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <SatisfactionDonut courseId={courseId} />
          </Grid>
          <Grid item xs={12}>
            <TeeTimesList courseId={courseId} />
          </Grid>
        </Grid>
      </Box>
    </ErrorBoundary>
  );
};

export default DashboardPage; 