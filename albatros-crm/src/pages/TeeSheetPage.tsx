import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, Typo<PERSON>, Grid, TextField, ToggleButton, ToggleButtonGroup, IconButton, Divider, MenuItem, Select, FormControl, InputLabel } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import PeopleIcon from '@mui/icons-material/People';
import FlagIcon from '@mui/icons-material/Flag';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DirectionsWalkIcon from '@mui/icons-material/DirectionsWalk';
import FilterListIcon from '@mui/icons-material/FilterList';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import CloseIcon from '@mui/icons-material/Close';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import DeleteIcon from '@mui/icons-material/Delete';
import { useCourseConfig } from '../components/Setup/CourseConfigContext';
import { WeekDay } from '../components/Setup/types';

// 12-hour AM/PM time options
const teeTimeOptions = [
  '12:00 AM', '12:10 AM', '12:20 AM', '12:30 AM', '12:40 AM', '12:50 AM',
  '1:00 AM', '1:10 AM', '1:20 AM', '1:30 AM', '1:40 AM', '1:50 AM',
  '2:00 AM', '2:10 AM', '2:20 AM', '2:30 AM', '2:40 AM', '2:50 AM',
  '3:00 AM', '3:10 AM', '3:20 AM', '3:30 AM', '3:40 AM', '3:50 AM',
  '4:00 AM', '4:10 AM', '4:20 AM', '4:30 AM', '4:40 AM', '4:50 AM',
  '5:00 AM', '5:10 AM', '5:20 AM', '5:30 AM', '5:40 AM', '5:50 AM',
  '6:00 AM', '6:10 AM', '6:20 AM', '6:30 AM', '6:40 AM', '6:50 AM',
  '7:00 AM', '7:10 AM', '7:20 AM', '7:30 AM', '7:40 AM', '7:50 AM',
  '8:00 AM', '8:10 AM', '8:20 AM', '8:30 AM', '8:40 AM', '8:50 AM',
  '9:00 AM', '9:10 AM', '9:20 AM', '9:30 AM', '9:40 AM', '9:50 AM',
  '10:00 AM', '10:10 AM', '10:20 AM', '10:30 AM', '10:40 AM', '10:50 AM',
  '11:00 AM', '11:10 AM', '11:20 AM', '11:30 AM', '11:40 AM', '11:50 AM',
  '12:00 PM'
];

// Helper function to convert 12h AM/PM to 24h format
function to24Hour(time: string): string {
  if (!time) return '00:00';
  const [timePart, period] = time.split(' ');
  if (!timePart || !period) return '00:00';
  const [hour, minute] = timePart.split(':').map(Number);
  let hour24 = hour;
  if (period === 'PM' && hour !== 12) hour24 += 12;
  if (period === 'AM' && hour === 12) hour24 = 0;
  return `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
}

// Helper function to convert 24h to 12h AM/PM format
function toAmPm(time: string): string {
  const [h, m] = time.split(':').map(Number);
  const period = h >= 12 ? 'PM' : 'AM';
  const hour = h % 12 || 12;
  return `${hour}:${m.toString().padStart(2, '0')} ${period}`;
}

// Helper function to get time slot (morning, afternoon, evening)
function getTimeSlot(time: string): 'morning' | 'afternoon' | 'evening' {
  const hour = parseInt(time.split(':')[0]);
  if (hour < 12) return 'morning';
  if (hour < 17) return 'afternoon';
  return 'evening';
}

// Generate mock tee times with conditional rules applied
const generateMockTeeTimes = (
  selectedDate: Date,
  operatingHours: any,
  maxGolfers: number,
  teeTimeInterval: string,
  teeTimeCosts: any,
  closures: any[],
  conditionalRules: any[]
) => {
  const weekDays = [WeekDay.Sunday, WeekDay.Monday, WeekDay.Tuesday, WeekDay.Wednesday, WeekDay.Thursday, WeekDay.Friday, WeekDay.Saturday];
  const currentWeekDay = weekDays[selectedDate.getDay()] as WeekDay;
  
  // Check if course is closed on this date
  const dateString = selectedDate.toISOString().split('T')[0];
  const isClosed = closures.some(closure => closure.date === dateString);
  if (isClosed) return [];
  
  // Get operating hours for the day
  const { open, close } = operatingHours[currentWeekDay] || { open: '06:00', close: '18:00' };
  
  // Apply conditional rules
  let adjustedOpen = open;
  let adjustedClose = close;
  let additionalInterval = 0;
  
  conditionalRules.forEach(rule => {
    const ruleStart = new Date(rule.timeline.start);
    const ruleEnd = new Date(rule.timeline.end);
    
    if (selectedDate >= ruleStart && selectedDate <= ruleEnd) {
      if (rule.condition.type === 'dayOfWeek' && 
          (rule.condition.value === currentWeekDay || rule.condition.value === WeekDay.EveryDay)) {
        
        if (rule.effect.type === 'delayOpening') {
          adjustedOpen = rule.effect.value;
        } else if (rule.effect.type === 'earlyClosing') {
          adjustedClose = rule.effect.value;
        } else if (rule.effect.type === 'addTimeInterval') {
          additionalInterval = parseInt(rule.effect.value) || 0;
        }
      }
    }
  });
  
  // Generate time slots based on interval
  const interval = parseInt(teeTimeInterval) + additionalInterval;
  const timeSlots = [];
  
  let currentTime = new Date(`2000-01-01T${adjustedOpen}:00`);
  const closeTime = new Date(`2000-01-01T${adjustedClose}:00`);
  
  while (currentTime < closeTime) {
    const timeString = currentTime.toTimeString().slice(0, 5);
    const timeAmPm = toAmPm(timeString);
    
    // Skip if outside operating hours
    if (timeString >= adjustedOpen && timeString <= adjustedClose) {
      timeSlots.push(timeAmPm);
    }
    
    currentTime.setMinutes(currentTime.getMinutes() + interval);
  }
  
  return timeSlots.map(time => {
    const time24h = to24Hour(time);
    const timeSlot = getTimeSlot(time24h);
    // Map WeekDay enum to DayOfWeek string for tee time costs lookup
    const dayOfWeek = currentWeekDay as string;
    const basePrice = teeTimeCosts[dayOfWeek]?.[timeSlot] || 50;
    
    // Random availability (80% chance of being available)
    const isAvailable = Math.random() > 0.2;
    
    // Random golfer count (1 to maxGolfers)
    const golferCount = isAvailable ? Math.floor(Math.random() * maxGolfers) + 1 : 0;
    
    // Random holes (70% chance of 18 holes)
    const holes = Math.random() > 0.3 ? 18 : 9;
    
    // Random transportation (60% chance of cart)
    const transportation = Math.random() > 0.4 ? 'cart' : 'walk';
    
    // Rate type based on time and day
    const isWeekend = currentWeekDay === WeekDay.Saturday || currentWeekDay === WeekDay.Sunday;
    const isPeakTime = timeSlot === 'morning' || (timeSlot === 'afternoon' && !isWeekend);
    const rateType = isPeakTime ? 'standard' : 'discount';
    
    // Calculate final price with variations
    let finalPrice = basePrice;
    if (transportation === 'cart') finalPrice += 15;
    if (holes === 9) finalPrice *= 0.6;
    if (rateType === 'discount') finalPrice *= 0.8;
    
    return {
      time,
      available: isAvailable,
      golfers: golferCount,
      holes,
      transportation,
      rateType,
      price: Math.round(finalPrice),
      maxGolfers: maxGolfers,
      remainingSlots: isAvailable ? maxGolfers - golferCount : 0
    };
  });
};

const months = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

interface TeeSheetPageProps {
  cart: any[];
  setCart: React.Dispatch<React.SetStateAction<any[]>>;
  cartModalOpen: boolean;
  setCartModalOpen: (open: boolean) => void;
  handleRemoveFromCart: (idx: number) => void;
}

const TeeSheetPage: React.FC<TeeSheetPageProps> = ({ cart, setCart, cartModalOpen, setCartModalOpen, handleRemoveFromCart }) => {
  const {
    operatingHours,
    maxGolfers,
    teeTimeInterval,
    teeTimeCosts,
    closures,
    conditionalRules
  } = useCourseConfig();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [price, setPrice] = useState([0, 300]);
  const [selectedGolfers, setSelectedGolfers] = useState<'any' | 1 | 2 | 3 | 4>('any');
  const [selectedHoles, setSelectedHoles] = useState<'any' | 9 | 18>('any');
  const [transportation, setTransportation] = useState<'any' | 'cart' | 'walk'>('any');
  const [rateType, setRateType] = useState<'any' | 'standard' | 'discount'>('any');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [bookingModalOpen, setBookingModalOpen] = useState(false);
  const [selectedTeeTime, setSelectedTeeTime] = useState<any>(null);
  const [selectedBookingGolfers, setSelectedBookingGolfers] = useState<number | null>(null);

  // Reset all filters function
  const resetAllFilters = () => {
    setPrice([0, 300]);
    setSelectedGolfers('any');
    setSelectedHoles('any');
    setTransportation('any');
    setRateType('any');
    if (openAmPm) setStartTime(openAmPm);
    if (closeAmPm) setEndTime(closeAmPm);
  };

  // Get the current weekday string
  const weekDays = [WeekDay.Sunday, WeekDay.Monday, WeekDay.Tuesday, WeekDay.Wednesday, WeekDay.Thursday, WeekDay.Friday, WeekDay.Saturday];
  const currentWeekDay = weekDays[selectedDate.getDay()] as WeekDay;
  // Get open/close for the current day
  const { open, close } = operatingHours[currentWeekDay] || { open: '06:00', close: '18:00' };
  // Convert 24h open/close to 12h AM/PM for comparison
  function toAmPm(time: string) {
    const [h, m] = time.split(':').map(Number);
    const period = h >= 12 ? 'PM' : 'AM';
    const hour = h % 12 || 12;
    return `${hour}:${m.toString().padStart(2, '0')} ${period}`;
  }
  const openAmPm = toAmPm(open);
  const closeAmPm = toAmPm(close);

  // Initialize start/end times with operating hours if not set
  useEffect(() => {
    if (!startTime && openAmPm) setStartTime(openAmPm);
    if (!endTime && closeAmPm) setEndTime(closeAmPm);
  }, [openAmPm, closeAmPm, startTime, endTime]);

  // Generate time options within operating hours
  const operatingTimeOptions = useMemo(() => {
    const options = [];
    let currentTime = new Date(`2000-01-01T${open}:00`);
    const closeTime = new Date(`2000-01-01T${close}:00`);
    
    while (currentTime <= closeTime) {
      const timeString = currentTime.toTimeString().slice(0, 5);
      options.push(toAmPm(timeString));
      currentTime.setMinutes(currentTime.getMinutes() + 15); // 15-minute intervals
    }
    
    return options;
  }, [open, close]);

  // Generate mock tee times for the day
  const mockTeeTimes = React.useMemo(() => {
    console.log('Generating tee times with params:', {
      selectedDate: selectedDate.toDateString(),
      operatingHours,
      maxGolfers,
      teeTimeInterval,
      teeTimeCosts,
      closures,
      conditionalRules
    });
    
    const generated = generateMockTeeTimes(selectedDate, operatingHours, maxGolfers, teeTimeInterval, teeTimeCosts, closures, conditionalRules);
    
    // Fallback: if no tee times generated, create some basic ones
    if (generated.length === 0) {
      console.log('No tee times generated, creating fallback times');
      const fallbackTimes = [];
      for (let hour = 6; hour <= 17; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          const timeAmPm = toAmPm(timeString);
          fallbackTimes.push({
            time: timeAmPm,
            available: Math.random() > 0.2,
            golfers: Math.floor(Math.random() * maxGolfers) + 1,
            holes: Math.random() > 0.3 ? 18 : 9,
            transportation: Math.random() > 0.4 ? 'cart' : 'walk',
            rateType: Math.random() > 0.5 ? 'standard' : 'discount',
            price: Math.floor(Math.random() * 100) + 30,
            maxGolfers: maxGolfers,
            remainingSlots: Math.floor(Math.random() * maxGolfers) + 1
          });
        }
      }
      return fallbackTimes;
    }
    
    return generated;
  }, [selectedDate, operatingHours, maxGolfers, teeTimeInterval, teeTimeCosts, closures, conditionalRules]);

  // Debug logging
  console.log('Mock tee times generated:', mockTeeTimes.length);
  console.log('Operating hours:', { open, close, openAmPm, closeAmPm });
  console.log('Selected date:', selectedDate.toDateString());
  console.log('Current weekday:', currentWeekDay);

  // Filter logic
  const filteredTeeTimes = (startTime && endTime) ? mockTeeTimes.filter(tee => {
    // Safety check for undefined times
    if (!tee.time || !startTime || !endTime) return false;
    
    // Hide tee times with no available slots
    if (tee.remainingSlots === 0) return false;
    
    // Time filter - convert times to comparable format
    const teeTime24h = to24Hour(tee.time);
    const startTime24h = to24Hour(startTime);
    const endTime24h = to24Hour(endTime);
    
    // Check if tee time is within the selected time range and operating hours
    if (teeTime24h < startTime24h || teeTime24h > endTime24h) return false;
    if (teeTime24h < open || teeTime24h > close) return false;
    
    // Golfers filter
    if (selectedGolfers !== 'any' && tee.remainingSlots < selectedGolfers) return false;
    // Holes filter
    if (selectedHoles !== 'any' && tee.holes !== selectedHoles) return false;
    // Transportation filter
    if (transportation !== 'any' && tee.transportation !== transportation) return false;
    // Rate type filter
    if (rateType !== 'any' && tee.rateType !== rateType) return false;
    // Price filter
    if (tee.price < price[0] || tee.price > price[1]) return false;
    return true;
  }) : [];

  console.log('Filtered tee times:', filteredTeeTimes.length);
  console.log('Start/End times:', { startTime, endTime });

  // Generate a full month calendar grid for the current month
  const year = selectedDate.getFullYear();
  const month = selectedDate.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const startDay = firstDay.getDay();
  const daysInMonth = lastDay.getDate();
  const calendarDays = [];
  for (let i = 0; i < startDay; i++) calendarDays.push(null);
  for (let d = 1; d <= daysInMonth; d++) calendarDays.push(new Date(year, month, d));
  while (calendarDays.length % 7 !== 0) calendarDays.push(null);

  // Handler for Book Now button
  const handleBookNow = (tee: any) => {
    setSelectedTeeTime(tee);
    // If only 1 slot, preselect 1 golfer, else prompt
    if (tee.remainingSlots === 1) {
      setSelectedBookingGolfers(1);
    } else {
      setSelectedBookingGolfers(null);
    }
    setBookingModalOpen(true);
  };

  const handleCloseBookingModal = () => {
    setBookingModalOpen(false);
    setSelectedTeeTime(null);
    setSelectedBookingGolfers(null);
  };

  // Add to cart handler
  const handleAddToCart = () => {
    if (selectedTeeTime && selectedBookingGolfers) {
      setCart([
        ...cart,
        {
          ...selectedTeeTime,
          golfers: selectedBookingGolfers,
          date: selectedDate.toISOString().split('T')[0],
        },
      ]);
      setBookingModalOpen(false);
      setSelectedTeeTime(null);
      setSelectedBookingGolfers(null);
      setCartModalOpen(true);
    }
  };

  return (
    <Box display="flex" minHeight="100vh" bgcolor="#F8FAFC">
      {/* Sidebar */}
      <Box width={320} p={3} bgcolor="#fff" borderRight="1px solid #e0e0e0">
        {/* Full Month Calendar */}
        <Typography variant="h6" gutterBottom>
          <CalendarMonthIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          {months[month]} {year}
        </Typography>
        <Box mb={2}>
          <Box display="grid" gridTemplateColumns="repeat(7, 1fr)" mb={1}>
            {["S","M","T","W","T","F","S"].map(d => (
              <Typography key={d} align="center" fontWeight={600} fontSize={14}>{d}</Typography>
            ))}
          </Box>
          <Box display="grid" gridTemplateColumns="repeat(7, 1fr)" gap={0.5}>
            {calendarDays.map((date, idx) => (
              <Button
                key={idx}
                variant={date && date.getDate() === selectedDate.getDate() ? 'contained' : 'text'}
                size="small"
                sx={{ minWidth: 0, height: 32, p: 0, fontWeight: 500 }}
                onClick={() => date && setSelectedDate(date)}
                disabled={!date}
              >
                {date ? date.getDate() : ''}
              </Button>
            ))}
          </Box>
        </Box>
        <Divider sx={{ my: 2 }} />
        {/* Start/End Time Filters */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Start Time</InputLabel>
              <Select
                value={startTime}
                label="Start Time"
                onChange={e => setStartTime(e.target.value)}
              >
                {operatingTimeOptions.map(opt => (
                  <MenuItem key={opt} value={opt}>{opt}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth size="small">
              <InputLabel>End Time</InputLabel>
              <Select
                value={endTime}
                label="End Time"
                onChange={e => setEndTime(e.target.value)}
              >
                {operatingTimeOptions.map(opt => (
                  <MenuItem key={opt} value={opt}>{opt}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
        {/* Golfers Filter */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <PeopleIcon fontSize="small" />
              <Typography fontWeight={500}>Available Slots</Typography>
            </Box>
            <Button size="small" onClick={() => setSelectedGolfers('any')}>Reset</Button>
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Show times with at least this many slots available
          </Typography>
          <ToggleButtonGroup
            value={selectedGolfers}
            exclusive
            onChange={(_, v) => setSelectedGolfers(v || 'any')}
            sx={{ mt: 1 }}
          >
            <ToggleButton value="any">Any</ToggleButton>
            <ToggleButton value={1}>1</ToggleButton>
            <ToggleButton value={2}>2</ToggleButton>
            <ToggleButton value={3}>3</ToggleButton>
            <ToggleButton value={4}>4</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        {/* Holes Filter */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <FlagIcon fontSize="small" />
              <Typography fontWeight={500}>Holes</Typography>
            </Box>
            <Button size="small" onClick={() => setSelectedHoles('any')}>Reset</Button>
          </Box>
          <ToggleButtonGroup
            value={selectedHoles}
            exclusive
            onChange={(_, v) => setSelectedHoles(v || 'any')}
            sx={{ mt: 1 }}
          >
            <ToggleButton value="any">Any</ToggleButton>
            <ToggleButton value={9}>9</ToggleButton>
            <ToggleButton value={18}>18</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        {/* Transportation Filter */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <DirectionsCarIcon fontSize="small" />
              <Typography fontWeight={500}>Transportation</Typography>
            </Box>
            <Button size="small" onClick={() => setTransportation('any')}>Reset</Button>
          </Box>
          <ToggleButtonGroup
            value={transportation}
            exclusive
            onChange={(_, v) => setTransportation(v || 'any')}
            sx={{ mt: 1 }}
          >
            <ToggleButton value="cart"><DirectionsCarIcon /></ToggleButton>
            <ToggleButton value="walk"><DirectionsWalkIcon /></ToggleButton>
          </ToggleButtonGroup>
        </Box>
        {/* Rate Type Filter */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <FilterListIcon fontSize="small" />
              <Typography fontWeight={500}>Rate Type</Typography>
            </Box>
            <Button size="small" onClick={() => setRateType('any')}>Reset</Button>
          </Box>
          <ToggleButtonGroup
            value={rateType}
            exclusive
            onChange={(_, v) => setRateType(v || 'any')}
            sx={{ mt: 1 }}
          >
            <ToggleButton value="any">Any</ToggleButton>
            <ToggleButton value="standard">Standard</ToggleButton>
            <ToggleButton value="discount">Discount</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        <Divider sx={{ my: 2 }} />
        {/* Price Filter Placeholder */}
        <Typography variant="subtitle2">Price</Typography>
        <Box display="flex" alignItems="center" gap={1}>
          <TextField label="$" size="small" value={price[0]} style={{ width: 60 }} />
          <Typography variant="body2">-</Typography>
          <TextField label="$" size="small" value={price[1]} style={{ width: 60 }} />
        </Box>
        <Button variant="outlined" fullWidth sx={{ mt: 2 }} onClick={resetAllFilters}>
          Reset All Filters
        </Button>
        <Divider sx={{ my: 2 }} />
        {/* Price Filter */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography fontWeight={500}>Price Range</Typography>
            <Button size="small" onClick={() => setPrice([0, 300])}>Reset</Button>
          </Box>
          <Box display="flex" alignItems="center" gap={1} sx={{ mt: 1 }}>
            <TextField 
              label="$" 
              size="small" 
              value={price[0]} 
              onChange={(e) => setPrice([parseInt(e.target.value) || 0, price[1]])}
              style={{ width: 60 }} 
            />
            <Typography variant="body2">-</Typography>
            <TextField 
              label="$" 
              size="small" 
              value={price[1]} 
              onChange={(e) => setPrice([price[0], parseInt(e.target.value) || 300])}
              style={{ width: 60 }} 
            />
          </Box>
        </Box>
      </Box>
      {/* Main Content */}
      <Box flex={1} p={3}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6">Showing Tee Times for: {selectedDate.toLocaleDateString()}</Typography>
          <TextField placeholder="Enter Promo" size="small" sx={{ mr: 1 }} />
          <Button variant="contained">Apply</Button>
        </Box>
        <Grid container spacing={2}>
          {filteredTeeTimes.map((tee, idx) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={tee.time}>
              <Card sx={{ p: 2, mb: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Typography variant="h6">{tee.time}</Typography>
                <Typography variant="body2" color="text.secondary">Ridgeview Ranch Golf Club</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Available: {tee.remainingSlots} slots | Holes: {tee.holes} | {tee.transportation === 'cart' ? 'Cart' : 'Walk'} | {tee.rateType.charAt(0).toUpperCase() + tee.rateType.slice(1)}
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mb: 1 }}>
                  ${tee.price}
                </Typography>
                <Button variant="outlined" sx={{ mt: 1, width: '100%' }}>Choose Rate</Button>
                <Button variant="contained" sx={{ mt: 1, width: '100%' }} onClick={() => handleBookNow(tee)}>
                  Book Now ({tee.remainingSlots} slots)
                </Button>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
      {/* Booking Modal */}
      <Dialog open={bookingModalOpen} onClose={handleCloseBookingModal} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {selectedTeeTime && (
            <>
              Ridgeview Ranch Golf Club at {selectedTeeTime.time}, {selectedDate.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric' })}
              <IconButton onClick={handleCloseBookingModal} size="small"><CloseIcon /></IconButton>
            </>
          )}
        </DialogTitle>
        <DialogContent sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
          {/* Left: Rate Card */}
          <Box flex={1} minWidth={260}>
            <Typography variant="subtitle1" fontWeight={600} mb={1}>Select Rate</Typography>
            <Card sx={{ p: 2, mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box>
                <Typography variant="h6">${selectedTeeTime?.price}</Typography>
                <Typography variant="body2">{selectedTeeTime?.holes} Holes</Typography>
                <Box display="flex" alignItems="center" gap={1} mt={1}>
                  <PeopleIcon fontSize="small" />
                  <Typography variant="body2">1 - {selectedTeeTime?.remainingSlots}</Typography>
                  <FlagIcon fontSize="small" sx={{ ml: 1 }} />
                  <Typography variant="body2">{selectedTeeTime?.holes}</Typography>
                  <DirectionsCarIcon fontSize="small" sx={{ ml: 1 }} />
                  <Typography variant="body2">{selectedTeeTime?.transportation === 'cart' ? 'Cart Included' : 'Walk'}</Typography>
                </Box>
              </Box>
              <Box ml={2}>
                <Button variant="contained" size="small" disabled>
                  SELECTED RATE
                </Button>
              </Box>
            </Card>
            <Typography variant="subtitle1" fontWeight={600} mb={1}>Tee time Description</Typography>
            <Typography variant="body2" color="text.secondary">
              Discounted Tee times are non-refundable and non-cancelable. Not showing for the tee time will result in your credit card being charged the greens fee for each golfer in your party. We ask that our customers adhere to our dress code: No denim allowed Collared shirt required Soft spikes only
              <br /><br />
              Ridgeview Ranch Golf Club has a 24-hour tee time cancellation policy. If you do not cancel 24 hours ...
            </Typography>
            <Button variant="text" sx={{ mt: 1 }}>Show more</Button>
          </Box>
          {/* Right: Golfer Selection & Details */}
          <Box flex={1} minWidth={260}>
            <Typography variant="subtitle1" fontWeight={600} mb={1}>Select Number of Golfers</Typography>
            <Box display="flex" gap={1} mb={2}>
              {[...Array(selectedTeeTime?.remainingSlots || 1)].map((_, idx) => (
                <Button
                  key={idx+1}
                  variant={selectedBookingGolfers === idx+1 ? 'contained' : 'outlined'}
                  onClick={() => setSelectedBookingGolfers(idx+1)}
                >
                  {idx+1}
                </Button>
              ))}
            </Box>
            <Typography variant="subtitle2" fontWeight={600}>Tee Time Details</Typography>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
              <FlagIcon fontSize="small" />
              <Typography variant="body2">{selectedTeeTime?.holes} holes</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
              <DirectionsCarIcon fontSize="small" />
              <Typography variant="body2">{selectedTeeTime?.transportation === 'cart' ? 'Cart Included' : 'Walk'}</Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 3, width: '100%' }}
              disabled={!selectedBookingGolfers}
              onClick={handleAddToCart}
            >
              ADD TO CART
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseBookingModal}>CLOSE</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeeSheetPage; 