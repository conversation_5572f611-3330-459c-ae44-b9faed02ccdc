import React, { useState } from 'react';
import { Box, styled } from '@mui/material';
import CalendarHeader from '../components/Calendar/CalendarHeader';
import CalendarNavigation from '../components/Calendar/CalendarNavigation';
import WeeklyCalendarView from '../components/Calendar/WeeklyCalendarView';
import MonthlyCalendarView from '../components/Calendar/MonthlyCalendarView';
import { TeeTimeDailyList } from '../components/TeeTimesList/TeeTimeDailyList/TeeTimeDailyList';
import EmbeddedTeeTimeBooking from '../components/Calendar/EmbeddedTeeTimeBooking';
import { CalendarPageProps, CalendarViewMode } from './types';

const PageContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: '#F8FAFC',
  minHeight: '100vh'
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFFFFF',
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(3),
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)'
}));

const CalendarPage: React.FC<CalendarPageProps> = ({ courseId }) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date('2025-03-15'));
  const [viewMode, setViewMode] = useState<CalendarViewMode | 'embedded'>('daily');

  return (
    <PageContainer>
      <CalendarHeader onBlankTabClick={() => setViewMode('embedded')} />
      <ContentContainer>
        <CalendarNavigation 
          selectedDate={selectedDate}
          viewMode={viewMode as CalendarViewMode}
          onViewModeChange={setViewMode}
        />
        {viewMode === 'daily' ? (
          <TeeTimeDailyList 
            courseId={courseId}
            date={selectedDate}
          />
        ) : viewMode === 'weekly' ? (
          <WeeklyCalendarView 
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
        ) : viewMode === 'monthly' ? (
          <MonthlyCalendarView 
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
        ) : viewMode === 'embedded' ? (
          <EmbeddedTeeTimeBooking />
        ) : null}
      </ContentContainer>
    </PageContainer>
  );
};

export default CalendarPage; 