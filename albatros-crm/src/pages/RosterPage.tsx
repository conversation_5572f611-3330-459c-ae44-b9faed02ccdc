import React from 'react';
import { Box, Typography } from '@mui/material';
import { Roster } from '../components/Roster/Roster';
import { RosterPageProps } from './types';

/**
 * RosterPage component - now uses the fully functional XANO-integrated Roster component
 */
const RosterPage: React.FC<RosterPageProps> = ({ courseId }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Roster
      </Typography>
      <Roster />
    </Box>
  );
};

export default RosterPage;
