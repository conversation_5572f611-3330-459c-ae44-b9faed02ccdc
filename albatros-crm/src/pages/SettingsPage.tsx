import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Container,
  styled
} from '@mui/material';
import { TabValue, SettingsPageProps } from './types';
import { ProfileTab } from '../components/Settings/ProfileTab';
import { UsersPermissionsTab } from '../components/Settings/UsersPermissionsTab';
import { WaitlistTab } from '../components/Settings/WaitlistTab';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3),
  '& .MuiTab-root': {
    textTransform: 'none',
    minWidth: 120,
    padding: theme.spacing(2),
    color: theme.palette.text.secondary,
    fontSize: '1rem',
    fontWeight: 500,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const SettingsPage: React.FC<SettingsPageProps> = ({ courseId }) => {
  const [currentTab, setCurrentTab] = useState<TabValue>('profile');

  const handleTabChange = (_event: React.SyntheticEvent, newValue: TabValue) => {
    setCurrentTab(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Paper sx={{ p: 3 }}>
          {/* Top Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <StyledTabs 
              value={currentTab} 
              onChange={handleTabChange}
            >
              <Tab label="Profile" value="profile" />
              <Tab label="Users & Permissions" value="users" />
              <Tab label="Waitlist" value="waitlist" />
              <Tab label="Integrations" value="integrations" />
              <Tab label="Plan / Billing" value="billing" />
              <Tab label="Audit" value="audit" />
              <Tab label="BLANK" value="blank" />
            </StyledTabs>
          </Box>

          {/* Tab Content */}
          {currentTab === 'profile' && <ProfileTab courseId={courseId} />}
          {currentTab === 'users' && <UsersPermissionsTab courseId={courseId} />}
          {currentTab === 'waitlist' && <WaitlistTab />}
          {currentTab === 'integrations' && <Typography>Integrations (Coming Soon)</Typography>}
          {currentTab === 'billing' && <Typography>Plan / Billing (Coming Soon)</Typography>}
          {currentTab === 'audit' && <Typography>Audit (Coming Soon)</Typography>}
          {currentTab === 'blank' && <Typography>Blank (Coming Soon)</Typography>}
        </Paper>
      </Box>
    </Container>
  );
};

export default SettingsPage; 