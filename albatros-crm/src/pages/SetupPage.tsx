import React, { useState } from 'react';
import { Box, Typography, Paper, Container, styled } from '@mui/material';
import { TimeIntervalSelector } from '../components/Setup/TimeIntervalSelector';
import { MaxGolfersSelector } from '../components/Setup/MaxGolfersSelector';
import { CourseClosures } from '../components/Setup/CourseClosures';
import { TeeTimeCosts } from '../components/Setup/TeeTimeCosts';
import { ConditionalRules } from '../components/Setup/ConditionalRules';
import { 
  WeekDay, 
  OperatingHoursData, 
  TeeTimeCostsData, 
  DayOfWeek, 
  TimeSlot, 
  ConditionalRule,
  TimeField
} from '../components/Setup/types';
import CalendarHeader from '../components/Calendar/CalendarHeader';
import {
  Timer as TimerIcon,
  Group as GroupIcon,
  Rule as RuleIcon,
  EventBusy as EventBusyIcon,
  AccessTime as AccessTimeIcon,
  Gavel as GavelIcon
} from '@mui/icons-material';
import { OperatingHours } from '../components/Setup/OperatingHours';
import { CourseRules } from '../components/Setup/CourseRules';

const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.text.primary,
  fontWeight: 600,
  fontSize: '1.25rem',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  '& svg': {
    fontSize: '1.5rem',
    color: theme.palette.primary.main
  }
}));

const SetupPage: React.FC = () => {

  // Time Interval state
  const [timeInterval, setTimeInterval] = React.useState('15');
  const [customInterval, setCustomInterval] = React.useState('');

  // Max Golfers state
  const [maxGolfers, setMaxGolfers] = React.useState<number>(4);

  // Tee Time Costs state with realistic pricing
  const [teeTimeCosts, setTeeTimeCosts] = React.useState<TeeTimeCostsData>({
    // Weekday rates (Mon-Thu): Lower rates to attract players
    Monday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
    Tuesday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
    Wednesday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
    Thursday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
    // Friday: Transitional rates (weekend rates)
    Friday: { morning: 65.00, afternoon: 75.00, evening: 45.00 },
    // Weekend rates (Sat-Sun): Premium pricing for peak days
    Saturday: { morning: 75.00, afternoon: 85.00, evening: 55.00 },
    Sunday: { morning: 75.00, afternoon: 85.00, evening: 55.00 }
  });

  // Initialize operating hours with all days
  const [operatingHours, setOperatingHours] = useState<OperatingHoursData>({
    [WeekDay.Monday]: { open: '06:00', close: '18:00' },
    [WeekDay.Tuesday]: { open: '06:00', close: '18:00' },
    [WeekDay.Wednesday]: { open: '06:00', close: '18:00' },
    [WeekDay.Thursday]: { open: '06:00', close: '18:00' },
    [WeekDay.Friday]: { open: '06:00', close: '18:00' },
    [WeekDay.Saturday]: { open: '06:00', close: '18:00' },
    [WeekDay.Sunday]: { open: '06:00', close: '18:00' },
    [WeekDay.EveryDay]: { open: '06:00', close: '18:00' }
  });

  // Course Closures state with default items
  const [closures, setClosures] = React.useState<Array<{ id: number; date: string; reason: string }>>([
    { id: 1, date: '2024-12-25', reason: 'Christmas Day' },
    { id: 2, date: '2024-12-26', reason: 'Boxing Day' },
    { id: 3, date: '2025-01-01', reason: 'New Year\'s Day' },
    { id: 4, date: '2025-01-20', reason: 'Martin Luther King Jr. Day' }
  ]);
  const [nextClosureId, setNextClosureId] = React.useState(5);

  // Course Rules state with default items
  const [rules, setRules] = React.useState<Array<{ id: number; title: string; description: string }>>([
    { 
      id: 1, 
      title: 'Dress Code', 
      description: 'Proper golf attire required at all times. This includes collared shirts, appropriate length shorts or pants, and golf shoes. No denim, t-shirts, or tank tops allowed.' 
    },
    { 
      id: 2, 
      title: 'Group Size', 
      description: 'Maximum of 4 players per group. Single players may be paired with other groups to maintain pace of play.' 
    },
    { 
      id: 3, 
      title: 'Cart Usage', 
      description: 'Golf carts must stay on cart paths at all times. Carts are not allowed in areas marked as "No Cart Zones".' 
    },
    { 
      id: 4, 
      title: 'Course Maintenance', 
      description: 'Players are required to repair all divots and ball marks. Please use the provided sand/seed mixture for divots and repair ball marks with a ball mark repair tool.' 
    },
    { 
      id: 5, 
      title: 'Food & Beverage', 
      description: 'No outside food or beverages allowed on the course. Food and beverages must be purchased from the clubhouse or on-course facilities.' 
    }
  ]);
  const [nextRuleId, setNextRuleId] = React.useState(6);

  // Conditional Rules state
  const [conditionalRules, setConditionalRules] = React.useState<ConditionalRule[]>([
    {
      id: 1,
      title: 'Water Delay - Tuesday',
      condition: {
        type: 'dayOfWeek',
        value: 'Tuesday'
      },
      effect: {
        type: 'delayOpening',
        value: '09:00'
      },
      timeline: {
        start: new Date().toISOString().split('T')[0],
        end: '2025-09-30'
      }
    },
    {
      id: 2,
      title: 'Time Intervals - Every Day',
      condition: {
        type: 'dayOfWeek',
        value: 'Every Day'
      },
      effect: {
        type: 'addTimeInterval',
        value: '3'
      },
      timeline: {
        start: new Date().toISOString().split('T')[0],
        end: '2025-09-30'
      }
    }
  ]);

  const handleAddClosure = (closure: { date: string; reason: string }) => {
    setClosures(prev => [...prev, { ...closure, id: nextClosureId }]);
    setNextClosureId(prev => prev + 1);
  };

  const handleDeleteClosure = (id: number) => {
    setClosures(prev => prev.filter(closure => closure.id !== id));
  };

  const handleAddRule = (rule: { title: string; description: string }) => {
    setRules(prev => [...prev, { ...rule, id: nextRuleId }]);
    setNextRuleId(prev => prev + 1);
  };

  const handleDeleteRule = (id: number) => {
    setRules(prev => prev.filter(rule => rule.id !== id));
  };

  const handleTimeChange = (day: WeekDay, field: TimeField, value: string) => {
    setOperatingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }));
  };

  const handleCostChange = (
    day: DayOfWeek,
    timeOfDay: TimeSlot,
    value: number
  ) => {
    setTeeTimeCosts((prev: TeeTimeCostsData) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [timeOfDay]: value
      }
    }));
  };

  const handleAddConditionalRule = () => {
    const newRule: ConditionalRule = {
      id: nextRuleId,
      title: 'New Rule',
      condition: {
        type: 'dayOfWeek',
        value: ''
      },
      effect: {
        type: 'delayOpening',
        value: ''
      },
      timeline: {
        start: new Date().toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      }
    };
    setConditionalRules(prev => [...prev, newRule]);
    setNextRuleId(prev => prev + 1);
  };

  const handleUpdateConditionalRule = (updatedRule: ConditionalRule) => {
    setConditionalRules(prev =>
      prev.map(rule => (rule.id === updatedRule.id ? updatedRule : rule))
    );
  };

  const handleDeleteConditionalRule = (id: number) => {
    setConditionalRules(prev => prev.filter(rule => rule.id !== id));
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <CalendarHeader />
        <Paper sx={{ p: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Course Setup
          </Typography>

          <Box sx={{ mb: 4 }}>
            <SectionTitle>
              <TimerIcon />
              Time Interval Settings
            </SectionTitle>
            <TimeIntervalSelector
              value={timeInterval}
              onChange={setTimeInterval}
              customValue={customInterval}
              onCustomChange={setCustomInterval}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <SectionTitle>
              <GroupIcon />
              Maximum Golfers
            </SectionTitle>
            <MaxGolfersSelector
              value={maxGolfers}
              onChange={setMaxGolfers}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <TeeTimeCosts
              costs={teeTimeCosts}
              onCostChange={handleCostChange}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <SectionTitle>
              <AccessTimeIcon />
              Operating Hours
            </SectionTitle>
            <OperatingHours
              operatingHours={operatingHours}
              onTimeChange={handleTimeChange}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <SectionTitle>
              <EventBusyIcon />
              Course Closures
            </SectionTitle>
            <CourseClosures
              closures={closures}
              onAddClosure={handleAddClosure}
              onDeleteClosure={handleDeleteClosure}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <SectionTitle>
              <GavelIcon />
              Course Rules
            </SectionTitle>
            <CourseRules
              rules={rules}
              onAddRule={handleAddRule}
              onDeleteRule={handleDeleteRule}
            />
          </Box>

          <Box>
            <SectionTitle>
              <RuleIcon />
              Conditional Rules
            </SectionTitle>
            <ConditionalRules
              rules={conditionalRules}
              onAddRule={handleAddConditionalRule}
              onUpdateRule={handleUpdateConditionalRule}
              onDeleteRule={handleDeleteConditionalRule}
            />
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SetupPage; 