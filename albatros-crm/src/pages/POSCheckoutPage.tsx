import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Divider,
  IconButton,
  styled,
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Ta<PERSON>,
  Tab
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { useNavigate } from 'react-router-dom';
import { posAPI } from '../services/posAPI';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import ImageIcon from '@mui/icons-material/Image';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Grid from '@mui/material/Grid';
import Autocomplete from '@mui/material/Autocomplete';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Modal from '@mui/material/Modal';
import Link from '@mui/material/Link';
import CheckIcon from '@mui/icons-material/Check';
import { SquarePaymentForm } from '../components/SquarePaymentForm';
import type { SquareConfig } from '../components/SquarePaymentForm';

const PageContainer = styled(Paper)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  boxShadow: theme.shadows[3],
  margin: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  [theme.breakpoints.up('sm')]: {
    margin: theme.spacing(3),
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(4),
  },
  display: 'grid',
  gridTemplateColumns: '1fr 400px',
  gap: theme.spacing(3),
  height: 'calc(100vh - 100px)',
}));

const CartItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(1),
  marginBottom: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

interface POSCheckoutPageProps {
  courseId: string;
}

const TOP_FILTERS = ['Course', 'Equipment', 'Clothing', 'Range'] as const;
type TopFilter = typeof TOP_FILTERS[number];

const EQUIPMENT_FILTERS = ['Balls', 'Tees', 'Drivers', 'Irons', 'Wedges', 'Putters', 'Misc'] as const;
const CLOTHING_FILTERS = ['Tops', 'Bottoms', 'Hats', 'Shoes', 'Gloves', 'Misc'] as const;
type EquipmentFilter = typeof EQUIPMENT_FILTERS[number];
type ClothingFilter = typeof CLOTHING_FILTERS[number];

// Add a type for the product
interface Product {
  id: string;
  name: string;
  price: number;
  type: string;
  subtype?: string;
  imageUrl?: string;
}

const PLACEHOLDER_IMAGE = '/golf-ball-placeholder.jpg'; // Save the attached image as public/golf-ball-placeholder.jpg

// Mock product data with images
const mockProducts = [
  // Course
  { id: 'c1', name: '18 Hole Green Fees', price: 56.00, type: 'Course', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'c2', name: 'Cart Rental Fees', price: 5.00, type: 'Course', imageUrl: PLACEHOLDER_IMAGE },
  // Equipment
  { id: 'e1', name: 'Titleist ProV1 Box', price: 41.00, type: 'Equipment', subtype: 'Balls', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e2', name: 'Bridgestone e6 Box', price: 23.99, type: 'Equipment', subtype: 'Balls', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e3', name: 'Callaway Distance Box', price: 35.00, type: 'Equipment', subtype: 'Balls', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e4', name: 'Senior Discount Tees', price: 6.00, type: 'Equipment', subtype: 'Tees', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e5', name: 'TaylorMade Driver', price: 299.99, type: 'Equipment', subtype: 'Drivers', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e6', name: 'Ping Irons Set', price: 799.99, type: 'Equipment', subtype: 'Irons', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e7', name: 'Cleveland Wedge', price: 119.99, type: 'Equipment', subtype: 'Wedges', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e8', name: 'Odyssey Putter', price: 199.99, type: 'Equipment', subtype: 'Putters', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'e9', name: 'Golf Ball Marker', price: 2.99, type: 'Equipment', subtype: 'Misc', imageUrl: PLACEHOLDER_IMAGE },
  // Clothing
  { id: 'cl1', name: 'Nike Polo Shirt', price: 49.99, type: 'Clothing', subtype: 'Tops', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'cl2', name: 'Adidas Golf Pants', price: 59.99, type: 'Clothing', subtype: 'Bottoms', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'cl3', name: 'Titleist Hat', price: 24.99, type: 'Clothing', subtype: 'Hats', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'cl4', name: 'FootJoy Shoes', price: 129.99, type: 'Clothing', subtype: 'Shoes', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'cl5', name: 'Callaway Glove', price: 19.99, type: 'Clothing', subtype: 'Gloves', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'cl6', name: 'Golf Socks', price: 9.99, type: 'Clothing', subtype: 'Misc', imageUrl: PLACEHOLDER_IMAGE },
  // Range
  { id: 'r1', name: 'Large Bucket of Balls', price: 12.00, type: 'Range', imageUrl: PLACEHOLDER_IMAGE },
  { id: 'r2', name: 'Small Bucket of Balls', price: 7.00, type: 'Range', imageUrl: PLACEHOLDER_IMAGE },
];

// Mock golfer/member data
const mockGolfers = [
  { id: '110032', name: 'Jim Jordan' },
  { id: '110033', name: 'Lucas Kyle' },
  { id: '110034', name: 'Connor Tate' },
  { id: '110035', name: 'Hank Drake' },
  { id: '110036', name: 'Sarah Lee' },
];

// Mock data for transactions
const mockTransactions = [
  { id: '000345', time: '7:25AM', date: '2024-03-24', name: 'Jim Jordan', amount: 76.86, details: 'Details for #000345' },
  { id: '000346', time: '8:18AM', date: '2024-03-24', name: 'Hank Drake', amount: 49.80, details: 'Details for #000346' },
  { id: '000347', time: '9:05AM', date: '2024-03-24', name: 'Sarah Lee', amount: 88.20, details: 'Details for #000347' },
  { id: '000348', time: '9:45AM', date: '2024-03-24', name: 'Lucas Kyle', amount: 33.20, details: 'Details for #000348' },
  { id: '000349', time: '10:10AM', date: '2024-03-24', name: 'Connor Tate', amount: 49.80, details: 'Details for #000349' },
  { id: '000350', time: '10:30AM', date: '2024-03-24', name: 'Jim Jordan', amount: 22.50, details: 'Details for #000350' },
];

const mockPreOrders = [
  { id: 'M0002220', name: 'Lucas Kyle', amount: 33.20, pickedUp: false, details: 'Pre-order details for M0002220' },
  { id: 'M0002320', name: 'Connor Tate', amount: 49.80, pickedUp: false, details: 'Pre-order details for M0002320' },
  { id: 'M0002321', name: 'Sarah Lee', amount: 27.50, pickedUp: false, details: 'Pre-order details for M0002321' },
  { id: 'M0002322', name: 'Jim Jordan', amount: 19.99, pickedUp: false, details: 'Pre-order details for M0002322' },
  { id: 'M0002323', name: 'Hank Drake', amount: 12.00, pickedUp: false, details: 'Pre-order details for M0002323' },
  { id: 'M0002324', name: 'Lucas Kyle', amount: 15.00, pickedUp: false, details: 'Pre-order details for M0002324' },
];

const env = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
const SQUARE_CONFIG = {
  applicationId: process.env.REACT_APP_SQUARE_APP_ID || 'sandbox-sq0idb-xxxx',
  locationId: process.env.REACT_APP_SQUARE_LOCATION_ID || 'LXXXXXXXX',
  environment: env === 'production' ? 'production' : 'sandbox',
} as SquareConfig;

const POSCheckoutPage: React.FC<POSCheckoutPageProps> = ({ courseId }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [cart, setCart] = useState<Array<{ id: string; name: string; price: number; quantity: number; imageUrl?: string }>>([
    { id: '1', name: '18 Hole Green Fees', price: 56.00, quantity: 1 },
    { id: '2', name: 'Cart Rental Fees', price: 5.00, quantity: 1 },
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [topFilter, setTopFilter] = useState<TopFilter>('Equipment');
  const [equipmentFilter, setEquipmentFilter] = useState<EquipmentFilter>('Balls');
  const [clothingFilter, setClothingFilter] = useState<ClothingFilter>('Tops');
  const [selectedGolfer, setSelectedGolfer] = useState<{ id: string; name: string } | null>(mockGolfers[0]);
  const [checkoutStep, setCheckoutStep] = useState<'cart' | 'payment'>('cart');
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);
  const [cashTendered, setCashTendered] = useState<string>('');
  const [showChange, setShowChange] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [paymentReceipt, setPaymentReceipt] = useState<any>(null);

  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.085; // 8.5% tax rate
  const memberDiscount = subtotal * 0.10; // 10% member discount
  const total = subtotal + tax - memberDiscount;
  const changeDue = cashTendered ? (parseFloat(cashTendered) - total) : 0;

  const handleRemoveItem = (id: string) => {
    setCart(cart.filter(item => item.id !== id));
  };

  const handlePayment = async () => {
    try {
      setLoading(true);
      setError(null);

      const transaction = {
        courseId,
        memberId: '110032', // Hardcoded for demo
        items: cart,
        subtotal,
        tax,
        memberDiscount,
        total,
        paymentMethod: 'card', // Default to card payment
      };

      const response = await posAPI.createTransaction(courseId, transaction);

      if (response.success) {
        setSuccess(true);
        // Clear cart after successful payment
        setCart([]);
      } else {
        setError(response.error?.message || 'Failed to process payment');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Filtering logic for mock products
  const filteredProducts = mockProducts.filter(product => {
    if (topFilter === 'Course') return product.type === 'Course';
    if (topFilter === 'Range') return product.type === 'Range';
    if (topFilter === 'Equipment') return product.type === 'Equipment' && product.subtype === equipmentFilter;
    if (topFilter === 'Clothing') return product.type === 'Clothing' && product.subtype === clothingFilter;
    return false;
  });

  // Update cart logic to add/increment items
  const handleAddToCart = (product: Product) => {
    setCart(prevCart => {
      const existing = prevCart.find(item => item.id === product.id);
      if (existing) {
        return prevCart.map(item =>
          item.id === product.id ? { ...item, quantity: item.quantity + 1 } : item
        );
      } else {
        return [...prevCart, { id: product.id, name: product.name, price: product.price, quantity: 1, imageUrl: product.imageUrl }];
      }
    });
  };

  // Helper to extract make (brand) from product name
  const getMake = (name: string) => name.split(' ')[0];

  return (
    <PageContainer>
      <ContentContainer>
        {/* Left side - Product search and list */}
        <Box>
          {/* Top Filters */}
          <Tabs
            value={topFilter}
            onChange={(_, v) => setTopFilter(v)}
            textColor="primary"
            indicatorColor="primary"
            sx={{ minHeight: 0, mb: 1 }}
          >
            {TOP_FILTERS.map(f => (
              <Tab
                key={f}
                label={f}
                value={f}
                sx={{
                  fontWeight: topFilter === f ? 700 : 500,
                  borderBottom: topFilter === f ? `2px solid ${theme.palette.primary.main}` : 'none',
                  color: topFilter === f ? theme.palette.primary.main : theme.palette.text.primary,
                  minWidth: 100,
                  textTransform: 'none',
                  fontSize: '1rem',
                }}
              />
            ))}
          </Tabs>
          {/* Second Line Filters */}
          {(topFilter === 'Equipment' || topFilter === 'Clothing') && (
            <Box sx={{ display: 'flex', gap: 2, mb: 2, pl: 1 }}>
              {(topFilter === 'Equipment' ? EQUIPMENT_FILTERS : CLOTHING_FILTERS).map(f => (
                <Button
                  key={f}
                  onClick={() => topFilter === 'Equipment' ? setEquipmentFilter(f as EquipmentFilter) : setClothingFilter(f as ClothingFilter)}
                  sx={{
                    fontWeight: (topFilter === 'Equipment' ? equipmentFilter : clothingFilter) === f ? 700 : 500,
                    borderBottom: (topFilter === 'Equipment' ? equipmentFilter : clothingFilter) === f ? `2px solid ${theme.palette.primary.main}` : 'none',
                    color: (topFilter === 'Equipment' ? equipmentFilter : clothingFilter) === f ? theme.palette.primary.main : theme.palette.text.primary,
                    borderRadius: 0,
                    minWidth: 0,
                    px: 2,
                    textTransform: 'none',
                    fontSize: '1rem',
                    background: 'none',
                    boxShadow: 'none',
                  }}
                >
                  {f}
                </Button>
              ))}
            </Box>
          )}
          <Typography variant="h5" gutterBottom>
            Products
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Search Product/Member"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ mb: 3 }}
          />
          {/* Product cards grid */}
          <Grid container spacing={2}>
            {filteredProducts.length === 0 ? (
              <Grid item xs={12}>
                <Typography color="text.secondary">No products found.</Typography>
              </Grid>
            ) : (
              filteredProducts.map(product => (
                <Grid item xs={12} sm={6} md={3} key={product.id}>
                  <Card sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2, minHeight: 260, background: theme.palette.grey[100], boxShadow: 0 }}>
                    <CardMedia
                      component="img"
                      image={product.imageUrl || PLACEHOLDER_IMAGE}
                      alt={product.name}
                      sx={{ width: 80, height: 80, objectFit: 'contain', mb: 1, mt: 1, borderRadius: 1, bgcolor: theme.palette.grey[200] }}
                    />
                    <CardContent sx={{ flex: 1, p: 0, textAlign: 'center', width: '100%' }}>
                      <Typography variant="subtitle1" fontWeight={700} sx={{ mb: 0.5 }}>
                        {getMake(product.name)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                        {product.subtype || product.type}
                      </Typography>
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        ${product.price.toFixed(2)}
                      </Typography>
                    </CardContent>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      startIcon={<AddShoppingCartIcon />}
                      onClick={() => handleAddToCart(product)}
                      sx={{ mt: 'auto', width: '90%' }}
                    >
                      Add to Cart
                    </Button>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>

        {/* Right side - Cart */}
        <Box sx={{ 
          backgroundColor: theme.palette.grey[100], 
          borderRadius: 2,
          padding: 2,
          display: 'flex',
          flexDirection: 'column'
        }}>
          <Autocomplete
            options={mockGolfers}
            getOptionLabel={option => `${option.name} (${option.id})`}
            value={selectedGolfer}
            onChange={(_, value) => setSelectedGolfer(value)}
            renderInput={(params) => (
              <TextField {...params} label="Search Golfer/Member" variant="outlined" size="small" sx={{ mb: 1 }} />
            )}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            sx={{ mb: 1 }}
          />
          {selectedGolfer && (
            <>
              <Typography variant="h6" gutterBottom>
                Golfer: {selectedGolfer.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Member ID: {selectedGolfer.id}
              </Typography>
            </>
          )}
          {checkoutStep === 'cart' ? (
            <>
              <Box sx={{ flex: 1, mt: 2 }}>
                {cart.map((item) => (
                  <CartItem key={item.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: 36, height: 36, mr: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: theme.palette.grey[200], borderRadius: 1, overflow: 'hidden' }}>
                        {item.imageUrl ? (
                          <img src={item.imageUrl} alt={item.name} style={{ width: 28, height: 28, objectFit: 'cover' }} />
                        ) : (
                          <ImageIcon color="disabled" fontSize="small" />
                        )}
                      </Box>
                      <Box>
                        <Typography variant="body1">{item.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          ${item.price.toFixed(2)} x {item.quantity}
                        </Typography>
                      </Box>
                    </Box>
                    <IconButton 
                      size="small" 
                      onClick={() => handleRemoveItem(item.id)}
                      sx={{ color: theme.palette.error.main }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CartItem>
                ))}
              </Box>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Subtotal:</Typography>
                  <Typography>${subtotal.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Taxes:</Typography>
                  <Typography>${tax.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Member Discount:</Typography>
                  <Typography>-${memberDiscount.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6">${total.toFixed(2)}</Typography>
                </Box>
              </Box>
              <Button 
                variant="contained" 
                color="primary" 
                fullWidth 
                size="large"
                onClick={() => setCheckoutStep('payment')}
                disabled={loading || cart.length === 0}
                sx={{ 
                  height: 56,
                  borderRadius: 2,
                  fontSize: '1.1rem'
                }}
              >
                PAY ({cart.length} Items)
              </Button>
            </>
          ) : (
            // Payment method selection and flows
            <>
              <Box sx={{ flex: 1, mt: 2 }}>
                {cart.map((item) => (
                  <CartItem key={item.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: 36, height: 36, mr: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: theme.palette.grey[200], borderRadius: 1, overflow: 'hidden' }}>
                        {item.imageUrl ? (
                          <img src={item.imageUrl} alt={item.name} style={{ width: 28, height: 28, objectFit: 'cover' }} />
                        ) : (
                          <ImageIcon color="disabled" fontSize="small" />
                        )}
                      </Box>
                      <Box>
                        <Typography variant="body1">{item.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          ${item.price.toFixed(2)} x {item.quantity}
                        </Typography>
                      </Box>
                    </Box>
                  </CartItem>
                ))}
              </Box>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Subtotal:</Typography>
                  <Typography>${subtotal.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Taxes:</Typography>
                  <Typography>${tax.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Member Discount:</Typography>
                  <Typography>-${memberDiscount.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6">${total.toFixed(2)}</Typography>
                </Box>
              </Box>
              {!selectedPayment ? (
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>Select Payment Method</Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button variant="outlined" onClick={() => setSelectedPayment('card')}>Credit/Debit Card</Button>
                    <Button variant="outlined" onClick={() => setSelectedPayment('tap')}>Tap to Pay</Button>
                    <Button variant="outlined" onClick={() => setSelectedPayment('cash')}>Cash</Button>
                    <Button variant="outlined" onClick={() => setSelectedPayment('tab')}>Add to Tab</Button>
                  </Box>
                </Box>
              ) : null}
              {(selectedPayment === 'card' || selectedPayment === 'tap') && !paymentComplete && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>{selectedPayment === 'card' ? 'Insert Card' : 'Tap to Pay'}</Typography>
                  <SquarePaymentForm
                    amount={total}
                    config={SQUARE_CONFIG}
                    onPaymentSuccess={(result) => {
                      setPaymentComplete(true);
                      setPaymentReceipt(result);
                      setSuccess(true);
                      setCart([]);
                    }}
                    onPaymentError={(err) => setError(err)}
                  />
                  <Button variant="contained" sx={{ mt: 3 }} onClick={() => { setCheckoutStep('cart'); setSelectedPayment(null); setPaymentComplete(false); setPaymentReceipt(null); }}>
                    Back
                  </Button>
                </Box>
              )}
              {selectedPayment === 'cash' && !paymentComplete && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>Cash Payment</Typography>
                  <TextField
                    label="Amount Tendered"
                    type="number"
                    value={cashTendered}
                    onChange={e => setCashTendered(e.target.value)}
                    sx={{ mb: 2 }}
                    fullWidth
                  />
                  {cashTendered && (
                    <Typography sx={{ mb: 2 }}>
                      Change Due: <b>${changeDue >= 0 ? changeDue.toFixed(2) : '0.00'}</b>
                    </Typography>
                  )}
                  <Button variant="contained" color="secondary" sx={{ mb: 2 }} onClick={() => setShowChange(true)}>
                    Open Cash Drawer
                  </Button>
                  <Button variant="contained" sx={{ mr: 2 }} onClick={() => { setCheckoutStep('cart'); setSelectedPayment(null); setCashTendered(''); setShowChange(false); }}>Back</Button>
                </Box>
              )}
              {selectedPayment === 'tab' && !paymentComplete && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>Add to Member Tab</Typography>
                  <TextField
                    label="Member Name or ID"
                    value={selectedGolfer ? `${selectedGolfer.name} (${selectedGolfer.id})` : ''}
                    fullWidth
                    disabled
                    sx={{ mb: 2 }}
                  />
                  <Button variant="contained" color="primary" sx={{ mb: 2 }}>
                    Add to Tab
                  </Button>
                  <Button variant="contained" sx={{ mr: 2 }} onClick={() => { setCheckoutStep('cart'); setSelectedPayment(null); }}>Back</Button>
                </Box>
              )}
              {paymentComplete && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>Payment Complete!</Typography>
                  {paymentReceipt?.receiptUrl ? (
                    <Button href={paymentReceipt.receiptUrl} target="_blank" rel="noopener" variant="outlined" sx={{ mb: 2 }}>
                      View Receipt
                    </Button>
                  ) : null}
                  <Button variant="contained" onClick={() => { setCheckoutStep('cart'); setSelectedPayment(null); setPaymentComplete(false); setPaymentReceipt(null); }}>
                    New Transaction
                  </Button>
                </Box>
              )}
            </>
          )}
        </Box>
      </ContentContainer>

      <Snackbar 
        open={success} 
        autoHideDuration={6000} 
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert severity="success" onClose={() => setSuccess(false)}>
          Payment processed successfully!
        </Alert>
      </Snackbar>

      <Snackbar 
        open={!!error} 
        autoHideDuration={6000} 
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      </Snackbar>

      <Box sx={{ display: 'flex', gap: 2, mt: 3, mb: 2 }}>
        <RecentTransactions />
        <PreOrderedConfirmation />
      </Box>
    </PageContainer>
  );
};

// Recent Transactions Component
const RecentTransactions: React.FC = () => {
  const [visibleCount, setVisibleCount] = useState(5);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalDetails, setModalDetails] = useState<string | null>(null);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 10 && visibleCount < mockTransactions.length) {
      setVisibleCount(c => Math.min(c + 3, mockTransactions.length));
    }
  };

  return (
    <Box sx={{ flex: 1, minWidth: 320, bgcolor: '#fafbfc', borderRadius: 2, p: 3, mr: 3, maxHeight: 340, display: 'flex', flexDirection: 'column' }}>
      <Typography variant="subtitle1" color="#4ec3b3" fontWeight={600} sx={{ mb: 2, fontSize: 18 }}>
        Recent Transactions
      </Typography>
      <List component="div" sx={{ overflowY: 'auto', maxHeight: 250 }} onScroll={handleScroll}>
        {mockTransactions.slice(0, visibleCount).map(tx => (
          <ListItem key={tx.id} sx={{ py: 1.5, borderBottom: '1px solid #e0e0e0', alignItems: 'center', display: 'flex' }} disableGutters>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography fontSize={15} fontWeight={600}>{tx.time}</Typography>
              <Typography fontSize={14} color="text.secondary">{tx.name}</Typography>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', minWidth: 100 }}>
              <Link component="button" onClick={() => { setModalDetails(tx.details); setModalOpen(true); }} sx={{ color: '#4ec3b3', fontSize: 15, fontWeight: 600, mb: 0.5, textAlign: 'right' }}>#{tx.id}</Link>
              <Typography fontWeight={700} fontSize={17} sx={{ color: 'text.primary', textAlign: 'right' }}>${tx.amount.toFixed(2)}</Typography>
            </Box>
          </ListItem>
        ))}
      </List>
      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', bgcolor: 'background.paper', p: 4, borderRadius: 2, minWidth: 320 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Transaction Details</Typography>
          <Typography>{modalDetails}</Typography>
        </Box>
      </Modal>
    </Box>
  );
};

// Pre-Ordered Confirmation Component
const PreOrderedConfirmation: React.FC = () => {
  const [visibleCount, setVisibleCount] = useState(5);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalDetails, setModalDetails] = useState<string | null>(null);
  const [orders, setOrders] = useState(mockPreOrders);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 10 && visibleCount < orders.length) {
      setVisibleCount(c => Math.min(c + 3, orders.length));
    }
  };

  const handleConfirm = (id: string) => {
    setOrders(prev => prev.map(o => o.id === id ? { ...o, pickedUp: true } : o));
  };

  return (
    <Box sx={{ flex: 1, minWidth: 320, bgcolor: '#fafbfc', borderRadius: 2, p: 3, maxHeight: 340, display: 'flex', flexDirection: 'column' }}>
      <Typography variant="subtitle1" color="#4ec3b3" fontWeight={600} sx={{ mb: 2, fontSize: 18 }}>
        Pre-Ordered Confirmation
      </Typography>
      <List component="div" sx={{ overflowY: 'auto', maxHeight: 250 }} onScroll={handleScroll}>
        {orders.slice(0, visibleCount).map(order => (
          <ListItem key={order.id} sx={{ py: 1.5, borderBottom: '1px solid #e0e0e0', alignItems: 'center', display: 'flex' }} disableGutters>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography fontSize={15} fontWeight={600}>Mobile Order</Typography>
              <Typography fontSize={14} color="text.secondary">{order.name}</Typography>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', minWidth: 100 }}>
              <Link component="button" onClick={() => { setModalDetails(order.details); setModalOpen(true); }} sx={{ color: '#4ec3b3', fontSize: 15, fontWeight: 600, mb: 0.5, textAlign: 'right' }}>#{order.id}</Link>
              <Typography fontWeight={700} fontSize={17} sx={{ color: 'text.primary', textAlign: 'right' }}>${order.amount.toFixed(2)}</Typography>
              <IconButton onClick={() => handleConfirm(order.id)} disabled={order.pickedUp} color={order.pickedUp ? 'success' : 'default'} sx={{ mt: 1 }}>
                <CheckIcon />
              </IconButton>
            </Box>
          </ListItem>
        ))}
      </List>
      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', bgcolor: 'background.paper', p: 4, borderRadius: 2, minWidth: 320 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Pre-Order Details</Typography>
          <Typography>{modalDetails}</Typography>
        </Box>
      </Modal>
    </Box>
  );
};

export default POSCheckoutPage; 