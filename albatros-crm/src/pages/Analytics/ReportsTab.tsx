import React, { useState } from 'react';
import { Box, Typography, Paper, Button, TextField, InputAdornment, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Stack, Dialog, DialogTitle, DialogContent, DialogActions, MenuItem, Select, FormControl, InputLabel, Checkbox, ListItemText } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import EmailIcon from '@mui/icons-material/Email';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

const mockReports = [
  { id: 1, name: 'Daily Sales', createdBy: '<EMAIL>', lastRun: '2024-06-12 09:00', automated: true, columns: ['Date', 'Sales'], dataSource: 'Sales', filters: [], automation: { recipients: ['<EMAIL>'], frequency: 'Daily', time: '09:00' } },
  { id: 2, name: 'Inventory Summary', createdBy: '<EMAIL>', lastRun: '2024-06-11 18:00', automated: false, columns: ['Product', 'Stock'], dataSource: 'Inventory', filters: [], automation: null },
  { id: 3, name: 'Tee Time Utilization', createdBy: '<EMAIL>', lastRun: '2024-06-10 12:00', automated: true, columns: ['Time', 'Utilization'], dataSource: 'Tee Times', filters: [], automation: { recipients: ['<EMAIL>'], frequency: 'Weekly', time: '12:00' } },
  { id: 4, name: 'Monthly Revenue', createdBy: '<EMAIL>', lastRun: '2024-06-01 08:00', automated: false, columns: ['Date', 'Sales'], dataSource: 'Sales', filters: [{ column: 'Date', operator: 'contains', value: '2024-05' }], automation: null },
  { id: 5, name: 'Low Stock Products', createdBy: '<EMAIL>', lastRun: '2024-06-09 15:00', automated: false, columns: ['Product', 'Stock'], dataSource: 'Inventory', filters: [{ column: 'Stock', operator: 'lt', value: '30' }], automation: null },
  { id: 6, name: 'Morning Tee Times', createdBy: '<EMAIL>', lastRun: '2024-06-08 07:00', automated: true, columns: ['Time', 'Golfer'], dataSource: 'Tee Times', filters: [{ column: 'Time', operator: 'lt', value: '10:00' }], automation: { recipients: ['<EMAIL>'], frequency: 'Daily', time: '07:00' } },
];

const mockDataSources = [
  { name: 'Sales', columns: ['Date', 'Sales', 'Product', 'Employee'], data: [
    { Date: '2024-06-10', Sales: 1000, Product: 'Ball', Employee: 'Alice' },
    { Date: '2024-06-11', Sales: 1200, Product: 'Club', Employee: 'Bob' },
    { Date: '2024-06-12', Sales: 900, Product: 'Ball', Employee: 'Carol' },
  ] },
  { name: 'Inventory', columns: ['Product', 'Stock', 'Category', 'Supplier'], data: [
    { Product: 'Ball', Stock: 50, Category: 'Equipment', Supplier: 'Acme' },
    { Product: 'Club', Stock: 20, Category: 'Equipment', Supplier: 'GolfPro' },
    { Product: 'Shirt', Stock: 100, Category: 'Apparel', Supplier: 'WearCo' },
  ] },
  { name: 'Tee Times', columns: ['Time', 'Utilization', 'Golfer', 'Course'], data: [
    { Time: '08:00', Utilization: 80, Golfer: 'James', Course: 'North' },
    { Time: '09:00', Utilization: 60, Golfer: 'Jacob', Course: 'South' },
    { Time: '10:00', Utilization: 90, Golfer: 'Alice', Course: 'North' },
  ] },
];

const frequencyOptions = ['Daily', 'Weekly', 'Monthly'];

const columnTypes: Record<string, 'text' | 'number' | 'date'> = {
  Date: 'date',
  Sales: 'number',
  Product: 'text',
  Employee: 'text',
  Stock: 'number',
  Category: 'text',
  Supplier: 'text',
  Time: 'text',
  Utilization: 'number',
  Golfer: 'text',
  Course: 'text',
};

const filterOperators = [
  { label: 'Equals', value: 'eq', types: ['text', 'number', 'date'] },
  { label: 'Contains', value: 'contains', types: ['text'] },
  { label: 'Greater Than', value: 'gt', types: ['number', 'date'] },
  { label: 'Less Than', value: 'lt', types: ['number', 'date'] },
  { label: 'Between', value: 'between', types: ['number', 'date'] },
];

function applyFilters(data: any[], filters: any[]): any[] {
  return data.filter((row: any) =>
    filters.every((f: any) => {
      const val = row[f.column];
      if (f.operator === 'eq') return String(val) === String(f.value);
      if (f.operator === 'contains') return String(val).toLowerCase().includes(String(f.value).toLowerCase());
      if (f.operator === 'gt') return Number(val) > Number(f.value);
      if (f.operator === 'lt') return Number(val) < Number(f.value);
      if (f.operator === 'between') {
        if (Array.isArray(f.value) && f.value.length === 2) {
          if (columnTypes[f.column] === 'date') {
            return dayjs(val).isAfter(dayjs(f.value[0]).subtract(1, 'day')) && dayjs(val).isBefore(dayjs(f.value[1]).add(1, 'day'));
          }
          return Number(val) >= Number(f.value[0]) && Number(val) <= Number(f.value[1]);
        }
        return true;
      }
      return true;
    })
  );
}

function ReportBuilderModal({ open, onClose, onSave, initialValues }: any) {
  const [name, setName] = useState(initialValues?.name || '');
  const [dataSource, setDataSource] = useState(initialValues?.dataSource || mockDataSources[0].name);
  const [columns, setColumns] = useState(initialValues?.columns || []);
  const [type, setType] = useState(initialValues?.type || 'Table');
  const [filters, setFilters] = useState<any[]>(initialValues?.filters || []);
  // Date range filter state
  const [dateColumn, setDateColumn] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [sortCol, setSortCol] = useState<string | null>(null);
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('asc');

  const availableColumns = mockDataSources.find(ds => ds.name === dataSource)?.columns || [];
  const data = mockDataSources.find(ds => ds.name === dataSource)?.data || [];

  // Find date columns
  const dateColumns = availableColumns.filter(col => columnTypes[col] === 'date');

  // Add date range filter to filters array for preview
  let allFilters = [...filters];
  if (dateColumn && dateRange[0] && dateRange[1]) {
    allFilters = [
      ...allFilters,
      {
        column: dateColumn,
        operator: 'between',
        value: [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')],
      },
    ];
  }
  const filteredData = applyFilters(data, allFilters);

  // Minimal working sort handler
  const handleSort = (col: string) => {
    if (sortCol === col) {
      setSortDir(sortDir === 'asc' ? 'desc' : 'asc');
    } else {
      setSortCol(col);
      setSortDir('asc');
    }
    // Sorting logic can be added here if needed
  };

  // Minimal working CSV export
  const handleExportCSV = () => {
    if (!columns.length || !filteredData.length) return;
    const rows = [columns.join(',')];
    filteredData.forEach((row: any) => {
      rows.push(columns.map((col: string) => JSON.stringify(row[col] ?? '')).join(','));
    });
    const csv = rows.join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${name || 'report'}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleSave = () => {
    onSave({
      ...initialValues,
      name,
      dataSource,
      columns,
      type,
      filters,
      dateColumn,
      dateRange: dateRange[0] && dateRange[1] ? [dateRange[0].toISOString(), dateRange[1].toISOString()] : null,
    });
  };

  // Filter builder handlers
  const handleAddFilter = () => {
    setFilters((f: any[]) => [...f, { column: availableColumns[0], operator: 'eq', value: '' }]);
  };
  const handleFilterChange = (idx: number, key: string, value: any) => {
    setFilters((f: any[]) => f.map((flt: any, i: number) => i === idx ? { ...flt, [key]: value } : flt));
  };
  const handleRemoveFilter = (idx: number) => {
    setFilters((f: any[]) => f.filter((_: any, i: number) => i !== idx));
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>{initialValues ? 'Edit Report' : 'Create Report'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField label="Report Name" value={name} onChange={e => setName(e.target.value)} fullWidth />
            <FormControl fullWidth>
              <InputLabel>Data Source</InputLabel>
              <Select value={dataSource} label="Data Source" onChange={e => { setDataSource(e.target.value); setColumns([]); setFilters([]); setDateColumn(null); setDateRange([null, null]); }}>
                {mockDataSources.map(ds => (
                  <MenuItem key={ds.name} value={ds.name}>{ds.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Columns</InputLabel>
              <Select
                multiple
                value={columns}
                onChange={e => setColumns(e.target.value as string[])}
                renderValue={selected => (selected as string[]).join(', ')}
              >
                {availableColumns.map((col: string) => (
                  <MenuItem key={col} value={col}>
                    <Checkbox checked={columns.indexOf(col) > -1} />
                    <ListItemText primary={col} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Output Type</InputLabel>
              <Select value={type} label="Output Type" onChange={e => setType(e.target.value)}>
                <MenuItem value="Table">Table</MenuItem>
                <MenuItem value="CSV">CSV</MenuItem>
              </Select>
            </FormControl>
            {/* Date Range Filter */}
            {dateColumns.length > 0 && (
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 1 }}>
                <FormControl sx={{ minWidth: 160 }} size="small">
                  <InputLabel>Date Column</InputLabel>
                  <Select value={dateColumn || ''} label="Date Column" onChange={e => setDateColumn(e.target.value)}>
                    {dateColumns.map(col => (
                      <MenuItem key={col} value={col}>{col}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <DatePicker
                  label="Start Date"
                  value={dateRange[0]}
                  onChange={(val: Dayjs | null) => setDateRange([val, dateRange[1]])}
                  slotProps={{ textField: { size: 'small' } }}
                  disabled={!dateColumn}
                />
                <DatePicker
                  label="End Date"
                  value={dateRange[1]}
                  onChange={(val: Dayjs | null) => setDateRange([dateRange[0], val])}
                  slotProps={{ textField: { size: 'small' } }}
                  disabled={!dateColumn}
                />
              </Box>
            )}
            {/* Filter Builder */}
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Filters</Typography>
              {filters.map((flt: any, idx: number) => {
                const colType = columnTypes[flt.column] || 'text';
                const ops = filterOperators.filter(op => op.types.includes(colType));
                return (
                  <Box key={idx} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                    <FormControl sx={{ minWidth: 120 }} size="small">
                      <InputLabel>Column</InputLabel>
                      <Select value={flt.column} label="Column" onChange={e => handleFilterChange(idx, 'column', e.target.value)}>
                        {availableColumns.map((col: string) => (
                          <MenuItem key={col} value={col}>{col}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <FormControl sx={{ minWidth: 120 }} size="small">
                      <InputLabel>Operator</InputLabel>
                      <Select value={flt.operator} label="Operator" onChange={e => handleFilterChange(idx, 'operator', e.target.value)}>
                        {ops.map((op: { label: string; value: string }) => (
                          <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {/* Value input(s) based on operator and type */}
                    {flt.operator === 'between' ? (
                      <>
                        {colType === 'date' ? (
                          <DatePicker
                            label="Start"
                            value={flt.value?.[0] ? dayjs(flt.value[0]) : null}
                            onChange={(val: Dayjs | null) => handleFilterChange(idx, 'value', [val, flt.value?.[1] ? dayjs(flt.value[1]) : null])}
                            slotProps={{ textField: { size: 'small' } }}
                          />
                        ) : (
                          <TextField
                            size="small"
                            label="Min"
                            type={colType === 'number' ? 'number' : 'text'}
                            value={flt.value?.[0] ?? ''}
                            onChange={e => handleFilterChange(idx, 'value', [e.target.value, flt.value?.[1] ?? ''])}
                            sx={{ width: 80 }}
                          />
                        )}
                        {colType === 'date' ? (
                          <DatePicker
                            label="End"
                            value={flt.value?.[1] ? dayjs(flt.value[1]) : null}
                            onChange={(val: Dayjs | null) => handleFilterChange(idx, 'value', [flt.value?.[0] ? dayjs(flt.value[0]) : null, val])}
                            slotProps={{ textField: { size: 'small' } }}
                          />
                        ) : (
                          <TextField
                            size="small"
                            label="Max"
                            type={colType === 'number' ? 'number' : 'text'}
                            value={flt.value?.[1] ?? ''}
                            onChange={e => handleFilterChange(idx, 'value', [flt.value?.[0] ?? '', e.target.value])}
                            sx={{ width: 80 }}
                          />
                        )}
                      </>
                    ) : colType === 'date' ? (
                      <DatePicker
                        label="Value"
                        value={flt.value ? dayjs(flt.value) : null}
                        onChange={(val: Dayjs | null) => handleFilterChange(idx, 'value', val ? val.format('YYYY-MM-DD') : '')}
                        slotProps={{ textField: { size: 'small' } }}
                      />
                    ) : (
                      <TextField
                        size="small"
                        label="Value"
                        type={colType === 'number' ? 'number' : 'text'}
                        value={flt.value}
                        onChange={e => handleFilterChange(idx, 'value', e.target.value)}
                        sx={{ width: 120 }}
                      />
                    )}
                    <Button onClick={() => handleRemoveFilter(idx)} color="error" size="small">Remove</Button>
                  </Box>
                );
              })}
              <Button onClick={handleAddFilter} size="small" variant="outlined">Add Filter</Button>
            </Box>
            {/* Preview Table with Export and Sorting */}
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" sx={{ flex: 1 }}>Preview</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<FileDownloadIcon />}
                  onClick={handleExportCSV}
                  disabled={!columns.length || !filteredData.length}
                >
                  Export CSV
                </Button>
              </Box>
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {columns.map((col: string) => (
                        <TableCell
                          key={col}
                          onClick={() => handleSort(col)}
                          sx={{ cursor: 'pointer', userSelect: 'none' }}
                        >
                          {col}
                          {sortCol === col ? (sortDir === 'asc' ? ' ▲' : ' ▼') : ''}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredData.slice(0, 10).map((row: any, idx: number) => (
                      <TableRow key={idx}>
                        {columns.map((col: string) => (
                          <TableCell key={col}>{row[col]}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                    {filteredData.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={columns.length} align="center">No data</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
}

function AutomationModal({ open, onClose, onSave, initialValues }: any) {
  const [recipients, setRecipients] = useState(initialValues?.recipients?.join(', ') || '');
  const [frequency, setFrequency] = useState(initialValues?.frequency || 'Daily');
  const [time, setTime] = useState(initialValues?.time || '09:00');

  const handleSave = () => {
    onSave({
      recipients: recipients.split(',').map((r: string) => r.trim()).filter(Boolean),
      frequency,
      time,
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>Automate Report</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField label="Recipients (comma separated)" value={recipients} onChange={e => setRecipients(e.target.value)} fullWidth />
          <FormControl fullWidth>
            <InputLabel>Frequency</InputLabel>
            <Select value={frequency} label="Frequency" onChange={e => setFrequency(e.target.value)}>
              {frequencyOptions.map(opt => (
                <MenuItem key={opt} value={opt}>{opt}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField label="Time (HH:MM)" value={time} onChange={e => setTime(e.target.value)} fullWidth />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">Save</Button>
      </DialogActions>
    </Dialog>
  );
}

function ReportPreviewModal({ open, onClose, report }: any) {
  if (!report) return null;
  const dataSource = mockDataSources.find(ds => ds.name === report.dataSource);
  const columns = report.columns || [];
  const filters = report.filters || [];
  const data = dataSource ? applyFilters(dataSource.data, filters) : [];

  // CSV download logic
  const handleExportCSV = () => {
    if (!columns.length) return;
    const rows = [columns.join(',')];
    data.forEach((row: any) => {
      rows.push(columns.map((col: string) => JSON.stringify(row[col] ?? '')).join(','));
    });
    const csv = rows.join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.name || 'report'}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{report.name}</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Data Source: {report.dataSource}</Typography>
          <Typography variant="subtitle2">Columns: {columns.join(', ')}</Typography>
          {filters.length > 0 && (
            <Typography variant="subtitle2">Filters: {filters.map((f: any) => `${f.column} ${f.operator} ${f.value}`).join('; ')}</Typography>
          )}
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle2" sx={{ flex: 1 }}>Preview</Typography>
          <Button variant="outlined" onClick={handleExportCSV} disabled={!columns.length || !data.length}>Download CSV</Button>
        </Box>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                {columns.map((col: string) => (
                  <TableCell key={col}>{col}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {data.slice(0, 20).map((row: any, idx: number) => (
                <TableRow key={idx}>
                  {columns.map((col: string) => (
                    <TableCell key={col}>{row[col]}</TableCell>
                  ))}
                </TableRow>
              ))}
              {data.length === 0 && (
                <TableRow>
                  <TableCell colSpan={columns.length} align="center">No data</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}

const ReportsTab: React.FC = () => {
  const [search, setSearch] = useState('');
  const [reports, setReports] = useState(mockReports);
  const [builderOpen, setBuilderOpen] = useState(false);
  const [automationOpen, setAutomationOpen] = useState(false);
  const [editReport, setEditReport] = useState<any | null>(null);
  const [automationReport, setAutomationReport] = useState<any | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewReport, setPreviewReport] = useState<any | null>(null);

  const filteredReports = reports.filter(r => r.name.toLowerCase().includes(search.toLowerCase()));

  const handleCreate = () => {
    setEditReport(null);
    setBuilderOpen(true);
  };
  const handleEdit = (report: any) => {
    setEditReport(report);
    setBuilderOpen(true);
  };
  const handleSaveReport = (report: any) => {
    if (report.id) {
      setReports(reports => reports.map(r => r.id === report.id ? { ...r, ...report } : r));
    } else {
      setReports(reports => [...reports, { ...report, id: Date.now(), lastRun: '-', automated: false }]);
    }
    setBuilderOpen(false);
  };
  const handleAutomate = (report: any) => {
    setAutomationReport(report);
    setAutomationOpen(true);
  };
  const handleSaveAutomation = (automation: any) => {
    setReports(reports => reports.map(r => r.id === automationReport.id ? { ...r, automated: true, automation } : r));
    setAutomationOpen(false);
  };
  const handleDelete = (report: any) => {
    setReports(reports => reports.filter(r => r.id !== report.id));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <TextField
          placeholder="Search reports..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ width: 320 }}
        />
        <Button variant="contained" color="primary" onClick={handleCreate}>
          Create Report
        </Button>
      </Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Created By</TableCell>
              <TableCell>Last Run</TableCell>
              <TableCell>Automated</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredReports.map(report => (
              <TableRow key={report.id} hover sx={{ cursor: 'pointer' }} onClick={e => {
                if ((e.target as HTMLElement).closest('button')) return;
                setPreviewReport(report);
                setPreviewOpen(true);
              }}>
                <TableCell>{report.name}</TableCell>
                <TableCell>
                  <a href={`mailto:${report.createdBy}`} style={{ color: '#1976d2', textDecoration: 'underline' }}>{report.createdBy}</a>
                </TableCell>
                <TableCell>{report.lastRun}</TableCell>
                <TableCell>{report.automated ? 'Yes' : 'No'}</TableCell>
                <TableCell align="right">
                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <IconButton size="small" color="primary" onClick={e => { e.stopPropagation(); handleEdit(report); }}><EditIcon /></IconButton>
                    <IconButton size="small" color="secondary" onClick={e => { e.stopPropagation(); handleAutomate(report); }}><EmailIcon /></IconButton>
                    <IconButton size="small" color="error" onClick={e => { e.stopPropagation(); handleDelete(report); }}><DeleteIcon /></IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {filteredReports.length === 0 && (
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          No reports found.
        </Typography>
      )}
      <ReportBuilderModal
        open={builderOpen}
        onClose={() => setBuilderOpen(false)}
        onSave={handleSaveReport}
        initialValues={editReport}
      />
      <AutomationModal
        open={automationOpen}
        onClose={() => setAutomationOpen(false)}
        onSave={handleSaveAutomation}
        initialValues={automationReport?.automation || {}}
      />
      <ReportPreviewModal
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        report={previewReport}
      />
    </Box>
  );
};

export default ReportsTab; 