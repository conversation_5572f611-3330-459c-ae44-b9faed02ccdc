import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, MenuItem, Select, FormControl, InputLabel, Box, Typography, TextField } from '@mui/material';
import { <PERSON><PERSON>hart, <PERSON>, <PERSON>Chart, Line, Pie<PERSON>hart, Pie, Cell, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const chartTypes = [
  { label: 'Bar', value: 'bar' },
  { label: 'Line', value: 'line' },
  { label: 'Pie', value: 'pie' },
  { label: 'Donut', value: 'donut' },
  { label: 'Funnel', value: 'funnel' },
  { label: 'Heatmap', value: 'heatmap' },
  { label: 'Number/KPI', value: 'number' },
  { label: 'Percentage', value: 'percentage' },
];

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const VisualizationBuilder = ({
  open,
  onClose,
  onSave,
  initialValues,
  mockDataSets,
  readOnly = false,
}: any) => {
  const [chartType, setChartType] = useState('bar');
  const [dataSetIdx, setDataSetIdx] = useState(0);
  const [title, setTitle] = useState('');
  const [customData, setCustomData] = useState<any[]>([]);

  useEffect(() => {
    if (initialValues) {
      setChartType(initialValues.chartType || 'bar');
      setDataSetIdx(initialValues.dataSetIdx || 0);
      setTitle(initialValues.title || '');
      setCustomData(initialValues.customData || []);
    } else {
      setChartType('bar');
      setDataSetIdx(0);
      setTitle('');
      setCustomData([]);
    }
  }, [initialValues, open]);

  const data = customData.length > 0 ? customData : (mockDataSets[dataSetIdx]?.data || []);

  const handleSave = () => {
    onSave({ chartType, dataSetIdx, title, customData });
  };

  const handleDataChange = (idx: number, key: string, value: string) => {
    setCustomData(prev => {
      const arr = [...prev];
      arr[idx] = { ...arr[idx], [key]: key === 'value' ? Number(value) : value };
      return arr;
    });
  };

  const handleAddRow = () => {
    setCustomData(prev => [...prev, { name: '', value: 0 }]);
  };

  const handleRemoveRow = (idx: number) => {
    setCustomData(prev => prev.filter((_, i) => i !== idx));
  };

  const funnelChart = (
    <BarChart data={data} layout="vertical">
      <XAxis type="number" />
      <YAxis type="category" dataKey="name" />
      <Tooltip />
      <Legend />
      <Bar dataKey="value" fill="#ff8042" />
    </BarChart>
  );

  const donutChart = (
    <PieChart>
      <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} innerRadius={40} fill="#8884d8">
        {data.map((entry: any, idx: number) => (
          <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
        ))}
      </Pie>
      <Tooltip />
      <Legend />
    </PieChart>
  );

  const heatmapChart = (
    <Box sx={{ display: 'grid', gridTemplateColumns: `repeat(${data.length}, 1fr)`, gap: 1, width: '100%', height: 200 }}>
      {data.map((d: any, idx: number) => (
        <Box key={idx} sx={{ bgcolor: COLORS[idx % COLORS.length], color: '#fff', display: 'flex', alignItems: 'center', justifyContent: 'center', height: 80, borderRadius: 1 }}>
          {d.value}
        </Box>
      ))}
    </Box>
  );

  const numberChart = (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
      <Typography variant="h2">{data[0]?.value ?? 0}</Typography>
    </Box>
  );

  const percentageChart = (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
      <Typography variant="h2">{data[0]?.value ?? 0}%</Typography>
    </Box>
  );

  const chartPreview = (
    <Box sx={{ width: '100%', height: 260 }}>
      <ResponsiveContainer width="100%" height="100%">
        {chartType === 'bar' ? (
          <BarChart data={data}>
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        ) : chartType === 'line' ? (
          <LineChart data={data}>
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="value" stroke="#82ca9d" />
          </LineChart>
        ) : chartType === 'pie' ? (
          <PieChart>
            <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} fill="#8884d8">
              {data.map((entry: any, idx: number) => (
                <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        ) : chartType === 'donut' ? donutChart
          : chartType === 'funnel' ? funnelChart
          : chartType === 'heatmap' ? heatmapChart
          : chartType === 'number' ? numberChart
          : chartType === 'percentage' ? percentageChart
          : (<div />)
        }
      </ResponsiveContainer>
    </Box>
  );

  if (readOnly) {
    return (
      <Box>
        <Typography variant="subtitle1" sx={{ mb: 1 }}>{title || 'Untitled Visualization'}</Typography>
        {chartPreview}
      </Box>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{initialValues ? 'Edit Visualization' : 'Build Data Visualization'}</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Title"
            value={title}
            onChange={e => setTitle(e.target.value)}
            fullWidth
          />
          <FormControl fullWidth>
            <InputLabel>Chart Type</InputLabel>
            <Select
              value={chartType}
              label="Chart Type"
              onChange={e => setChartType(e.target.value)}
            >
              {chartTypes.map(type => (
                <MenuItem key={type.value} value={type.value}>{type.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth>
            <InputLabel>Mock Data</InputLabel>
            <Select
              value={dataSetIdx}
              label="Mock Data"
              onChange={e => {
                setDataSetIdx(Number(e.target.value));
                setCustomData([]);
              }}
            >
              {mockDataSets.map((ds: any, idx: number) => (
                <MenuItem key={ds.name} value={idx}>{ds.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>Edit Data</Typography>
            {data.map((row: any, idx: number) => (
              <Box key={idx} sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  size="small"
                  label="Name"
                  value={row.name}
                  onChange={e => handleDataChange(idx, 'name', e.target.value)}
                  sx={{ flex: 1 }}
                />
                <TextField
                  size="small"
                  label="Value"
                  type="number"
                  value={row.value}
                  onChange={e => handleDataChange(idx, 'value', e.target.value)}
                  sx={{ width: 100 }}
                />
                <Button onClick={() => handleRemoveRow(idx)} color="error" size="small">Remove</Button>
              </Box>
            ))}
            <Button onClick={handleAddRow} size="small" variant="outlined">Add Row</Button>
          </Box>
          {chartPreview}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">Save</Button>
      </DialogActions>
    </Dialog>
  );
};

export default VisualizationBuilder; 