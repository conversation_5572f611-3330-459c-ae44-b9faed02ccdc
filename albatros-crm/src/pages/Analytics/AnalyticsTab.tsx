import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, Grid, useTheme, CircularProgress } from '@mui/material';
import { <PERSON><PERSON>hart, Bar, LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { analyticsAPI } from '../../services/api';
import config from '../../config';

const defaultKpis = [
  { label: 'Revenue', value: '$9.12M', sub: '% Diff from Last Month: ****%', color: '#4fd1c5' },
  { label: 'Expected Revenue', value: '$2.05M', sub: 'May 2025', color: '#63b3ed' },
  { label: 'Rounds Booked', value: '312', sub: 'Apr 2025: 298', color: '#f6ad55' },
  { label: 'Rounds Played', value: '128', sub: 'Apr 2025: 120', color: '#fc8181' },
  { label: 'Lost Bookings', value: '1,102', sub: '', color: '#a0aec0' },
  { label: 'Conversion %', value: '69.2%', sub: '', color: '#68d391' },
];

const defaultFunnelData = [
  { name: 'Inquiry', value: 2600, color: '#4fd1c5' },
  { name: 'Booking', value: 2100, color: '#63b3ed' },
  { name: 'Check-in', value: 1500, color: '#f6ad55' },
  { name: 'Played', value: 1280, color: '#fc8181' },
  { name: 'Follow-up', value: 600, color: '#a0aec0' },
];

const defaultLineData = [
  { month: 'Jan 2024', value: 0.7 },
  { month: 'Mar 2024', value: 0.9 },
  { month: 'May 2024', value: 1.1 },
  { month: 'Jul 2024', value: 1.3 },
  { month: 'Sep 2024', value: 1.5 },
  { month: 'Nov 2024', value: 1.7 },
  { month: 'Jan 2025', value: 1.8 },
  { month: 'Mar 2025', value: 2.0 },
  { month: 'May 2025', value: 2.2 },
];

const defaultBarData = [
  { name: 'Target', value: 2.5 },
  { name: 'Actual', value: 1.8 },
];

const COLORS = ['#4fd1c5', '#63b3ed', '#f6ad55', '#fc8181', '#a0aec0', '#68d391'];

const AnalyticsTab: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kpis, setKpis] = useState(defaultKpis);
  const [funnelData, setFunnelData] = useState(defaultFunnelData);
  const [lineData, setLineData] = useState(defaultLineData);
  const [barData, setBarData] = useState(defaultBarData);

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await analyticsAPI.getOverview(config.courseId, 'monthly');
        const data = response.data;
        if (data) {
          setKpis([
            { label: 'Revenue', value: `$${(data.revenue ?? 0).toLocaleString()}`, sub: '', color: '#4fd1c5' },
            { label: 'Bookings', value: `${data.bookings ?? 0}`, sub: '', color: '#63b3ed' },
            { label: 'Utilization', value: `${data.utilization ?? 0}%`, sub: '', color: '#f6ad55' },
          ]);
          setFunnelData([
            { name: 'Revenue', value: data.revenue ?? 0, color: '#4fd1c5' },
            { name: 'Bookings', value: data.bookings ?? 0, color: '#63b3ed' },
            { name: 'Utilization', value: data.utilization ?? 0, color: '#f6ad55' },
          ]);
          setLineData(
            (data.trends?.monthly ?? []).map((v: number, idx: number) => ({ month: `M${idx + 1}`, value: v }))
          );
          setBarData([
            { name: 'Revenue', value: data.revenue ?? 0 },
            { name: 'Bookings', value: data.bookings ?? 0 },
          ]);
        }
      } catch (err: any) {
        setError('Failed to load analytics data.');
      } finally {
        setLoading(false);
      }
    };
    fetchAnalytics();
  }, []);

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}><CircularProgress /></Box>;
  }
  if (error) {
    return <Box sx={{ color: 'error.main', p: 2 }}>{error}</Box>;
  }
  return (
    <Box sx={{ p: 2, borderRadius: 2 }}>
      {/* KPI Row */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        {kpis.map((kpi, idx) => (
          <Grid item xs={12} sm={6} md={2} key={kpi.label}>
            <Paper elevation={3} sx={{ color: kpi.color, p: 2, textAlign: 'center', borderRadius: 2 }}>
              <Typography variant="subtitle2" sx={{ color: theme.palette.text.secondary, mb: 0.5 }}>{kpi.label}</Typography>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>{kpi.value}</Typography>
              <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>{kpi.sub}</Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
      {/* Main Visualizations Grid */}
      <Grid container spacing={2}>
        {/* Funnel Chart */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 2, borderRadius: 2, height: 340 }}>
            <Typography variant="subtitle2" sx={{ color: theme.palette.text.secondary, mb: 1 }}>This Month Booking Pipeline</Typography>
            <ResponsiveContainer width="100%" height={260}>
              <BarChart data={funnelData} layout="vertical">
                <XAxis type="number" hide />
                <YAxis type="category" dataKey="name" tick={{ fill: theme.palette.text.secondary }} />
                <Bar dataKey="value">
                  {funnelData.map((entry, idx) => (
                    <Cell key={entry.name} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        {/* Line Chart */}
        <Grid item xs={12} md={5}>
          <Paper elevation={3} sx={{ p: 2, borderRadius: 2, height: 340 }}>
            <Typography variant="subtitle2" sx={{ color: theme.palette.text.secondary, mb: 1 }}>Rounds Played Trend</Typography>
            <ResponsiveContainer width="100%" height={260}>
              <LineChart data={lineData}>
                <XAxis dataKey="month" tick={{ fill: theme.palette.text.secondary }} />
                <YAxis tick={{ fill: theme.palette.text.secondary }} />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#63b3ed" strokeWidth={3} dot={{ r: 5 }} />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        {/* Bar (Target) Chart */}
        <Grid item xs={12} md={3}>
          <Paper elevation={3} sx={{ p: 2, borderRadius: 2, height: 340, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
            <Typography variant="subtitle2" sx={{ color: theme.palette.text.secondary, mb: 1 }}>Sales Target</Typography>
            <ResponsiveContainer width="60%" height={220}>
              <BarChart data={barData} layout="vertical">
                <XAxis type="number" hide />
                <YAxis type="category" dataKey="name" hide />
                <Bar dataKey="value" fill="#4fd1c5" barSize={40} />
              </BarChart>
            </ResponsiveContainer>
            <Typography variant="caption" sx={{ color: theme.palette.text.secondary, mt: 1 }}>
              Target: ${barData[0]?.value ?? 0}M | Actual: ${barData[1]?.value ?? 0}M
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsTab; 