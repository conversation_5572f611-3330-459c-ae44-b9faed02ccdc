import React, { useState } from 'react';
import { Box, Tabs, Tab, Grid, Paper, Typography, LinearProgress, Card, CardContent } from '@mui/material';
import { Funnel, <PERSON><PERSON><PERSON><PERSON>, LabelList, Tooltip as RechartsTool<PERSON>, ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Legend } from 'recharts';
import ReportsTab from './ReportsTab';

// Mock 2025 data
const metrics = [
  { label: 'Revenue', value: 1200000, diff: 8.2, unit: '$', sub: 'YTD 2025' },
  { label: 'Expected Revenue', value: 1800000, diff: 5.1, unit: '$', sub: '2025 Forecast' },
  { label: 'Bookings in Pipeline', value: 320, diff: -4.5, unit: '', sub: 'Current' },
  { label: 'Rounds Played', value: 9500, diff: 2.3, unit: '', sub: 'YTD 2025' },
  { label: 'Cancellations', value: 210, diff: 1.1, unit: '', sub: '2025' },
  { label: 'Conversion Rate', value: 68.4, diff: 1.7, unit: '%', sub: 'YTD 2025' },
];

const funnelData = [
  { stage: 'Website Visits', value: 12000 },
  { stage: 'Inquiries', value: 3400 },
  { stage: 'Bookings', value: 1800 },
  { stage: 'Check-ins', value: 1550 },
];

const revenueTrend = [
  { month: 'Jan', actual: 80000, forecast: 90000 },
  { month: 'Feb', actual: 95000, forecast: 95000 },
  { month: 'Mar', actual: 110000, forecast: 115000 },
  { month: 'Apr', actual: 120000, forecast: 120000 },
  { month: 'May', actual: 130000, forecast: 135000 },
  { month: 'Jun', actual: 140000, forecast: 145000 },
  { month: 'Jul', actual: 150000, forecast: 155000 },
  { month: 'Aug', actual: 160000, forecast: 165000 },
  { month: 'Sep', actual: 170000, forecast: 175000 },
  { month: 'Oct', actual: 180000, forecast: 185000 },
  { month: 'Nov', actual: 190000, forecast: 195000 },
  { month: 'Dec', actual: 200000, forecast: 205000 },
];

const revenueTarget = 2000000;
const revenueActual = metrics[0].value;
const revenueProgress = Math.min((revenueActual / revenueTarget) * 100, 100);

const cardColor = '#14b8a6';
const cardBg = '#fff';
const cardText = '#0f172a';
const cardSub = '#64748b';

const AnalyticsDashboard: React.FC = () => (
  <>
    {/* Top Metric Cards */}
    <Grid container spacing={2} mb={2}>
      {metrics.map((m, idx) => (
        <Grid item xs={12} sm={6} md={2} key={m.label}>
          <Card sx={{ background: cardBg, color: cardText, boxShadow: 1 }}>
            <CardContent>
              <Typography variant="subtitle2" sx={{ color: cardSub }}>{m.label}</Typography>
              <Typography variant="h5" sx={{ fontWeight: 700, color: cardText }}>
                {m.unit}{m.value.toLocaleString()}
                <Typography component="span" variant="body2" sx={{ color: m.diff >= 0 ? '#10b981' : '#ef4444', ml: 1 }}>
                  {m.diff >= 0 ? '↑' : '↓'} {Math.abs(m.diff)}%
                </Typography>
              </Typography>
              <Typography variant="caption" sx={{ color: cardSub }}>{m.sub}</Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>

    <Grid container spacing={2}>
      {/* Booking Funnel */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, height: '100%' }}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Booking Funnel (2025)</Typography>
          <ResponsiveContainer width="100%" height={260}>
            <FunnelChart>
              <RechartsTooltip />
              <Funnel
                dataKey="value"
                data={funnelData}
                isAnimationActive
                activeShape
                stroke="#14b8a6"
                fill="#38bdf8"
              >
                <LabelList dataKey="stage" position="right" />
              </Funnel>
            </FunnelChart>
          </ResponsiveContainer>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, color: cardSub, fontSize: 13 }}>
            {funnelData.map(f => (
              <span key={f.stage}>{f.stage}</span>
            ))}
          </Box>
        </Paper>
      </Grid>
      {/* Revenue Trend Line Chart */}
      <Grid item xs={12} md={5}>
        <Paper sx={{ p: 2, height: '100%' }}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Revenue Trend with Forecast (2025)</Typography>
          <ResponsiveContainer width="100%" height={260}>
            <LineChart data={revenueTrend} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={v => `$${(v/1000).toFixed(1)}k`} />
              <RechartsTooltip formatter={v => `$${v.toLocaleString()}`} />
              <Legend />
              <Line type="monotone" dataKey="actual" name="Actual" stroke="#14b8a6" strokeWidth={3} dot />
              <Line type="monotone" dataKey="forecast" name="Forecast" stroke="#38bdf8" strokeDasharray="5 5" strokeWidth={2} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
      {/* Revenue Target Progress */}
      <Grid item xs={12} md={3}>
        <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Revenue Target (2025)</Typography>
          <Box sx={{ width: '100%', mb: 1 }}>
            <LinearProgress variant="determinate" value={revenueProgress} sx={{ height: 16, borderRadius: 8, background: '#e0e7ef', '& .MuiLinearProgress-bar': { background: '#14b8a6' } }} />
          </Box>
          <Typography variant="h6" sx={{ fontWeight: 700, color: cardText }}>
            ${revenueActual.toLocaleString()} <span style={{ color: cardSub, fontWeight: 400, fontSize: 16 }}>/ ${revenueTarget.toLocaleString()}</span>
          </Typography>
          <Typography variant="caption" sx={{ color: cardSub }}>YTD Progress</Typography>
        </Paper>
      </Grid>
    </Grid>
  </>
);

const AnalyticsPage: React.FC = () => {
  const [tab, setTab] = useState(0);
  return (
    <Box sx={{ width: '100%', p: 3, background: '#fff', minHeight: '100vh' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Tabs value={tab} onChange={(_, v) => setTab(v)}>
          <Tab label="Analytics" />
          <Tab label="Reports" />
        </Tabs>
      </Box>
      {tab === 0 && <AnalyticsDashboard />}
      {tab === 1 && <ReportsTab />}
    </Box>
  );
};

export default AnalyticsPage; 