import React, { useState } from 'react';
import { Box, TextField, Button, Typography, CircularProgress, Alert } from '@mui/material';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

const XANO_BASE_URL = 'https://x8ki-letl-twmt.n7.xano.io/api:9X1kC8oi';
const LOGIN_ENDPOINT = '/auth/login';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      // First, clear any existing tokens and headers
      localStorage.removeItem('xano_token');
      delete axios.defaults.headers.common['Authorization'];

      // Make the login request
      const response = await axios.post(`${XANO_BASE_URL}${LOGIN_ENDPOINT}`, {
        email,
        password,
      });
      
      console.log('Full Login Response:', response);
      const token = response.data?.authToken || response.data?.token;
      console.log('Raw Token:', token);

      if (token) {
        // Store the raw token
        localStorage.setItem('xano_token', token);
        console.log('Stored raw token:', localStorage.getItem('xano_token'));

        // Create a new axios instance for authenticated requests
        const authAxios = axios.create({
          baseURL: XANO_BASE_URL,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // Verify the token by making a test request
        try {
          console.log('Making test request with token:', token);
          const testResponse = await authAxios.get('/user');
          console.log('Test Response:', testResponse);
          
          if (testResponse.status === 200) {
            // If verification successful, set up global axios defaults
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            console.log('Global headers set:', axios.defaults.headers.common);
            navigate('/');
          } else {
            throw new Error('Unexpected response status: ' + testResponse.status);
          }
        } catch (verifyError: any) {
          console.error('Token verification failed:', {
            error: verifyError,
            response: verifyError.response,
            request: verifyError.request,
            config: verifyError.config
          });
          setError('Login successful but token verification failed. Please try again.');
          localStorage.removeItem('xano_token');
          delete axios.defaults.headers.common['Authorization'];
        }
      } else {
        console.error('No token in response. Full response:', response.data);
        setError('No token returned from server.');
      }
    } catch (err: any) {
      console.error('Login error:', {
        error: err,
        response: err.response,
        request: err.request,
        config: err.config
      });
      setError(err.response?.data?.message || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
      <Typography variant="h5" sx={{ mb: 2 }}>Login</Typography>
      <form onSubmit={handleLogin} style={{ width: 320 }}>
        <TextField
          label="Email"
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          fullWidth
          required
          sx={{ mb: 2 }}
        />
        <TextField
          label="Password"
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          fullWidth
          required
          sx={{ mb: 2 }}
        />
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        <Button type="submit" variant="contained" color="primary" fullWidth disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Login'}
        </Button>
      </form>
    </Box>
  );
};

export default LoginPage; 