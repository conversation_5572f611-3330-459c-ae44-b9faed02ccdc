import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  styled,
  Paper,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import { Settings as SettingsIcon } from '@mui/icons-material';
import {
  MenuList,
  MenuCategories,
  MenuActions,
  MenuItemSkeleton,
  MenuItemEditor,
  CategoryManager,
  mockCategories,
  mockMenuItems,
  TabValue,
  MenuItem,
  MenuCategory,
  OrdersTab,
  ReportsTab,
} from '../components/NineteenthHole';
import GrillLayoutEditor from '../components/NineteenthHole/GrillLayoutEditor';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3),
  '& .MuiTab-root': {
    textTransform: 'none',
    minWidth: 120,
    padding: theme.spacing(2),
    color: theme.palette.text.secondary,
    fontSize: '1rem',
    fontWeight: 500,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const PageContainer = styled(Paper)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  boxShadow: theme.shadows[3],
  margin: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  [theme.breakpoints.up('sm')]: {
    margin: theme.spacing(3),
  },
  transition: 'box-shadow 0.3s ease-in-out',
  '&:hover': {
    boxShadow: theme.shadows[5],
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(4),
  },
}));

interface NineteenthHolePageProps {
  courseId: string;
}

// Cache for storing loaded items by category
const categoryCache = new Map<string, MenuItem[]>();

const NineteenthHolePage: React.FC<NineteenthHolePageProps> = ({ courseId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [currentTab, setCurrentTab] = useState<TabValue>('menu');
  const [selectedCategory, setSelectedCategory] = useState<string>('sandwiches');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<MenuCategory[]>(mockCategories);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isItemEditorOpen, setIsItemEditorOpen] = useState<boolean>(false);
  const [isCategoryManagerOpen, setIsCategoryManagerOpen] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<MenuItem | undefined>(undefined);

  // Memoize filtered items to prevent unnecessary recalculations
  const filteredItems = useMemo(() => {
    if (!searchTerm) return menuItems;
    
    const searchLower = searchTerm.toLowerCase();
    return menuItems.filter(item =>
      item.name.toLowerCase().includes(searchLower) ||
      item.description.toLowerCase().includes(searchLower) ||
      item.ingredients?.some((ing: string) => ing.toLowerCase().includes(searchLower))
    );
  }, [menuItems, searchTerm]);

  const loadMenuItems = useCallback(async (category: string) => {
    // Check cache first
    if (categoryCache.has(category)) {
      setMenuItems(categoryCache.get(category)!);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const items = mockMenuItems.filter(item => item.category === category);
      categoryCache.set(category, items);
      setMenuItems(items);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadMenuItems(selectedCategory);
  }, [selectedCategory, loadMenuItems]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: TabValue) => {
    setCurrentTab(newValue);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleMobileAppToggle = async (itemId: string, currentValue: boolean) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setMenuItems(prevItems =>
        prevItems.map(item =>
          item.id === itemId ? { ...item, mobileApp: !currentValue } : item
        )
      );
      // Update cache
      categoryCache.set(selectedCategory, menuItems);
    } catch (err) {
      setError('Failed to update mobile app status');
    }
  };

  const handleEditItem = (item: MenuItem) => {
    setEditingItem(item);
    setIsItemEditorOpen(true);
  };

  const handleAddItem = () => {
    setEditingItem(undefined);
    setIsItemEditorOpen(true);
  };

  const handleSaveItem = (item: MenuItem) => {
    if (editingItem) {
      // If the category has changed, we need to update both categories
      if (editingItem.category !== item.category) {
        // Remove the item from the old category's cache
        const oldCategoryItems = categoryCache.get(editingItem.category) || [];
        categoryCache.set(
          editingItem.category,
          oldCategoryItems.filter(i => i.id !== item.id)
        );

        // Add the item to the new category's cache
        const newCategoryItems = categoryCache.get(item.category) || [];
        categoryCache.set(
          item.category,
          [...newCategoryItems, item]
        );

        // If we're currently viewing either the old or new category, update the display
        if (selectedCategory === editingItem.category) {
          setMenuItems(prevItems => prevItems.filter(i => i.id !== item.id));
        } else if (selectedCategory === item.category) {
          setMenuItems(prevItems => [...prevItems, item]);
        }
      } else {
        // Same category, just update the item
        setMenuItems(prevItems =>
          prevItems.map(prevItem =>
            prevItem.id === item.id ? item : prevItem
          )
        );
        // Update cache for the current category
        const categoryItems = categoryCache.get(item.category) || [];
        categoryCache.set(
          item.category,
          categoryItems.map(i => i.id === item.id ? item : i)
        );
      }
    } else {
      // Adding new item
      // Add to the category cache
      const categoryItems = categoryCache.get(item.category) || [];
      categoryCache.set(
        item.category,
        [...categoryItems, { ...item, id: Date.now().toString() }] // Generate a temporary ID
      );

      // If we're currently viewing the category where the item was added, update the display
      if (selectedCategory === item.category) {
        setMenuItems(prevItems => [...prevItems, { ...item, id: Date.now().toString() }]);
      }
    }

    setIsItemEditorOpen(false);
    setEditingItem(undefined);
  };

  const handleSaveCategories = (updatedCategories: MenuCategory[]) => {
    setCategories(updatedCategories);
  };

  const handleDeleteItem = (item: MenuItem) => {
    // Remove from current display
    setMenuItems(prevItems => prevItems.filter(i => i.id !== item.id));
    
    // Remove from cache
    const categoryItems = categoryCache.get(item.category) || [];
    categoryCache.set(
      item.category,
      categoryItems.filter(i => i.id !== item.id)
    );
  };

  const handleReorderItems = (reorderedItems: MenuItem[]) => {
    setMenuItems(reorderedItems);
    // Update cache for the current category
    categoryCache.set(selectedCategory, reorderedItems);
  };

  const handleReorderCategories = (reorderedCategories: MenuCategory[]) => {
    setCategories(reorderedCategories);
  };

  if (error) {
    return (
      <PageContainer>
        <ContentContainer>
          <Typography color="error" variant="h6" sx={{ mb: 2 }}>
            Error: {error}
          </Typography>
        </ContentContainer>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <ContentContainer>
        {/* Top Navigation */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <StyledTabs 
            value={currentTab} 
            onChange={handleTabChange}
            variant={isMobile ? "fullWidth" : "standard"}
            centered={!isMobile}
          >
            <Tab label="Menu" value="menu" />
            <Tab label="Orders" value="orders" />
            <Tab label="Reports" value="reports" />
            <Tab label="Layout" value="layout" />
          </StyledTabs>
          {currentTab === 'menu' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Open Restaurant POS">
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => window.open('/19th-hole-pos', '_blank')}
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Restaurant POS
                </Button>
              </Tooltip>
              <Tooltip title="Manage Categories">
                <IconButton onClick={() => setIsCategoryManagerOpen(true)}>
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {currentTab === 'menu' && (
          <>
            <MenuActions
              onAdd={handleAddItem}
              onEdit={() => setIsEditMode(!isEditMode)}
              onSearch={handleSearch}
              searchTerm={searchTerm}
              isEditMode={isEditMode}
            />

            <MenuCategories
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={handleCategoryChange}
              onReorderCategories={isEditMode ? handleReorderCategories : undefined}
              isEditMode={isEditMode}
            />

            {loading ? (
              <Box sx={{ mt: 3 }}>
                {[...Array(3)].map((_, index) => (
                  <MenuItemSkeleton key={index} />
                ))}
              </Box>
            ) : (
              <MenuList
                items={filteredItems}
                onMobileAppToggle={handleMobileAppToggle}
                onEditItem={handleEditItem}
                onDeleteItem={isEditMode ? handleDeleteItem : undefined}
                onReorderItems={isEditMode ? handleReorderItems : undefined}
                isEditMode={isEditMode}
              />
            )}

            {/* Item Editor Dialog */}
            <MenuItemEditor
              open={isItemEditorOpen}
              onClose={() => setIsItemEditorOpen(false)}
              onSave={handleSaveItem}
              categories={categories}
              item={editingItem}
              onCategoriesChange={handleSaveCategories}
            />

            {/* Category Manager Dialog */}
            <CategoryManager
              open={isCategoryManagerOpen}
              onClose={() => setIsCategoryManagerOpen(false)}
              onSave={handleSaveCategories}
              categories={categories}
            />
          </>
        )}

        {currentTab === 'orders' && (
          <OrdersTab />
        )}

        {currentTab === 'reports' && (
          <ReportsTab />
        )}

        {currentTab === 'layout' && (
          <GrillLayoutEditor />
        )}
      </ContentContainer>
    </PageContainer>
  );
};

export default NineteenthHolePage; 