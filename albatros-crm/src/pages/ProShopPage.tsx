import {
  Add as AddIcon,
  Edit as EditIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  Paper,
  styled,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
  Alert
} from '@mui/material';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  CategoryManager,
  mockCategories,
  OrdersTab,
  Product,
  ProductActions,
  ProductCategories,
  ProductCategory,
  ProductItemEditor,
  ProductItemSkeleton,
  ProductList,
  ReportsTab
} from '../components/ProShop';
import { useProShopItems, useCreateProShopItem, useUpdateProShopItem } from '../api/hooks/useProShopItems';
import { useXanoAuth } from '../api/hooks/useXanoAuth';
import { ProShopItem } from '../types/xano';
import LoadingState from '../components/shared/LoadingState';
import ErrorBoundary from '../components/shared/ErrorBoundary';
import config from '../config';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3),
  '& .MuiTab-root': {
    textTransform: 'none',
    minWidth: 120,
    padding: theme.spacing(2),
    color: theme.palette.text.secondary,
    fontSize: '1rem',
    fontWeight: 500,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const PageContainer = styled(Paper)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  boxShadow: theme.shadows[3],
  margin: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  [theme.breakpoints.up('sm')]: {
    margin: theme.spacing(3),
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(4),
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  height: '48px',
  borderRadius: theme.shape.borderRadius * 2,
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[2],
  },
}));

interface ProShopPageProps {
  courseId: string;
}

// Utility functions to convert between XANO and component formats
const convertXanoItemToComponent = (xanoItem: ProShopItem): Product => {
  return {
    id: xanoItem.id.toString(),
    name: xanoItem.pro_shop_item_name,
    description: xanoItem.description || '',
    price: xanoItem.price,
    category: xanoItem.pro_shop_category_name,
    imageUrl: xanoItem.pro_shop_item_photo,
    inStock: xanoItem.inventory_in_stock > 0,
    quantity: xanoItem.inventory_in_stock,
    brand: '', // XANO doesn't have brand field yet
    mobileApp: xanoItem.pro_shop_mobile_item_offered || false,
    order: 1 // Default order
  };
};

const convertComponentItemToXano = (product: Product, golf_course_id: number): any => {
  return {
    golf_course_id,
    pro_shop_category_name: product.category,
    pro_shop_item_name: product.name,
    pro_shop_item_sku: product.id, // Use ID as SKU for now
    description: product.description,
    price: product.price,
    inventory_in_stock: product.quantity,
    pro_shop_mobile_item_offered: product.mobileApp,
    pro_shop_item_photo: product.imageUrl
  };
};

// Cache for storing loaded items by category
const categoryCache = new Map<string, Product[]>();

const ProShopPage: React.FC<ProShopPageProps> = ({ courseId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { isAuthenticated } = useXanoAuth();
  const golf_course_id = parseInt(config.courseId);

  const [activeTab, setActiveTab] = useState<'products' | 'orders' | 'reports'>('products');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(mockCategories[0]?.id || null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [categories, setCategories] = useState<ProductCategory[]>(mockCategories);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isItemEditorOpen, setIsItemEditorOpen] = useState<boolean>(false);
  const [isCategoryManagerOpen, setIsCategoryManagerOpen] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<Product | undefined>(undefined);

  // XANO API hooks
  const {
    data: xanoItems = [],
    isLoading,
    error: apiError,
    refetch
  } = useProShopItems({
    golf_course_id,
    // Filter by category if needed
    ...(selectedCategory && { category: selectedCategory })
  });

  const createItemMutation = useCreateProShopItem();
  const updateItemMutation = useUpdateProShopItem();

  // Convert XANO items to component format
  const products = useMemo(() => {
    return xanoItems.map(convertXanoItemToComponent);
  }, [xanoItems]);

  // Show authentication required state
  if (!isAuthenticated) {
    return (
      <PageContainer>
        <ContentContainer>
          <Alert severity="warning">
            Please log in to access the Pro Shop.
          </Alert>
        </ContentContainer>
      </PageContainer>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <PageContainer>
        <ContentContainer>
          <LoadingState message="Loading Pro Shop items..." />
        </ContentContainer>
      </PageContainer>
    );
  }

  // Show error state
  if (apiError) {
    return (
      <PageContainer>
        <ContentContainer>
          <Alert severity="error" sx={{ mb: 2 }}>
            Failed to load Pro Shop items: {apiError.message}
          </Alert>
          <Button onClick={() => refetch()}>Retry</Button>
        </ContentContainer>
      </PageContainer>
    );
  }

  // Filter products by category and search term
  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Filter by category
    if (selectedCategory) {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        filtered = filtered.filter(item => item.category === category.name);
      }
    }

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.brand?.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }, [products, selectedCategory, categories, searchTerm]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleMobileAppToggle = async (itemId: string, currentValue: boolean) => {
    try {
      const product = products.find(p => p.id === itemId);
      if (!product) return;

      const updateData = {
        pro_shop_mobile_item_offered: !currentValue
      };

      await updateItemMutation.mutateAsync({
        id: parseInt(itemId),
        data: updateData
      });
    } catch (err) {
      console.error('Failed to update mobile app status:', err);
    }
  };

  const handleEditItem = (item: Product) => {
    setEditingItem(item);
    setIsItemEditorOpen(true);
  };

  const handleAddItem = () => {
    setEditingItem(undefined);
    setIsItemEditorOpen(true);
  };

  const handleSaveItem = async (item: Product) => {
    try {
      if (editingItem) {
        // Update existing item
        const updateData = convertComponentItemToXano(item, golf_course_id);
        await updateItemMutation.mutateAsync({
          id: parseInt(item.id),
          data: updateData
        });
      } else {
        // Create new item
        const createData = convertComponentItemToXano(item, golf_course_id);
        await createItemMutation.mutateAsync(createData);
      }

      setIsItemEditorOpen(false);
      setEditingItem(undefined);
    } catch (err) {
      console.error('Failed to save item:', err);
    }
  };

  const handleSaveCategories = (updatedCategories: ProductCategory[]) => {
    setCategories(updatedCategories);
  };

  const handleDeleteItem = async (item: Product) => {
    try {
      // Note: Add delete mutation when available in API
      console.log('Delete item:', item.id);
      // await deleteItemMutation.mutateAsync(parseInt(item.id));
    } catch (err) {
      console.error('Failed to delete item:', err);
    }
  };


  const handleReorderCategories = (newCategories: ProductCategory[]) => {
    setCategories(newCategories);
  };

  return (
    <ErrorBoundary>
      <PageContainer>
        <ContentContainer>
        {/* Top Navigation */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}>
          <StyledTabs 
            value={activeTab} 
            onChange={(_, newValue) => setActiveTab(newValue as 'products' | 'orders' | 'reports')}
            variant={isMobile ? "fullWidth" : "standard"}
            centered={!isMobile}
          >
            <Tab label="Products" value="products" />
            <Tab label="Orders" value="orders" />
            <Tab label="Reports" value="reports" />
          </StyledTabs>
          {activeTab === 'products' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Open Checkout">
                <ActionButton
                  variant="contained"
                  color="primary"
                  onClick={() => window.open('/pro-shop-pos', '_blank')}
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Checkout
                </ActionButton>
              </Tooltip>
              <Tooltip title="Manage Categories">
                <IconButton onClick={() => setIsCategoryManagerOpen(true)}>
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {activeTab === 'products' && (
          <>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              gap: 2,
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'stretch', sm: 'center' },
              mt: 3,
              mb: 3,
            }}>
              <Box sx={{ flex: 1 }}>
                <ProductActions
                  onSearch={handleSearch}
                  searchTerm={searchTerm}
                />
              </Box>
              <Box sx={{ 
                display: 'flex', 
                gap: 1,
                flexShrink: 0,
              }}>
                <Tooltip title={isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}>
                  <ActionButton
                    variant={isEditMode ? 'contained' : 'outlined'}
                    color={isEditMode ? 'primary' : 'inherit'}
                    onClick={() => setIsEditMode(!isEditMode)}
                    startIcon={<EditIcon />}
                  >
                    {isEditMode ? 'Save Changes' : 'Edit Inventory'}
                  </ActionButton>
                </Tooltip>
                <Tooltip title="Add New Item">
                  <ActionButton
                    variant="contained"
                    color="primary"
                    onClick={handleAddItem}
                    startIcon={<AddIcon />}
                  >
                    Add Item
                  </ActionButton>
                </Tooltip>
              </Box>
            </Box>

            <ProductCategories
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              isEditMode={isEditMode}
              onReorderCategories={handleReorderCategories}
            />

            <ProductList
              items={filteredProducts}
              onMobileAppToggle={handleMobileAppToggle}
              onEditItem={handleEditItem}
              onDeleteItem={isEditMode ? handleDeleteItem : undefined}
              isEditMode={isEditMode}
            />

            <ProductItemEditor
              open={isItemEditorOpen}
              onClose={() => {
                setIsItemEditorOpen(false);
                setEditingItem(undefined);
              }}
              onSave={handleSaveItem}
              categories={categories}
              item={editingItem}
              isEditMode={isEditMode}
              onCategoriesChange={handleSaveCategories}
            />

            <CategoryManager
              open={isCategoryManagerOpen}
              onClose={() => setIsCategoryManagerOpen(false)}
              onSave={handleSaveCategories}
              categories={categories}
            />
          </>
        )}

        {activeTab === 'orders' && (
          <OrdersTab />
        )}

        {activeTab === 'reports' && (
          <ReportsTab />
        )}
        </ContentContainer>
      </PageContainer>
    </ErrorBoundary>
  );
};

export default ProShopPage; 