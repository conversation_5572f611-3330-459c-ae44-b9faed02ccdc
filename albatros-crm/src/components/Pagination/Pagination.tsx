import React from 'react';
import {
  Box,
  PaginationItem,
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import { PaginationProps } from './types';
import { StyledPagination } from './styles';

export const Pagination: React.FC<PaginationProps> = ({
  count,
  page,
  onChange,
  itemsPerPage = 10,
  showFirstButton = true,
  showLastButton = true,
  size = 'medium',
}) => {
  const totalPages = Math.ceil(count / itemsPerPage);

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
      <StyledPagination
        count={totalPages}
        page={page}
        onChange={onChange}
        size={size}
        showFirstButton={showFirstButton}
        showLastButton={showLastButton}
        renderItem={(item) => (
          <PaginationItem
            {...item}
            slots={{
              previous: ChevronLeftIcon,
              next: ChevronRightIcon,
            }}
          />
        )}
      />
    </Box>
  );
}; 