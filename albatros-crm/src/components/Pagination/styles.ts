import { styled } from '@mui/material';
import { Pagination } from '@mui/material';

export const StyledPagination = styled(Pagination)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  padding: theme.spacing(2),
  '& .MuiPaginationItem-root': {
    margin: theme.spacing(0.5),
    borderRadius: theme.shape.borderRadius,
    '&.Mui-selected': {
      backgroundColor: '#4ADE80',
      color: 'white',
      '&:hover': {
        backgroundColor: '#4ADE80',
      },
    },
  },
}));

export const PaginationButton = styled('button')(({ theme }) => ({
  '&.active': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  },
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  },
})); 