import React, { useState, useEffect, useRef } from 'react';

interface PaymentResult {
  id: string;
  status: string;
  receiptNumber?: string;
  receiptUrl?: string;
  totalMoney: {
    amount: number;
    currency: string;
  };
}

interface SquareConfig {
  applicationId: string;
  locationId: string;
  environment: 'sandbox' | 'production';
}

interface SquarePaymentFormProps {
  amount: number;
  onPaymentSuccess: (result: PaymentResult) => void;
  onPaymentError: (error: string) => void;
  config: SquareConfig;
}

declare global {
  interface Window {
    Square: any;
  }
}

export const SquarePaymentForm: React.FC<SquarePaymentFormProps> = ({
  amount,
  onPaymentSuccess,
  onPaymentError,
  config,
}) => {
  const [payments, setPayments] = useState<any>(null);
  const [card, setCard] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const cardContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initializeSquare = async () => {
      if (!window.Square) {
        onPaymentError('Square.js not loaded');
        return;
      }
      try {
        const paymentsInstance = window.Square.payments(
          config.applicationId,
          config.locationId
        );
        setPayments(paymentsInstance);
        const cardInstance = await paymentsInstance.card();
        await cardInstance.attach(cardContainerRef.current);
        setCard(cardInstance);
      } catch (error) {
        onPaymentError('Failed to initialize payment form');
      }
    };
    initializeSquare();
    return () => {
      if (card) card.destroy();
    };
    // eslint-disable-next-line
  }, [config]);

  const handlePayment = async () => {
    if (!card) {
      onPaymentError('Payment form not initialized');
      return;
    }
    setIsLoading(true);
    try {
      const result = await card.tokenize();
      if (result.status === 'OK') {
        // Send the token to your backend
        const response = await fetch('/api/payments/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sourceId: result.token,
            amount: amount * 100, // Convert to cents
            currency: 'USD',
            locationId: config.locationId,
          }),
        });
        if (!response.ok) throw new Error('Payment processing failed');
        const paymentResult = await response.json();
        onPaymentSuccess(paymentResult);
      } else {
        onPaymentError(result.errors?.[0]?.message || 'Tokenization failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      onPaymentError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="square-payment-form">
      <div className="payment-amount">
        <h3>Amount: ${amount.toFixed(2)}</h3>
      </div>
      <div
        ref={cardContainerRef}
        id="card-container"
        style={{
          minHeight: '200px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          marginBottom: '20px',
        }}
      />
      <button
        onClick={handlePayment}
        disabled={isLoading || !card}
        style={{
          backgroundColor: '#0066cc',
          color: 'white',
          padding: '12px 24px',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          opacity: isLoading ? 0.6 : 1,
        }}
      >
        {isLoading ? 'Processing...' : `Pay $${amount.toFixed(2)}`}
      </button>
    </div>
  );
}; 