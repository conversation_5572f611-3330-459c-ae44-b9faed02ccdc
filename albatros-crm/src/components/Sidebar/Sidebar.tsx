import React from 'react';
import {
  Drawer,
  Box,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  styled
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { ReactComponent as AlbatrossLogo } from '../../albatross-logo.svg';
import { SidebarProps } from './types';

interface StyledDrawerProps {
  isExpanded: boolean;
}

const StyledDrawer = styled(Drawer, {
  shouldForwardProp: prop => prop !== 'isExpanded'
})<StyledDrawerProps>(({ theme, isExpanded }) => ({
  width: isExpanded ? 280 : 72,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: isExpanded ? 280 : 72,
    boxSizing: 'border-box',
    backgroundColor: '#1E293B',
    color: 'white',
    border: 'none',
    borderRadius: 0,
    overflowX: 'hidden',
    transition: theme.transitions.create(['width'], {
      easing: theme.transitions.easing.easeInOut,
      duration: 150,
    }),
    '&:hover': {
      cursor: 'pointer',
    },
  },
}));

interface LogoContainerProps {
  isExpanded: boolean;
}

const LogoContainer = styled(Box, {
  shouldForwardProp: prop => prop !== 'isExpanded'
})<LogoContainerProps>(({ theme, isExpanded }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(4),
  justifyContent: isExpanded ? 'flex-start' : 'center'
}));

interface LogoBoxProps {
  isExpanded: boolean;
}

const LogoBox = styled(Box, {
  shouldForwardProp: prop => prop !== 'isExpanded'
})<LogoBoxProps>(({ theme, isExpanded }) => ({
  width: 40,
  height: 40,
  marginRight: isExpanded ? theme.spacing(2) : 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}));

interface StyledListItemProps {
  isExpanded: boolean;
}

const StyledListItem = styled(ListItemButton, {
  shouldForwardProp: prop => prop !== 'isExpanded'
})<StyledListItemProps>(({ theme, isExpanded }) => ({
  height: 48,
  padding: theme.spacing(0, 2),
  justifyContent: isExpanded ? 'flex-start' : 'center',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  transition: theme.transitions.create(['background-color', 'margin', 'padding'], {
    easing: theme.transitions.easing.easeInOut,
    duration: 150,
  }),
  cursor: 'pointer',
}));

interface StyledListItemIconProps {
  isExpanded: boolean;
}

const StyledListItemIcon = styled(ListItemIcon, {
  shouldForwardProp: prop => prop !== 'isExpanded'
})<StyledListItemIconProps>(({ theme, isExpanded }) => ({
  color: 'inherit',
  minWidth: isExpanded ? 40 : 'auto',
  marginRight: isExpanded ? theme.spacing(2) : 0,
  transition: theme.transitions.create(['margin-right', 'min-width'], {
    easing: theme.transitions.easing.easeInOut,
    duration: 150,
  }),
}));

const CourseInfoFooter = styled(Box)(({ theme }) => ({
  marginTop: 'auto',
  padding: theme.spacing(2),
  borderTop: '1px solid rgba(255, 255, 255, 0.1)'
}));

/**
 * Sidebar component that handles the main navigation of the application
 * @param {SidebarProps} props - Component props
 */
export const Sidebar: React.FC<SidebarProps> = ({ 
  isExpanded, 
  onMouseEnter, 
  onMouseLeave, 
  menuItems,
  courseInfo 
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const getSelectedIndex = (): number => {
    const path = location.pathname.substring(1); // Remove leading slash
    return menuItems.findIndex(item => item.path === path);
  };

  const handleMenuItemClick = (path: string): void => {
    navigate(`/${path}`);
  };

  return (
    <StyledDrawer
      variant="permanent"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      isExpanded={isExpanded}
    >
      <Box sx={{ p: isExpanded ? 3 : 2 }}>
        {/* Logo Section */}
        <LogoContainer isExpanded={isExpanded}>
          <LogoBox isExpanded={isExpanded}>
            <AlbatrossLogo />
          </LogoBox>
          {isExpanded && (
            <Typography variant="h6" fontWeight="bold">ALBATROSS</Typography>
          )}
        </LogoContainer>

        {/* Navigation Menu */}
        <List sx={{ mb: 4 }}>
          {menuItems.map((item, index) => (
            <StyledListItem
              key={item.text}
              selected={getSelectedIndex() === index}
              onClick={() => handleMenuItemClick(item.path)}
              isExpanded={isExpanded}
            >
              <StyledListItemIcon isExpanded={isExpanded}>
                {item.icon}
              </StyledListItemIcon>
              {isExpanded && <ListItemText primary={item.text} />}
            </StyledListItem>
          ))}
        </List>

        {/* Course Info Footer */}
        {isExpanded && courseInfo && (
          <CourseInfoFooter>
            <Typography variant="subtitle2" color="white">
            Westdale Hills Golf Course
            </Typography>
            {courseInfo.location && (
              <Typography variant="body2" color="text.secondary">
                Euless, TX
              </Typography>
            )}
          </CourseInfoFooter>
        )}
      </Box>
    </StyledDrawer>
  );
};

export default Sidebar; 