import { MenuItem } from '../../types/navigation';

export interface SidebarProps {
  isExpanded: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  selectedIndex: number;
  onMenuSelect: (index: number) => void;
  menuItems: MenuItem[];
  courseInfo: CourseInfo | null;
}

export interface CourseInfo {
  id: string;
  name: string;
  location?: {
    city: string;
    state: string;
  };
} 