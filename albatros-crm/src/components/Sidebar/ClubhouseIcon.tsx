import React from 'react';
import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';

const ClubhouseIcon: React.FC<SvgIconProps> = (props) => (
  <SvgIcon {...props} viewBox="0 0 48 48" fill="none">
    {/* Clubhouse base */}
    <rect x="8" y="20" width="32" height="18" rx="2" fill="#E0E7EF" stroke="#334155" strokeWidth="2" />
    {/* Roof */}
    <polygon points="8,20 24,8 40,20" fill="#64748B" stroke="#334155" strokeWidth="2" />
    {/* Door */}
    <rect x="21" y="28" width="6" height="10" rx="1" fill="#A78BFA" stroke="#334155" strokeWidth="2" />
    {/* Windows */}
    <rect x="12" y="24" width="4" height="4" rx="1" fill="#60A5FA" stroke="#334155" strokeWidth="1.5" />
    <rect x="32" y="24" width="4" height="4" rx="1" fill="#60A5FA" stroke="#334155" strokeWidth="1.5" />
    <rect x="12" y="32" width="4" height="4" rx="1" fill="#60A5FA" stroke="#334155" strokeWidth="1.5" />
    <rect x="32" y="32" width="4" height="4" rx="1" fill="#60A5FA" stroke="#334155" strokeWidth="1.5" />
    {/* Golf ball on top */}
    <circle cx="24" cy="5.5" r="4.5" fill="#FEF9C3" stroke="#334155" strokeWidth="2" />
    <circle cx="24" cy="5.5" r="4.5" fill="none" stroke="#334155" strokeWidth="2" />
    {/* Dimples */}
    <circle cx="22.5" cy="4.5" r="0.5" fill="#334155" />
    <circle cx="25.5" cy="6" r="0.5" fill="#334155" />
    <circle cx="23.5" cy="7" r="0.5" fill="#334155" />
    <circle cx="26" cy="4" r="0.5" fill="#334155" />
  </SvgIcon>
);

export default ClubhouseIcon; 