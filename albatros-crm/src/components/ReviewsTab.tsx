import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Rating,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Avatar,
  Divider,
  IconButton,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Star,
  Google,
  Email,
  Sms,
  Refresh,
  Settings,
  Send,
  Visibility,
  VisibilityOff,
  FilterList,
  Sort,
  Delete,
  Edit,
  SmartToy,
  ExpandMore,
  ContentCopy,
  ThumbUp,
  ThumbDown
} from '@mui/icons-material';
import { generateAIResponse, type AIResponse } from '../services/aiReviewService';

// Types
interface Review {
  id: string;
  source: 'google' | 'internal' | 'email' | 'sms';
  author: string;
  rating: number;
  comment: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  golfCourseId: string;
  teeTimeId?: string;
  golferId?: string;
  response?: string;
  helpful: number;
  flagged: boolean;
}

interface ReviewCampaign {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'in-app';
  template: string;
  trigger: 'round_completion' | 'manual' | 'scheduled';
  status: 'active' | 'inactive' | 'draft';
  sentCount: number;
  responseRate: number;
  createdAt: string;
  courseId: string;
}

interface GoogleBusinessConfig {
  placeId: string;
  apiKey: string;
  businessName: string;
  lastSync: string;
  autoSync: boolean;
}

interface CreateCampaignData {
  name: string;
  type: 'email' | 'sms' | 'in-app';
  template: string;
  trigger: 'round_completion' | 'manual' | 'scheduled';
}

// Mock data
const mockReviews: Review[] = [
  {
    id: '1',
    source: 'google',
    author: 'John Smith',
    rating: 5,
    comment: 'Amazing course! The greens were in perfect condition and the staff was incredibly friendly. Will definitely be back!',
    date: '2024-01-15T10:30:00Z',
    status: 'approved',
    golfCourseId: 'course1',
    helpful: 12,
    flagged: false
  },
  {
    id: '2',
    source: 'internal',
    author: 'Sarah Johnson',
    rating: 4,
    comment: 'Great experience overall. The pace of play was good and the course was well-maintained.',
    date: '2024-01-14T15:45:00Z',
    status: 'approved',
    golfCourseId: 'course1',
    teeTimeId: 'tt1',
    golferId: 'golfer1',
    helpful: 8,
    flagged: false
  },
  {
    id: '3',
    source: 'google',
    author: 'Mike Wilson',
    rating: 3,
    comment: 'Course was okay but the pace was slow. Staff could be more attentive.',
    date: '2024-01-13T09:15:00Z',
    status: 'pending',
    golfCourseId: 'course1',
    helpful: 3,
    flagged: true
  },
  {
    id: '4',
    source: 'email',
    author: 'Lisa Davis',
    rating: 5,
    comment: 'Absolutely loved the course! The pro shop had everything I needed and the restaurant food was delicious.',
    date: '2024-01-12T14:20:00Z',
    status: 'approved',
    golfCourseId: 'course1',
    golferId: 'golfer2',
    helpful: 15,
    flagged: false
  },
  {
    id: '5',
    source: 'sms',
    author: 'Robert Brown',
    rating: 4,
    comment: 'Good value for money. Course conditions were excellent and the staff was helpful.',
    date: '2024-01-11T11:30:00Z',
    status: 'approved',
    golfCourseId: 'course1',
    golferId: 'golfer3',
    helpful: 6,
    flagged: false
  }
];

const mockCampaigns: ReviewCampaign[] = [
  {
    id: '1',
    name: 'Post-Round Email Campaign',
    type: 'email',
    template: 'Thank you for playing at {courseName}! How was your experience? Rate us: {link}',
    trigger: 'round_completion',
    status: 'active',
    sentCount: 1250,
    responseRate: 23.5,
    createdAt: '2024-01-01T00:00:00Z',
    courseId: 'course1'
  },
  {
    id: '2',
    name: 'SMS Follow-up',
    type: 'sms',
    template: 'Thanks for golfing with us! Rate your experience: {link}',
    trigger: 'manual',
    status: 'active',
    sentCount: 850,
    responseRate: 18.2,
    createdAt: '2024-01-05T00:00:00Z',
    courseId: 'course1'
  },
  {
    id: '3',
    name: 'In-App Review Request',
    type: 'in-app',
    template: 'How was your round today? Share your experience with us!',
    trigger: 'round_completion',
    status: 'draft',
    sentCount: 0,
    responseRate: 0,
    createdAt: '2024-01-10T00:00:00Z',
    courseId: 'course1'
  }
];

const ReviewsTab: React.FC = () => {
  const courseId = process.env.REACT_APP_COURSE_ID || '6793f989213768ac24c381e4';
  
  // Local state with mock data
  const [reviews, setReviews] = useState<Review[]>(mockReviews);
  const [campaigns, setCampaigns] = useState<ReviewCampaign[]>(mockCampaigns);
  const [googleConfig, setGoogleConfig] = useState<GoogleBusinessConfig>({
    placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4', // Mock Place ID
    apiKey: 'mock-api-key-12345',
    businessName: 'Albatros Golf Course',
    lastSync: new Date().toISOString(),
    autoSync: true
  });

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [responseModalOpen, setResponseModalOpen] = useState(false);
  const [responseText, setResponseText] = useState('');
  const [campaignModalOpen, setCampaignModalOpen] = useState(false);
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<ReviewCampaign | null>(null);
  const [campaignForm, setCampaignForm] = useState<CreateCampaignData>({
    name: '',
    type: 'email',
    template: '',
    trigger: 'round_completion'
  });

  // AI Response state
  const [aiResponseModalOpen, setAiResponseModalOpen] = useState(false);
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [aiResponses, setAiResponses] = useState<AIResponse[]>([]);

  // Loading states for UI feedback
  const [googleLoading, setGoogleLoading] = useState(false);
  const [campaignsLoading, setCampaignsLoading] = useState(false);

  // Filter states
  const [filterSource, setFilterSource] = useState<string>('all');
  const [filterRating, setFilterRating] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Mock functions
  const syncReviews = async () => {
    setGoogleLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Syncing Google reviews...');
      // In a real implementation, this would update the reviews state
    } catch (error) {
      console.error('Error syncing Google reviews:', error);
    } finally {
      setGoogleLoading(false);
    }
  };

  const updateGoogleConfig = (newConfig: Partial<GoogleBusinessConfig>) => {
    setGoogleConfig(prev => ({ ...prev, ...newConfig }));
  };

  const createCampaign = async (data: CreateCampaignData) => {
    setCampaignsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCampaign: ReviewCampaign = {
        id: `campaign-${Date.now()}`,
        name: data.name,
        type: data.type,
        template: data.template,
        trigger: data.trigger,
        status: 'active',
        sentCount: 0,
        responseRate: 0,
        createdAt: new Date().toISOString(),
        courseId
      };
      
      setCampaigns(prev => [...prev, newCampaign]);
      setCampaignModalOpen(false);
      setCampaignForm({
        name: '',
        type: 'email',
        template: '',
        trigger: 'round_completion'
      });
    } catch (error) {
      console.error('Error creating campaign:', error);
    } finally {
      setCampaignsLoading(false);
    }
  };

  const updateCampaign = async (id: string, data: Partial<ReviewCampaign>) => {
    setCampaigns(prev => prev.map(campaign => 
      campaign.id === id ? { ...campaign, ...data } : campaign
    ));
  };

  const deleteCampaign = async (id: string) => {
    setCampaigns(prev => prev.filter(campaign => campaign.id !== id));
  };

  const sendCampaign = async (id: string) => {
    setCampaignsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update campaign stats
      setCampaigns(prev => prev.map(campaign => 
        campaign.id === id 
          ? { 
              ...campaign, 
              sentCount: campaign.sentCount + Math.floor(Math.random() * 50) + 10,
              responseRate: campaign.responseRate + Math.random() * 5
            }
          : campaign
      ));
    } catch (error) {
      console.error('Error sending campaign:', error);
    } finally {
      setCampaignsLoading(false);
    }
  };

  const handleCreateCampaign = async () => {
    await createCampaign({
      ...campaignForm
    });
  };

  const handleEditCampaign = (campaign: ReviewCampaign) => {
    setEditingCampaign(campaign);
    setCampaignForm({
      name: campaign.name,
      type: campaign.type,
      template: campaign.template,
      trigger: campaign.trigger
    });
    setCampaignModalOpen(true);
  };

  const handleRespondToReview = () => {
    if (selectedReview && responseText.trim()) {
      setReviews(prev => prev.map(review => 
        review.id === selectedReview.id 
          ? { ...review, response: responseText }
          : review
      ));
      setResponseModalOpen(false);
      setResponseText('');
      setSelectedReview(null);
    }
  };

  const handleGenerateAIResponse = async (review: Review) => {
    setSelectedReview(review);
    setAiLoading(true);
    setAiResponseModalOpen(true);
    
    try {
      const response = await generateAIResponse(review);
      setAiResponse(response);
      setAiResponses(prev => [...prev, response]);
    } catch (error) {
      console.error('Error generating AI response:', error);
    } finally {
      setAiLoading(false);
    }
  };

  const handleUseAIResponse = () => {
    if (aiResponse) {
      setResponseText(aiResponse.response);
      setAiResponseModalOpen(false);
      setResponseModalOpen(true);
    }
  };

  const handleCopyAIResponse = () => {
    if (aiResponse) {
      navigator.clipboard.writeText(aiResponse.response);
    }
  };

  const handleRateAIResponse = (rating: 'good' | 'bad') => {
    if (aiResponse) {
      // In a real implementation, this would send feedback to improve the AI
      console.log(`AI response rated as ${rating} for review ${aiResponse.reviewId}`);
      setAiResponseModalOpen(false);
    }
  };

  const filteredReviews = reviews.filter(review => {
    if (filterSource !== 'all' && review.source !== filterSource) return false;
    if (filterRating !== 'all' && review.rating !== parseInt(filterRating)) return false;
    if (filterStatus !== 'all' && review.status !== filterStatus) return false;
    return true;
  });

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(r => r.rating === rating).length,
    percentage: (reviews.filter(r => r.rating === rating).length / reviews.length) * 100
  }));

  return (
    <Box>
      {/* Mock Data Notice */}
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This is using mock data. Backend integration will be available once the API is set up.
        </Typography>
      </Alert>

      {/* Header with stats */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">Reviews & Feedback</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={syncReviews}
              disabled={googleLoading}
            >
              {googleLoading ? <CircularProgress size={20} /> : 'Sync Google Reviews'}
            </Button>
            <Button
              variant="outlined"
              startIcon={<Settings />}
              onClick={() => setConfigModalOpen(true)}
            >
              Settings
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="primary">
                  {averageRating.toFixed(1)}
                </Typography>
                <Rating value={averageRating} readOnly precision={0.1} />
                <Typography variant="body2" color="text.secondary">
                  Average Rating ({reviews.length} reviews)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="success.main">
                  {reviews.filter(r => r.rating >= 4).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Positive Reviews (4-5 stars)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="warning.main">
                  {campaigns.reduce((sum, c) => sum + c.sentCount, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Review Requests Sent
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="info.main">
                  {campaigns.length > 0 
                    ? (campaigns.reduce((sum, c) => sum + c.responseRate, 0) / campaigns.length).toFixed(1)
                    : '0'
                  }%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average Response Rate
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Rating Distribution */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>Rating Distribution</Typography>
        <Grid container spacing={2}>
          {ratingDistribution.map(({ rating, count, percentage }) => (
            <Grid item xs={12} md={2.4} key={rating}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">{rating}★</Typography>
                <Box sx={{ flex: 1, bgcolor: 'grey.200', height: 8, borderRadius: 4 }}>
                  <Box 
                    sx={{ 
                      bgcolor: 'primary.main', 
                      height: '100%', 
                      borderRadius: 4,
                      width: `${percentage}%`
                    }} 
                  />
                </Box>
                <Typography variant="body2" sx={{ minWidth: 30 }}>
                  {count}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="All Reviews" />
          <Tab label="Review Campaigns" />
          <Tab label="Google Integration" />
        </Tabs>
      </Paper>

      {/* Reviews Tab */}
      {activeTab === 0 && (
        <Paper sx={{ p: 3 }}>
          {/* Filters */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Source</InputLabel>
              <Select
                value={filterSource}
                onChange={(e) => setFilterSource(e.target.value)}
                label="Source"
              >
                <MenuItem value="all">All Sources</MenuItem>
                <MenuItem value="google">Google</MenuItem>
                <MenuItem value="internal">Internal</MenuItem>
                <MenuItem value="email">Email</MenuItem>
                <MenuItem value="sms">SMS</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Rating</InputLabel>
              <Select
                value={filterRating}
                onChange={(e) => setFilterRating(e.target.value)}
                label="Rating"
              >
                <MenuItem value="all">All Ratings</MenuItem>
                <MenuItem value="5">5 Stars</MenuItem>
                <MenuItem value="4">4 Stars</MenuItem>
                <MenuItem value="3">3 Stars</MenuItem>
                <MenuItem value="2">2 Stars</MenuItem>
                <MenuItem value="1">1 Star</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="approved">Approved</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="rejected">Rejected</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Reviews List */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {filteredReviews.map((review) => (
              <Card key={review.id}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: review.source === 'google' ? '#4285f4' : 'primary.main' }}>
                        {review.source === 'google' ? <Google /> : review.author[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1" fontWeight={600}>
                          {review.author}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Rating value={review.rating} readOnly size="small" />
                          <Chip 
                            label={review.source} 
                            size="small" 
                            color={review.source === 'google' ? 'primary' : 'default'}
                          />
                          <Chip 
                            label={review.status} 
                            size="small" 
                            color={review.status === 'approved' ? 'success' : review.status === 'pending' ? 'warning' : 'error'}
                          />
                        </Box>
                      </Box>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(review.date).toLocaleDateString()}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {review.comment}
                  </Typography>

                  {review.response && (
                    <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1, mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">Response:</Typography>
                      <Typography variant="body2">{review.response}</Typography>
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        onClick={() => {
                          setSelectedReview(review);
                          setResponseModalOpen(true);
                        }}
                      >
                        Respond
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<SmartToy />}
                        onClick={() => handleGenerateAIResponse(review)}
                      >
                        AI Response
                      </Button>
                      {review.source === 'google' && (
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Google />}
                          href={`https://www.google.com/search?q=${encodeURIComponent(googleConfig.businessName)}`}
                          target="_blank"
                        >
                          View on Google
                        </Button>
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="caption">
                        {review.helpful} found helpful
                      </Typography>
                      {review.flagged && (
                        <Chip label="Flagged" size="small" color="error" />
                      )}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        </Paper>
      )}

      {/* Campaigns Tab */}
      {activeTab === 1 && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">Review Campaigns</Typography>
            <Button
              variant="contained"
              onClick={() => {
                setEditingCampaign(null);
                setCampaignForm({
                  name: '',
                  type: 'email',
                  template: '',
                  trigger: 'round_completion'
                });
                setCampaignModalOpen(true);
              }}
            >
              Create Campaign
            </Button>
          </Box>

          {campaignsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {campaigns.map((campaign) => (
                <Grid item xs={12} md={6} key={campaign.id}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">{campaign.name}</Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton size="small" onClick={() => handleEditCampaign(campaign)}>
                            <Edit />
                          </IconButton>
                          <IconButton size="small" onClick={() => deleteCampaign(campaign.id)}>
                            <Delete />
                          </IconButton>
                          <Chip 
                            label={campaign.status} 
                            color={campaign.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                        {campaign.type === 'email' ? <Email /> : campaign.type === 'sms' ? <Sms /> : <Email />}
                        <Typography variant="body2" color="text.secondary">
                          {campaign.type.toUpperCase()} • {campaign.trigger.replace('_', ' ')}
                        </Typography>
                      </Box>

                      <Typography variant="body2" sx={{ mb: 2 }}>
                        {campaign.template}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 3, mb: 2 }}>
                        <Typography variant="caption">
                          Sent: <b>{campaign.sentCount}</b>
                        </Typography>
                        <Typography variant="caption">
                          Response Rate: <b>{campaign.responseRate.toFixed(1)}%</b>
                        </Typography>
                      </Box>

                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Send />}
                        onClick={() => sendCampaign(campaign.id)}
                        disabled={campaign.status !== 'active'}
                      >
                        Send Now
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Paper>
      )}

      {/* Google Integration Tab */}
      {activeTab === 2 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 3 }}>Google Business Integration</Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Configuration</Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <TextField
                      label="Business Name"
                      value={googleConfig.businessName}
                      onChange={(e) => updateGoogleConfig({ businessName: e.target.value })}
                      fullWidth
                    />
                    <TextField
                      label="Google Place ID"
                      value={googleConfig.placeId}
                      onChange={(e) => updateGoogleConfig({ placeId: e.target.value })}
                      fullWidth
                      helperText="Find your Place ID in Google My Business"
                    />
                    <TextField
                      label="API Key"
                      value={googleConfig.apiKey}
                      onChange={(e) => updateGoogleConfig({ apiKey: e.target.value })}
                      fullWidth
                      type="password"
                      helperText="Google Business API key"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={googleConfig.autoSync}
                          onChange={(e) => updateGoogleConfig({ autoSync: e.target.checked })}
                        />
                      }
                      label="Auto-sync reviews"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Sync Status</Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Typography variant="body2">
                      Last Sync: {new Date(googleConfig.lastSync).toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      Auto Sync: {googleConfig.autoSync ? 'Enabled' : 'Disabled'}
                    </Typography>
                    <Typography variant="body2">
                      Google Reviews: {reviews.filter(r => r.source === 'google').length}
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<Refresh />}
                      onClick={syncReviews}
                      disabled={googleLoading}
                    >
                      {googleLoading ? 'Syncing...' : 'Manual Sync'}
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              <strong>Demo Mode:</strong> This is using mock data. To integrate with Google Business API:
              <br />• Set up a Google Cloud Project
              <br />• Enable the Google My Business API
              <br />• Create API credentials
              <br />• Add your Place ID and API key above
            </Typography>
          </Alert>
        </Paper>
      )}

      {/* Response Modal */}
      <Dialog open={responseModalOpen} onClose={() => setResponseModalOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Respond to Review</DialogTitle>
        <DialogContent>
          {selectedReview && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Review by {selectedReview.author} on {new Date(selectedReview.date).toLocaleDateString()}
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {selectedReview.comment}
              </Typography>
            </Box>
          )}
          <TextField
            label="Your Response"
            multiline
            rows={4}
            value={responseText}
            onChange={(e) => setResponseText(e.target.value)}
            fullWidth
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResponseModalOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleRespondToReview}>Send Response</Button>
        </DialogActions>
      </Dialog>

      {/* AI Response Modal */}
      <Dialog open={aiResponseModalOpen} onClose={() => setAiResponseModalOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SmartToy />
            AI-Generated Response
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedReview && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 1 }}>Original Review</Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Typography variant="subtitle2">{selectedReview.author}</Typography>
                  <Rating value={selectedReview.rating} readOnly size="small" />
                  <Chip label={selectedReview.source} size="small" />
                </Box>
                <Typography variant="body2">{selectedReview.comment}</Typography>
              </Paper>
            </Box>
          )}

          {aiLoading ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 4 }}>
              <CircularProgress sx={{ mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                AI is analyzing the review and generating a response...
              </Typography>
            </Box>
          ) : aiResponse ? (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Generated Response</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip 
                    label={aiResponse.sentiment} 
                    color={aiResponse.sentiment === 'positive' ? 'success' : aiResponse.sentiment === 'negative' ? 'error' : 'default'}
                    size="small"
                  />
                  <Chip 
                    label={`${Math.round(aiResponse.confidence * 100)}% confidence`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </Box>
              
              <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                <Typography variant="body1">{aiResponse.response}</Typography>
              </Paper>

              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Button
                  size="small"
                  startIcon={<ThumbUp />}
                  onClick={() => handleRateAIResponse('good')}
                  color="success"
                >
                  Good Response
                </Button>
                <Button
                  size="small"
                  startIcon={<ThumbDown />}
                  onClick={() => handleRateAIResponse('bad')}
                  color="error"
                >
                  Needs Improvement
                </Button>
              </Box>
            </Box>
          ) : null}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAiResponseModalOpen(false)}>Cancel</Button>
          <Button
            startIcon={<ContentCopy />}
            onClick={handleCopyAIResponse}
            disabled={!aiResponse}
          >
            Copy
          </Button>
          <Button
            variant="contained"
            onClick={handleUseAIResponse}
            disabled={!aiResponse}
          >
            Use This Response
          </Button>
        </DialogActions>
      </Dialog>

      {/* Campaign Modal */}
      <Dialog open={campaignModalOpen} onClose={() => setCampaignModalOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>{editingCampaign ? 'Edit Campaign' : 'Create Review Campaign'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 2 }}>
            <TextField 
              label="Campaign Name" 
              value={campaignForm.name}
              onChange={(e) => setCampaignForm(prev => ({ ...prev, name: e.target.value }))}
              fullWidth 
            />
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select 
                label="Type"
                value={campaignForm.type}
                onChange={(e) => setCampaignForm(prev => ({ ...prev, type: e.target.value as any }))}
              >
                <MenuItem value="email">Email</MenuItem>
                <MenuItem value="sms">SMS</MenuItem>
                <MenuItem value="in-app">In-App</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Trigger</InputLabel>
              <Select 
                label="Trigger"
                value={campaignForm.trigger}
                onChange={(e) => setCampaignForm(prev => ({ ...prev, trigger: e.target.value as any }))}
              >
                <MenuItem value="round_completion">Round Completion</MenuItem>
                <MenuItem value="manual">Manual</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Message Template"
              multiline
              rows={4}
              value={campaignForm.template}
              onChange={(e) => setCampaignForm(prev => ({ ...prev, template: e.target.value }))}
              fullWidth
              helperText="Use {courseName}, {golferName}, {date} as placeholders"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCampaignModalOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleCreateCampaign}>
            {editingCampaign ? 'Update Campaign' : 'Create Campaign'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReviewsTab; 