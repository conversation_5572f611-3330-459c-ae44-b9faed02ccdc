import React, { useState } from 'react';
import { Box, Typography, Select, MenuItem } from '@mui/material';
import { styled } from '@mui/material/styles';
import { MonthlyData, QuarterlyData, YearlyData } from '../../pages/types';

interface OverviewProps {
  data: MonthlyData | QuarterlyData | YearlyData;
  onTimeRangeChange: (range: string) => void;
  courseId: string;
}

const ChartContainer = styled(Box)(({ theme }) => ({
  background: '#FFFFFF',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
  height: '400px',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column'
}));

const TimeSelect = styled(Select)(({ theme }) => ({
  '& .MuiSelect-select': {
    paddingTop: theme.spacing(0.5),
    paddingBottom: theme.spacing(0.5),
    color: '#4CAF50',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
}));

interface BarChartProps {
  height: number;
  color: string;
  ishovered: string;
}

const BarChart = styled(Box)<BarChartProps>(({ height, color, ishovered }) => ({
  width: '40px',
  height: `${height}px`,
  backgroundColor: '#E0E7FF',
  borderRadius: '4px',
  transition: 'all 0.3s ease',
  cursor: 'default',
  '&:hover': {
    backgroundColor: '#4F46E5',
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
  }
}));

interface QuarterBoxProps {
  isHovered: boolean;
}

const QuarterBox = styled(Box)<QuarterBoxProps>(({ isHovered }) => ({
  width: '120px',
  height: '120px',
  backgroundColor: '#E0E7FF',
  borderRadius: '12px',
  transition: 'all 0.3s ease',
  cursor: 'default',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  '& .MuiTypography-root': {
    color: '#4F46E5',
  },
  '&:hover': {
    backgroundColor: '#4F46E5',
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
    '& .MuiTypography-root': {
      color: '#FFFFFF',
    }
  }
}));

interface YearlyBarProps {
  progress: number;
  isHovered: boolean;
}

const YearlyBar = styled(Box)<YearlyBarProps>(({ progress, isHovered }) => ({
  height: '24px',
  backgroundColor: '#E0E7FF',
  borderRadius: '12px',
  width: `${progress}%`,
  transition: 'all 0.3s ease',
  cursor: 'default',
  '&:hover': {
    backgroundColor: '#4F46E5',
    transform: 'scale(1.02)',
    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
  }
}));

const PercentageLabel = styled(Typography)(({ theme }) => ({
  position: 'absolute',
  transform: 'translateY(-120%)',
  color: '#1E293B',
  fontWeight: 'bold',
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  padding: '2px 6px',
  borderRadius: '4px',
  fontSize: '0.75rem'
}));

const Overview: React.FC<OverviewProps> = ({ courseId }) => {
  const [timeView, setTimeView] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  
  const monthlyData: MonthlyData[] = [
    { value: 45, percentage: '+2.3%' },   // Jan
    { value: 52, percentage: '+3.1%' },   // Feb
    { value: 75, percentage: '+15.4%' },  // Mar
    { value: 85, percentage: '+18.2%' },  // Apr
    { value: 92, percentage: '+22.5%' },  // May
    { value: 98, percentage: '+25.7%' },  // Jun
    { value: 100, percentage: '+27.8%' }, // Jul
    { value: 95, percentage: '+24.3%' },  // Aug
    { value: 82, percentage: '+16.8%' },  // Sep
    { value: 68, percentage: '+8.9%' },   // Oct
    { value: 55, percentage: '+4.2%' },   // Nov
    { value: 48, percentage: '+2.8%' }    // Dec
  ];

  const quarterlyData: QuarterlyData[] = [
    { quarter: 'Q1', value: 172, percentage: '+12.4%', revenue: '$172K' },
    { quarter: 'Q2', value: 275, percentage: '+24.7%', revenue: '$275K' },
    { quarter: 'Q3', value: 277, percentage: '+25.1%', revenue: '$277K' },
    { quarter: 'Q4', value: 171, percentage: '+11.8%', revenue: '$171K' }
  ];

  const yearlyData: YearlyData[] = [
    { year: '2021', progress: 65, revenue: '$650K', growth: '+8.5%' },
    { year: '2022', progress: 78, revenue: '$780K', growth: '+20.0%' },
    { year: '2023', progress: 85, revenue: '$850K', growth: '+9.0%' },
    { year: '2024', progress: 92, revenue: '$920K', growth: '+8.2%' }
  ];

  const renderMonthlyView = (): JSX.Element => (
    <Box
      display="flex"
      alignItems="flex-end"
      justifyContent="space-between"
      height="calc(100% - 80px)"
      mt={4}
      px={2}
      sx={{
        overflowX: 'auto',
        overflowY: 'hidden',
        pb: 2,
        position: 'relative',
        '& > div': {
          flex: '1 0 auto',
          maxWidth: '60px',
          mx: 1
        },
        minWidth: '100%',
        gap: 1,
        '&::-webkit-scrollbar': {
          height: '8px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#F1F5F9',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#CBD5E1',
          borderRadius: '4px',
          '&:hover': {
            background: '#94A3B8',
          },
        }
      }}
    >
      {monthlyData.map((item, index) => (
        <Box 
          key={index} 
          display="flex" 
          flexDirection="column" 
          alignItems="center"
          position="relative"
          onMouseEnter={() => setHoveredItem(index)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          <BarChart
            height={Math.min(item.value * 1.5, 200)}
            color='#6366F1'
            ishovered={(hoveredItem === index).toString()}
          />
          <Typography
            variant="caption"
            color="text.secondary"
            mt={1}
            sx={{ whiteSpace: 'nowrap' }}
          >
            {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][index]}
          </Typography>
          {hoveredItem === index && (
            <PercentageLabel>
              {item.percentage}
            </PercentageLabel>
          )}
        </Box>
      ))}
    </Box>
  );

  const renderQuarterlyView = (): JSX.Element => (
    <Box
      display="grid"
      gridTemplateColumns="repeat(auto-fit, minmax(140px, 1fr))"
      gap={2}
      height="calc(100% - 80px)"
      mt={2}
      pt={2}
      px={2}
      sx={{
        overflowY: 'auto',
        pb: 2,
        alignItems: 'start',
        justifyItems: 'center',
        position: 'relative',
        '&::-webkit-scrollbar': {
          width: '8px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#F1F5F9',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#CBD5E1',
          borderRadius: '4px',
          '&:hover': {
            background: '#94A3B8',
          },
        },
      }}
    >
      {quarterlyData.map((quarter, index) => (
        <Box
          key={index}
          position="relative"
          onMouseEnter={() => setHoveredItem(index)}
          onMouseLeave={() => setHoveredItem(null)}
          sx={{ 
            zIndex: hoveredItem === index ? 2 : 1,
            transform: hoveredItem === index ? 'translateY(-4px)' : 'none',
            transition: 'transform 0.3s ease'
          }}
        >
          <QuarterBox isHovered={hoveredItem === index}>
            <Typography
              variant="h4"
              fontWeight="bold"
              color="#FFFFFF"
              mb={1}
            >
              {quarter.quarter}
            </Typography>
            <Typography
              variant="body2"
              color="#FFFFFF"
              sx={{ opacity: 0.9 }}
            >
              {quarter.revenue}
            </Typography>
          </QuarterBox>
          {hoveredItem === index && (
            <PercentageLabel>
              {quarter.percentage}
            </PercentageLabel>
          )}
        </Box>
      ))}
    </Box>
  );

  const renderYearlyView = (): JSX.Element => (
    <Box 
      mt={4} 
      px={2} 
      height="calc(100% - 80px)"
      sx={{ 
        overflowY: 'auto',
        '&::-webkit-scrollbar': {
          width: '8px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#F1F5F9',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#CBD5E1',
          borderRadius: '4px',
          '&:hover': {
            background: '#94A3B8',
          },
        }
      }}
    >
      {yearlyData.map((year, index) => (
        <Box
          key={index}
          mb={3}
          position="relative"
          onMouseEnter={() => setHoveredItem(index)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="body2" color="text.primary">
              {year.year}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {year.revenue}
            </Typography>
          </Box>
          <Box width="100%" bgcolor="#F1F5F9" borderRadius="12px" position="relative">
            <YearlyBar
              progress={year.progress}
              isHovered={hoveredItem === index}
            />
            {hoveredItem === index && (
              <Typography
                variant="caption"
                sx={{
                  position: 'absolute',
                  left: `${Math.min(year.progress, 92)}%`,
                  top: '-20px',
                  color: '#1E293B',
                  fontWeight: 'bold',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap'
                }}
              >
                {year.growth}
              </Typography>
            )}
          </Box>
        </Box>
      ))}
    </Box>
  );

  return (
    <ChartContainer>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Overview
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Revenue {timeView === 'yearly' ? 'by Year' : timeView === 'quarterly' ? 'by Quarter' : 'by Month'}
          </Typography>
        </Box>
        <TimeSelect
          value={timeView}
          onChange={(e) => setTimeView(e.target.value as 'monthly' | 'quarterly' | 'yearly')}
        >
          <MenuItem value="monthly">Monthly</MenuItem>
          <MenuItem value="quarterly">Quarterly</MenuItem>
          <MenuItem value="yearly">Yearly</MenuItem>
        </TimeSelect>
      </Box>

      {timeView === 'monthly' && renderMonthlyView()}
      {timeView === 'quarterly' && renderQuarterlyView()}
      {timeView === 'yearly' && renderYearlyView()}
    </ChartContainer>
  );
};

export default Overview; 