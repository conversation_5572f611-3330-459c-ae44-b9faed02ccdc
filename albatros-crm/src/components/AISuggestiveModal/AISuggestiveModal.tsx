import React from 'react';
import {
  Box,
  Paper,
  IconButton,
  Tooltip,
  Stack,
  styled
} from '@mui/material';
import {
  PersonAdd as QuickAddIcon,
  RestaurantMenu as QuickFoodIcon,
  Message as QuickMessageIcon,
  ShoppingCart as QuickCheckoutIcon,
  Timer as QuickClockIcon,
  Help as HelpIcon,
} from '@mui/icons-material';

interface QuickAction {
  icon: React.ReactNode;
  tooltip: string;
  action: () => void;
}

interface AISuggestiveModalProps {
  open: boolean;
  onClose: () => void;
  onQuickAdd?: () => void;
  onQuickFood?: () => void;
  onQuickMessage?: () => void;
  onQuickCheckout?: () => void;
  onQuickClock?: () => void;
  onHelp?: () => void;
}

const ModalPaper = styled(Paper)(({ theme }) => ({
  position: 'fixed',
  right: 20,
  top: '50%',
  transform: 'translateY(-50%)',
  backgroundColor: 'rgba(245, 245, 245, 0.85)',
  padding: theme.spacing(1),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
  boxShadow: theme.shadows[3],
  zIndex: 1200,
}));

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: 'white',
  '&:hover': {
    backgroundColor: '#1ABC9C',
    '& svg': {
      transform: 'scale(1.2)',
      color: 'white',
    },
  },
  transition: 'all 0.3s ease',
}));

const IconContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  transition: 'transform 0.3s ease',
  '& svg': {
    fontSize: 24,
    color: '#2C3E50',
    transition: 'all 0.3s ease',
  },
});

const AISuggestiveModal: React.FC<AISuggestiveModalProps> = ({
  open,
  onClose,
  onQuickAdd,
  onQuickFood,
  onQuickMessage,
  onQuickCheckout,
  onQuickClock,
  onHelp,
}) => {
  const quickActions: QuickAction[] = [
    { icon: <QuickAddIcon />, tooltip: 'Quick Add Golfer', action: onQuickAdd || (() => console.log('Quick Add clicked')) },
    { icon: <QuickFoodIcon />, tooltip: 'Quick Food/Drink', action: onQuickFood || (() => console.log('Quick Food clicked')) },
    { icon: <QuickMessageIcon />, tooltip: 'Quick Message', action: onQuickMessage || (() => console.log('Quick Message clicked')) },
    { icon: <QuickCheckoutIcon />, tooltip: 'Quick Check-Out', action: onQuickCheckout || (() => console.log('Quick Check-Out clicked')) },
    { icon: <QuickClockIcon />, tooltip: 'Quick Clock-In/Out', action: onQuickClock || (() => console.log('Quick Clock clicked')) },
    { icon: <HelpIcon />, tooltip: 'Help', action: onHelp || (() => console.log('Help clicked')) },
  ];

  return (
    <ModalPaper>
      <Stack spacing={2}>
        {quickActions.map((action, index) => (
          <Tooltip
            key={index}
            title={action.tooltip}
            placement="left"
            arrow
          >
            <StyledIconButton onClick={action.action}>
              <IconContainer>
                {action.icon}
              </IconContainer>
            </StyledIconButton>
          </Tooltip>
        ))}
      </Stack>
    </ModalPaper>
  );
};

export default AISuggestiveModal; 