import React from 'react';
import { Typography, IconButton, styled } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { SetupSection, ContentCard, FlexColumn, FlexRow, ActionButton, StyledTextField } from './styles';
import AddIcon from '@mui/icons-material/Add';

const ClosureItem = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.default,
  transition: theme.transitions.create(['background-color']),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const DateDisplay = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  color: theme.palette.text.primary,
  minWidth: 120,
}));

const ReasonDisplay = styled(Typography)(({ theme }) => ({
  flex: 1,
  color: theme.palette.text.secondary,
}));

interface CourseClosuresProps {
  closures: Array<{ id: number; date: string; reason: string }>;
  onAddClosure: (closure: { date: string; reason: string }) => void;
  onDeleteClosure: (id: number) => void;
}

export const CourseClosures: React.FC<CourseClosuresProps> = ({
  closures,
  onAddClosure,
  onDeleteClosure,
}) => {
  const [newDate, setNewDate] = React.useState('');
  const [newReason, setNewReason] = React.useState('');

  const handleAdd = () => {
    if (newDate && newReason) {
      onAddClosure({ date: newDate, reason: newReason });
      setNewDate('');
      setNewReason('');
    }
  };

  return (
    <SetupSection>
      <ContentCard>
        <FlexColumn>
          <FlexRow>
            <StyledTextField
              type="date"
              label="Date"
              value={newDate}
              onChange={(e) => setNewDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
            <StyledTextField
              label="Reason"
              value={newReason}
              onChange={(e) => setNewReason(e.target.value)}
              placeholder="Enter closure reason"
              fullWidth
            />
            <ActionButton
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              disabled={!newDate || !newReason}
            >
              Add
            </ActionButton>
          </FlexRow>

          <FlexColumn>
            {closures.map((closure) => (
              <ClosureItem key={closure.id}>
                <DateDisplay>
                  {new Date(closure.date).toLocaleDateString()}
                </DateDisplay>
                <ReasonDisplay>{closure.reason}</ReasonDisplay>
                <IconButton
                  color="error"
                  onClick={() => onDeleteClosure(closure.id)}
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </ClosureItem>
            ))}
          </FlexColumn>
        </FlexColumn>
      </ContentCard>
    </SetupSection>
  );
}; 