// From OperatingHours.tsx
export enum WeekDay {
  Monday = 'Monday',
  Tuesday = 'Tuesday',
  Wednesday = 'Wednesday',
  Thursday = 'Thursday',
  Friday = 'Friday',
  Saturday = 'Saturday',
  Sunday = 'Sunday',
  EveryDay = 'Every Day'
}

export type TimeField = 'open' | 'close';

export interface DayHours {
  open: string;
  close: string;
}

export type OperatingHoursData = {
  [K in WeekDay]: DayHours;
};

// From ConditionalRules.tsx
export type ConditionType = 'dayOfWeek' | 'groupSize' | 'timeOfDay';
export type EffectType = 'delayOpening' | 'addTimeInterval' | 'restrictGroups';
export type TextFieldEvent = React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>;

export interface ConditionalRule {
  id: number;
  title: string;
  condition: {
    type: ConditionType;
    value: string;
  };
  effect: {
    type: EffectType;
    value: string;
  };
  timeline: {
    start: string;
    end: string;
  };
}

// From TeeTimeCosts.tsx
export type DayOfWeek = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
export type TimeSlot = 'morning' | 'afternoon' | 'evening';
export type DayRates = Record<TimeSlot, number>;
export type TeeTimeCostsData = Record<DayOfWeek, DayRates>;

// Component Props
export interface OperatingHoursProps {
  operatingHours: OperatingHoursData;
  onTimeChange: (day: WeekDay, field: TimeField, value: string) => void;
}

export interface TeeTimeCostsProps {
  costs: TeeTimeCostsData;
  onCostChange: (day: DayOfWeek, timeOfDay: TimeSlot, value: number) => void;
} 