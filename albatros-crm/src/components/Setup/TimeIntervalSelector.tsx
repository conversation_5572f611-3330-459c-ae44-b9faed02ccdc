import React from 'react';
import { Radio, RadioGroup, FormControlLabel, styled } from '@mui/material';
import { SetupSection, ContentCard, StyledTextField, FlexRow } from './styles';

const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  margin: 0,
  '& .MuiRadio-root': {
    padding: theme.spacing(1),
  },
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
    fontWeight: 500,
    padding: theme.spacing(0.5, 1),
    borderRadius: theme.shape.borderRadius,
    transition: theme.transitions.create(['background-color', 'color']),
  },
  '&:hover .MuiTypography-root': {
    backgroundColor: theme.palette.action.hover,
  },
  '& .Mui-checked + .MuiTypography-root': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  }
}));

interface TimeIntervalSelectorProps {
  value: string;
  onChange: (value: string) => void;
  customValue: string;
  onCustomChange: (value: string) => void;
}

export const TimeIntervalSelector: React.FC<TimeIntervalSelectorProps> = ({ 
  value, 
  onChange, 
  customValue, 
  onCustomChange 
}) => {
  return (
    <SetupSection>
      <ContentCard>
        <RadioGroup
          row
          value={value}
          onChange={(e) => onChange(e.target.value)}
        >
          <FlexRow sx={{ flexWrap: 'wrap' }}>
            <StyledFormControlLabel value="5" control={<Radio />} label="5 min" />
            <StyledFormControlLabel value="7" control={<Radio />} label="7 min" />
            <StyledFormControlLabel value="10" control={<Radio />} label="10 min" />
            <StyledFormControlLabel value="12" control={<Radio />} label="12 min" />
            <StyledFormControlLabel value="15" control={<Radio />} label="15 min" />
            <FlexRow>
              <StyledFormControlLabel value="custom" control={<Radio />} label="Custom:" />
              <StyledTextField
                size="small"
                disabled={value !== 'custom'}
                value={customValue}
                onChange={(e) => onCustomChange(e.target.value)}
                sx={{ width: 80 }}
                InputProps={{
                  endAdornment: <span style={{ fontSize: '0.875rem' }}>min</span>
                }}
              />
            </FlexRow>
          </FlexRow>
        </RadioGroup>
      </ContentCard>
    </SetupSection>
  );
}; 