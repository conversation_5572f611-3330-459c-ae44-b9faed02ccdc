import { styled } from '@mui/material';
import React from 'react';
import { ContentCard, SetupSection, StyledTextField } from './styles';
import { OperatingHoursProps, WeekDay } from './types';

// Constants
const DAYS: WeekDay[] = [
  WeekDay.Monday,
  WeekDay.Tuesday,
  WeekDay.Wednesday,
  WeekDay.Thursday,
  WeekDay.Friday,
  WeekDay.Saturday,
  WeekDay.Sunday,
  WeekDay.EveryDay
];

// Styled Components
const Table = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

const TableHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

const TableRow = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create(['background-color']),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const Cell = styled('div')(({ theme }) => ({
  flex: 1,
  minWidth: 120,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const DayCell = styled(Cell)(({ theme }) => ({
  minWidth: 100,
  width: 100,
  justifyContent: 'flex-start',
}));

const HeaderCell = styled(Cell)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(1),
}));

const TimeInput = styled(StyledTextField)(({ theme }) => ({
  width: '100%',
  '& .MuiOutlinedInput-root': {
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
}));

export const OperatingHours: React.FC<OperatingHoursProps> = ({
  operatingHours,
  onTimeChange
}) => {
  return (
    <SetupSection>
      <ContentCard>
        <Table>
          <TableHeader>
            <DayCell>Day</DayCell>
            <HeaderCell>Open</HeaderCell>
            <HeaderCell>Close</HeaderCell>
          </TableHeader>

          {DAYS.map((day) => (
            <TableRow key={day}>
              <DayCell>{day}</DayCell>
              <Cell>
                <TimeInput
                  type="time"
                  value={operatingHours[day].open}
                  onChange={(e) => onTimeChange(day, 'open', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Cell>
              <Cell>
                <TimeInput
                  type="time"
                  value={operatingHours[day].close}
                  onChange={(e) => onTimeChange(day, 'close', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Cell>
            </TableRow>
          ))}
        </Table>
      </ContentCard>
    </SetupSection>
  );
}; 