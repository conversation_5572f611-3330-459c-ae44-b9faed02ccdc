import React from 'react';
import { Typography, IconButton, styled } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { SetupSection, ContentCard, FlexColumn, FlexRow, ActionButton, StyledTextField } from './styles';
import AddIcon from '@mui/icons-material/Add';

const RuleItem = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.default,
  transition: theme.transitions.create(['background-color']),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const RuleHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

const RuleTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  flex: 1,
}));

const RuleDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.95rem',
  lineHeight: 1.5,
}));

interface CourseRulesProps {
  rules: Array<{ id: number; title: string; description: string }>;
  onAddRule: (rule: { title: string; description: string }) => void;
  onDeleteRule: (id: number) => void;
}

export const CourseRules: React.FC<CourseRulesProps> = ({
  rules,
  onAddRule,
  onDeleteRule,
}) => {
  const [newTitle, setNewTitle] = React.useState('');
  const [newDescription, setNewDescription] = React.useState('');

  const handleAdd = () => {
    if (newTitle && newDescription) {
      onAddRule({ title: newTitle, description: newDescription });
      setNewTitle('');
      setNewDescription('');
    }
  };

  return (
    <SetupSection>
      <ContentCard>
        <FlexColumn>
          <FlexColumn>
            <StyledTextField
              label="Rule Title"
              value={newTitle}
              onChange={(e) => setNewTitle(e.target.value)}
              placeholder="Enter rule title"
              fullWidth
            />
            <StyledTextField
              label="Description"
              value={newDescription}
              onChange={(e) => setNewDescription(e.target.value)}
              placeholder="Enter rule description"
              multiline
              rows={3}
              fullWidth
            />
            <FlexRow sx={{ justifyContent: 'flex-end' }}>
              <ActionButton
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAdd}
                disabled={!newTitle || !newDescription}
              >
                Add Rule
              </ActionButton>
            </FlexRow>
          </FlexColumn>

          <FlexColumn>
            {rules.map((rule) => (
              <RuleItem key={rule.id}>
                <RuleHeader>
                  <RuleTitle>{rule.title}</RuleTitle>
                  <IconButton
                    color="error"
                    onClick={() => onDeleteRule(rule.id)}
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </RuleHeader>
                <RuleDescription>{rule.description}</RuleDescription>
              </RuleItem>
            ))}
          </FlexColumn>
        </FlexColumn>
      </ContentCard>
    </SetupSection>
  );
}; 