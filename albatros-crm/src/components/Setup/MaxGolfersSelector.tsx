import React from 'react';
import { Slider, Typography, styled } from '@mui/material';
import { SetupSection, ContentCard, FlexColumn } from './styles';
import GroupsIcon from '@mui/icons-material/Groups';

const StyledSlider = styled(Slider)(({ theme }) => ({
  '& .MuiSlider-thumb': {
    height: 28,
    width: 28,
    backgroundColor: theme.palette.background.paper,
    border: `2px solid ${theme.palette.primary.main}`,
    '&:hover': {
      boxShadow: `0 0 0 8px ${theme.palette.primary.main}20`,
    },
    '&:before': {
      display: 'none',
    },
  },
  '& .MuiSlider-track': {
    height: 8,
    borderRadius: 4,
  },
  '& .MuiSlider-rail': {
    height: 8,
    borderRadius: 4,
    opacity: 0.2,
  },
  '& .MuiSlider-valueLabel': {
    fontSize: 14,
    fontWeight: 'bold',
    top: -6,
    backgroundColor: theme.palette.primary.main,
    '&:before': { display: 'none' },
    '& *': {
      background: 'transparent',
    },
  },
  '& .MuiSlider-mark': {
    backgroundColor: theme.palette.primary.main,
    height: 12,
    width: 2,
    marginTop: -2,
    '&.MuiSlider-markActive': {
      opacity: 1,
      backgroundColor: theme.palette.primary.main,
    },
  },
}));

const ValueDisplay = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(4),
  '& .MuiTypography-h4': {
    fontWeight: 600,
    color: theme.palette.primary.main,
  },
  '& .MuiSvgIcon-root': {
    fontSize: '2rem',
    color: theme.palette.primary.main,
  },
}));

interface MaxGolfersSelectorProps {
  value: number;
  onChange: (value: number) => void;
}

export const MaxGolfersSelector: React.FC<MaxGolfersSelectorProps> = ({
  value,
  onChange,
}) => {
  const marks = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
  ];

  return (
    <SetupSection>
      <ContentCard>
        <FlexColumn>
          <ValueDisplay>
            <GroupsIcon />
            <Typography variant="h4">{value}</Typography>
            <Typography variant="body1" color="text.secondary">
              golfers per group
            </Typography>
          </ValueDisplay>
          
          <StyledSlider
            value={value}
            onChange={(_, newValue) => onChange(newValue as number)}
            step={1}
            marks={marks}
            min={1}
            max={4}
            valueLabelDisplay="auto"
          />
        </FlexColumn>
      </ContentCard>
    </SetupSection>
  );
}; 