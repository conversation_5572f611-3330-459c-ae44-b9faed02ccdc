import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import { Typography, styled } from '@mui/material';
import React from 'react';
import { ContentCard, SetupSection, StyledTextField } from './styles';
import { DayOfWeek, TeeTimeCostsProps, TimeSlot } from './types';

// Constants
const DAYS: DayOfWeek[] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const TIME_SLOTS: TimeSlot[] = ['morning', 'afternoon', 'evening'];

// Styled Components
const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.text.primary,
  fontWeight: 600,
  fontSize: '1.25rem',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  '& svg': {
    fontSize: '1.5rem',
    color: theme.palette.primary.main
  }
}));

const Table = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

const TableHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

const TableRow = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create(['background-color']),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const Cell = styled('div')(({ theme }) => ({
  flex: 1,
  minWidth: 120,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const DayCell = styled(Cell)(({ theme }) => ({
  minWidth: 100,
  width: 100,
  justifyContent: 'flex-start',
}));

const HeaderCell = styled(Cell)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(1),
}));

const CostInput = styled(StyledTextField)(({ theme }) => ({
  width: '100%',
  '& .MuiOutlinedInput-root': {
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
  '& .MuiInputAdornment-root': {
    color: theme.palette.text.secondary,
  },
}));

// Components
const TimeSlotHeader: React.FC<{ slot: TimeSlot }> = ({ slot }) => (
  <HeaderCell>
    {slot.charAt(0).toUpperCase() + slot.slice(1)}
  </HeaderCell>
);

const CostField: React.FC<{
  value: number;
  onChange: (value: number) => void;
}> = ({ value, onChange }) => (
  <CostInput
    type="number"
    value={value}
    onChange={(e) => onChange(parseFloat(e.target.value))}
    InputProps={{
      startAdornment: <AttachMoneyIcon sx={{ fontSize: 20, mr: 0.5 }} />,
    }}
  />
);

export const TeeTimeCosts: React.FC<TeeTimeCostsProps> = ({
  costs,
  onCostChange,
}) => {
  return (
    <SetupSection>
      <SectionTitle>
        <AttachMoneyIcon />
        Tee Time Costs
      </SectionTitle>
      <ContentCard>
        <Table>
          <TableHeader>
            <DayCell>Day</DayCell>
            {TIME_SLOTS.map((slot) => (
              <TimeSlotHeader key={slot} slot={slot} />
            ))}
          </TableHeader>

          {DAYS.map((day) => (
            <TableRow key={day}>
              <DayCell>{day}</DayCell>
              {TIME_SLOTS.map((slot) => (
                <Cell key={`${day}-${slot}`}>
                  <CostField
                    value={costs[day][slot]}
                    onChange={(value) => onCostChange(day, slot, value)}
                  />
                </Cell>
              ))}
            </TableRow>
          ))}
        </Table>
      </ContentCard>
    </SetupSection>
  );
}; 