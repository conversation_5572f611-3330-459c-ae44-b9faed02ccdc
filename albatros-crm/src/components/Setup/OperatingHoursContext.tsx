import React, { createContext, useContext, useState } from 'react';
import { OperatingHoursData, WeekDay } from './types';

const defaultOperatingHours: OperatingHoursData = {
  [WeekDay.Monday]: { open: '06:00', close: '18:00' },
  [WeekDay.Tuesday]: { open: '06:00', close: '18:00' },
  [WeekDay.Wednesday]: { open: '06:00', close: '18:00' },
  [WeekDay.Thursday]: { open: '06:00', close: '18:00' },
  [WeekDay.Friday]: { open: '06:00', close: '18:00' },
  [WeekDay.Saturday]: { open: '06:00', close: '18:00' },
  [WeekDay.Sunday]: { open: '06:00', close: '18:00' },
  [WeekDay.EveryDay]: { open: '06:00', close: '18:00' }
};

interface OperatingHoursContextType {
  operatingHours: OperatingHoursData;
  setOperatingHours: React.Dispatch<React.SetStateAction<OperatingHoursData>>;
}

const OperatingHoursContext = createContext<OperatingHoursContextType | undefined>(undefined);

export const OperatingHoursProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [operatingHours, setOperatingHours] = useState<OperatingHoursData>(defaultOperatingHours);
  return (
    <OperatingHoursContext.Provider value={{ operatingHours, setOperatingHours }}>
      {children}
    </OperatingHoursContext.Provider>
  );
};

export const useOperatingHours = () => {
  const ctx = useContext(OperatingHoursContext);
  if (!ctx) throw new Error('useOperatingHours must be used within OperatingHoursProvider');
  return ctx;
}; 