import React from 'react';
import { 
  Select, 
  MenuItem, 
  TextField,
  IconButton,
  Typography,
  styled,
  SelectChangeEvent,
  Button
} from '@mui/material';
import { SetupSection, ContentCard, FlexRow } from './styles';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { WeekDay, ConditionType, EffectType, TextFieldEvent, ConditionalRule } from './types';

// Styled Components
const RuleContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

const RuleCard = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  position: 'relative',
  border: `1px solid ${theme.palette.divider}`,
  transition: theme.transitions.create(['background-color', 'box-shadow', 'transform'], {
    duration: theme.transitions.duration.standard,
  }),
  '&:hover': {
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[2],
    transform: 'translateY(-2px)',
  },
}));

const RuleTitle = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    fontSize: '1.25rem',
    fontWeight: 600,
  },
  '& .MuiInputBase-input': {
    padding: theme.spacing(1),
  },
}));

const RuleRow = styled(FlexRow)(({ theme }) => ({
  gap: theme.spacing(2),
  alignItems: 'center',
  flexWrap: 'wrap',
}));

const DeleteButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  color: theme.palette.error.main,
}));

const AddRuleButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(2),
  alignSelf: 'flex-start',
  backgroundColor: theme.palette.primary.main,
  color: '#FFFFFF',
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
  },
}));

interface ConditionalRulesProps {
  rules: ConditionalRule[];
  onAddRule: () => void;
  onUpdateRule: (rule: ConditionalRule) => void;
  onDeleteRule: (id: number) => void;
}

export const ConditionalRules: React.FC<ConditionalRulesProps> = ({
  rules,
  onAddRule,
  onUpdateRule,
  onDeleteRule,
}) => {
  const handleTitleChange = (ruleId: number, event: TextFieldEvent) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        title: event.target.value,
      });
    }
  };

  const handleConditionTypeChange = (ruleId: number, event: SelectChangeEvent<ConditionType>) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        condition: {
          ...rule.condition,
          type: event.target.value as ConditionType,
          value: '',
        },
      });
    }
  };

  const handleConditionSelectChange = (ruleId: number, event: SelectChangeEvent<string>) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        condition: {
          ...rule.condition,
          value: event.target.value,
        },
      });
    }
  };

  const handleConditionInputChange = (ruleId: number, event: TextFieldEvent) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        condition: {
          ...rule.condition,
          value: event.target.value,
        },
      });
    }
  };

  const handleEffectTypeChange = (ruleId: number, event: SelectChangeEvent<EffectType>) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        effect: {
          ...rule.effect,
          type: event.target.value as EffectType,
          value: '',
        },
      });
    }
  };

  const handleEffectInputChange = (ruleId: number, event: TextFieldEvent) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        effect: {
          ...rule.effect,
          value: event.target.value,
        },
      });
    }
  };

  const handleTimelineChange = (ruleId: number, field: 'start' | 'end', event: TextFieldEvent) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      onUpdateRule({
        ...rule,
        timeline: {
          ...rule.timeline,
          [field]: event.target.value,
        },
      });
    }
  };

  const weekDays = Object.values(WeekDay);

  return (
    <SetupSection>
      <ContentCard>
        <RuleContainer>
          {rules.map((rule) => (
            <RuleCard key={rule.id}>
              <RuleTitle
                value={rule.title}
                onChange={(e) => handleTitleChange(rule.id, e)}
                variant="standard"
                fullWidth
                placeholder="Rule Title"
              />
              <DeleteButton
                size="small"
                onClick={() => onDeleteRule(rule.id)}
              >
                <DeleteIcon />
              </DeleteButton>

              <RuleRow>
                <Typography>If:</Typography>
                <Select<ConditionType>
                  value={rule.condition.type}
                  onChange={(e) => handleConditionTypeChange(rule.id, e)}
                  size="small"
                  sx={{ minWidth: 200 }}
                >
                  <MenuItem value="dayOfWeek">Day of Week</MenuItem>
                  <MenuItem value="groupSize">Group Size</MenuItem>
                  <MenuItem value="timeOfDay">Time of Day</MenuItem>
                </Select>

                {rule.condition.type === 'dayOfWeek' && (
                  <Select<string>
                    value={rule.condition.value}
                    onChange={(e) => handleConditionSelectChange(rule.id, e)}
                    size="small"
                    sx={{ minWidth: 200 }}
                  >
                    {weekDays.map((day) => (
                      <MenuItem key={day} value={day}>{day}</MenuItem>
                    ))}
                  </Select>
                )}

                {rule.condition.type === 'groupSize' && (
                  <TextField
                    type="number"
                    size="small"
                    value={rule.condition.value}
                    onChange={(e) => handleConditionInputChange(rule.id, e)}
                  />
                )}

                {rule.condition.type === 'timeOfDay' && (
                  <TextField
                    type="time"
                    size="small"
                    value={rule.condition.value}
                    onChange={(e) => handleConditionInputChange(rule.id, e)}
                    InputLabelProps={{ shrink: true }}
                  />
                )}
              </RuleRow>

              <RuleRow>
                <Typography>Then:</Typography>
                <Select<EffectType>
                  value={rule.effect.type}
                  onChange={(e) => handleEffectTypeChange(rule.id, e)}
                  size="small"
                  sx={{ minWidth: 200 }}
                >
                  <MenuItem value="delayOpening">Delay Opening Till</MenuItem>
                  <MenuItem value="addTimeInterval">Add Time Interval</MenuItem>
                  <MenuItem value="restrictGroups">Restrict Group Size To</MenuItem>
                </Select>

                {rule.effect.type === 'delayOpening' && (
                  <TextField
                    type="time"
                    size="small"
                    value={rule.effect.value}
                    onChange={(e) => handleEffectInputChange(rule.id, e)}
                    InputLabelProps={{ shrink: true }}
                  />
                )}

                {rule.effect.type === 'addTimeInterval' && (
                  <TextField
                    type="number"
                    size="small"
                    value={rule.effect.value}
                    onChange={(e) => handleEffectInputChange(rule.id, e)}
                    InputProps={{ endAdornment: 'minutes' }}
                  />
                )}

                {rule.effect.type === 'restrictGroups' && (
                  <TextField
                    type="number"
                    size="small"
                    value={rule.effect.value}
                    onChange={(e) => handleEffectInputChange(rule.id, e)}
                  />
                )}
              </RuleRow>

              <RuleRow>
                <Typography>Timeline:</Typography>
                <TextField
                  type="date"
                  size="small"
                  value={rule.timeline.start}
                  onChange={(e) => handleTimelineChange(rule.id, 'start', e)}
                  InputLabelProps={{ shrink: true }}
                />
                <Typography>to</Typography>
                <TextField
                  type="date"
                  size="small"
                  value={rule.timeline.end}
                  onChange={(e) => handleTimelineChange(rule.id, 'end', e)}
                  InputLabelProps={{ shrink: true }}
                />
              </RuleRow>
            </RuleCard>
          ))}

          <AddRuleButton
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={onAddRule}
          >
            Add New Rule
          </AddRuleButton>
        </RuleContainer>
      </ContentCard>
    </SetupSection>
  );
}; 