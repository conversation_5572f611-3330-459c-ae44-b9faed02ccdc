import React, { createContext, useContext, useState } from 'react';
import { OperatingHoursData, WeekDay, TeeTimeCostsData, ConditionalRule } from './types';

const defaultOperatingHours: OperatingHoursData = {
  [WeekDay.Monday]: { open: '06:00', close: '18:00' },
  [WeekDay.Tuesday]: { open: '06:00', close: '18:00' },
  [WeekDay.Wednesday]: { open: '06:00', close: '18:00' },
  [WeekDay.Thursday]: { open: '06:00', close: '18:00' },
  [WeekDay.Friday]: { open: '06:00', close: '18:00' },
  [WeekDay.Saturday]: { open: '06:00', close: '18:00' },
  [WeekDay.Sunday]: { open: '06:00', close: '18:00' },
  [WeekDay.EveryDay]: { open: '06:00', close: '18:00' }
};

const defaultTeeTimeCosts: TeeTimeCostsData = {
  Monday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
  Tuesday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
  Wednesday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
  Thursday: { morning: 45.00, afternoon: 55.00, evening: 35.00 },
  Friday: { morning: 65.00, afternoon: 75.00, evening: 45.00 },
  Saturday: { morning: 75.00, afternoon: 85.00, evening: 55.00 },
  Sunday: { morning: 75.00, afternoon: 85.00, evening: 55.00 }
};

const defaultClosures: CourseClosure[] = [
  { id: 1, date: '2024-12-25', reason: 'Christmas Day' },
  { id: 2, date: '2024-12-26', reason: 'Boxing Day' },
  { id: 3, date: '2025-01-01', reason: 'New Year\'s Day' },
  { id: 4, date: '2025-01-20', reason: 'Martin Luther King Jr. Day' }
];

const defaultConditionalRules: ConditionalRule[] = [
  {
    id: 1,
    title: 'Water Delay - Tuesday',
    condition: {
      type: 'dayOfWeek',
      value: 'Tuesday'
    },
    effect: {
      type: 'delayOpening',
      value: '09:00'
    },
    timeline: {
      start: new Date().toISOString().split('T')[0],
      end: '2025-09-30'
    }
  },
  {
    id: 2,
    title: 'Extended Intervals - Weekends',
    condition: {
      type: 'dayOfWeek',
      value: 'Every Day'
    },
    effect: {
      type: 'addTimeInterval',
      value: '5'
    },
    timeline: {
      start: new Date().toISOString().split('T')[0],
      end: '2025-09-30'
    }
  }
];

export interface CourseClosure {
  id: number;
  date: string;
  reason: string;
}

interface CourseConfigContextType {
  operatingHours: OperatingHoursData;
  setOperatingHours: React.Dispatch<React.SetStateAction<OperatingHoursData>>;
  maxGolfers: number;
  setMaxGolfers: React.Dispatch<React.SetStateAction<number>>;
  teeTimeInterval: string;
  setTeeTimeInterval: React.Dispatch<React.SetStateAction<string>>;
  teeTimeCosts: TeeTimeCostsData;
  setTeeTimeCosts: React.Dispatch<React.SetStateAction<TeeTimeCostsData>>;
  closures: CourseClosure[];
  setClosures: React.Dispatch<React.SetStateAction<CourseClosure[]>>;
  conditionalRules: ConditionalRule[];
  setConditionalRules: React.Dispatch<React.SetStateAction<ConditionalRule[]>>;
}

const CourseConfigContext = createContext<CourseConfigContextType | undefined>(undefined);

export const CourseConfigProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [operatingHours, setOperatingHours] = useState<OperatingHoursData>(defaultOperatingHours);
  const [maxGolfers, setMaxGolfers] = useState<number>(4);
  const [teeTimeInterval, setTeeTimeInterval] = useState<string>('15');
  const [teeTimeCosts, setTeeTimeCosts] = useState<TeeTimeCostsData>(defaultTeeTimeCosts);
  const [closures, setClosures] = useState<CourseClosure[]>(defaultClosures);
  const [conditionalRules, setConditionalRules] = useState<ConditionalRule[]>(defaultConditionalRules);

  return (
    <CourseConfigContext.Provider value={{
      operatingHours, setOperatingHours,
      maxGolfers, setMaxGolfers,
      teeTimeInterval, setTeeTimeInterval,
      teeTimeCosts, setTeeTimeCosts,
      closures, setClosures,
      conditionalRules, setConditionalRules
    }}>
      {children}
    </CourseConfigContext.Provider>
  );
};

export const useCourseConfig = () => {
  const ctx = useContext(CourseConfigContext);
  if (!ctx) throw new Error('useCourseConfig must be used within CourseConfigProvider');
  return ctx;
}; 