import { TimeSlot } from '../types';

/**
 * Props for the TimeSlotCard component
 */
export interface TimeSlotCardProps {
  /** The date to display in the header */
  date: string;
  /** Array of time slots to display */
  slots: TimeSlot[];
  /** Optional callback when a slot is clicked */
  onSlotClick?: (slot: TimeSlot) => void;
  /** Optional className for additional styling */
  className?: string;
  /** Optional flag to disable hover effects */
  disableHover?: boolean;
  /** Optional flag to show utilization bar */
  showUtilizationBar?: boolean;
}

/**
 * Props for the TimeSlotItem component
 */
export interface TimeSlotItemProps {
  /** The time slot data */
  slot: TimeSlot;
  /** Optional callback when clicked */
  onClick?: () => void;
  /** Optional className for additional styling */
  className?: string;
  /** Optional flag to disable hover effects */
  disableHover?: boolean;
  /** Optional flag to show utilization bar */
  showUtilizationBar?: boolean;
} 