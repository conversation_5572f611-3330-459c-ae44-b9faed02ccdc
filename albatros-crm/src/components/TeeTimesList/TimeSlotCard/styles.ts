import { styled } from '@mui/material/styles';
import { Paper, Box, Typography } from '@mui/material';

export const TimeSlotCardContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  transition: theme.transitions.create(['box-shadow'], {
    duration: theme.transitions.duration.shorter,
  }),
  '&:hover': {
    boxShadow: theme.shadows[2],
  },
}));

export const TimeSlotHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  paddingBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

export const TimeSlotContent = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
  gap: theme.spacing(2),
}));

export const TimeSlotItem = styled(Paper, {
  shouldForwardProp: (prop) => !['isPeak', 'disableHover'].includes(prop as string),
})<{ isPeak?: boolean; disableHover?: boolean }>(({ theme, isPeak, disableHover = false }) => ({
  padding: theme.spacing(2),
  cursor: disableHover ? 'default' : 'pointer',
  backgroundColor: isPeak 
    ? theme.palette.warning.light 
    : theme.palette.background.paper,
  transition: theme.transitions.create(['background-color', 'transform', 'box-shadow'], {
    duration: theme.transitions.duration.shorter,
  }),
  ...(!disableHover && {
    '&:hover': {
      backgroundColor: isPeak 
        ? theme.palette.warning.main 
        : theme.palette.action.hover,
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[2],
    },
  }),
}));

export const TimeSlotItemHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
}));

export const TimeSlotItemContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
}));

export const PeakTimeIndicator = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  color: theme.palette.warning.main,
  padding: theme.spacing(0.5),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.warning.light,
}));

export const UtilizationBar = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'utilization',
})<{ utilization: number }>(({ theme, utilization }) => ({
  width: '100%',
  height: 8,
  backgroundColor: theme.palette.grey[200],
  borderRadius: 4,
  overflow: 'hidden',
  marginTop: theme.spacing(1),
  '&::after': {
    content: '""',
    display: 'block',
    width: `${utilization}%`,
    height: '100%',
    backgroundColor: utilization > 80 
      ? theme.palette.error.main 
      : theme.palette.success.main,
    transition: theme.transitions.create('width', {
      duration: theme.transitions.duration.standard,
    }),
  },
})); 