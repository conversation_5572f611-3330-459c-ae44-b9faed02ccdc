import React, { useMemo } from 'react';
import { Typography } from '@mui/material';
import { Warning as WarningIcon } from '@mui/icons-material';
import { TimeSlotCardProps, TimeSlotItemProps } from './types';
import {
  TimeSlotCardContainer,
  TimeSlotHeader,
  TimeSlotContent,
  TimeSlotItem,
  TimeSlotItemHeader,
  TimeSlotItemContent,
  PeakTimeIndicator,
  UtilizationBar,
} from './styles';
import { TimeSlot } from '../types';

const TimeSlotItemComponent: React.FC<TimeSlotItemProps> = ({
  slot,
  onClick,
  className,
  disableHover = false,
  showUtilizationBar = true,
}) => {
  const handleClick = useMemo(() => {
    if (disableHover || !onClick) return undefined;
    return () => onClick();
  }, [disableHover, onClick]);

  return (
    <TimeSlotItem
      className={className}
      isPeak={slot.isPeak}
      onClick={handleClick}
      disableHover={disableHover}
    >
      <TimeSlotItemHeader>
        <Typography variant="subtitle1">{slot.time}</Typography>
        {slot.isPeak && (
          <PeakTimeIndicator>
            <WarningIcon fontSize="small" />
            <Typography variant="caption">Peak Time</Typography>
          </PeakTimeIndicator>
        )}
      </TimeSlotItemHeader>

      <TimeSlotItemContent>
        <Typography variant="body2" color="text.secondary">
          Bookings: {slot.booked}/{slot.total}
        </Typography>
        <Typography
          variant="body2"
          color={slot.utilization >= 80 ? 'error' : 'text.secondary'}
        >
          Utilization: {slot.utilization}%
        </Typography>
        {showUtilizationBar && (
          <UtilizationBar utilization={slot.utilization} />
        )}
      </TimeSlotItemContent>
    </TimeSlotItem>
  );
};

export const TimeSlotCard: React.FC<TimeSlotCardProps> = ({
  date,
  slots,
  onSlotClick,
  className,
  disableHover = false,
  showUtilizationBar = true,
}) => {
  const handleSlotClick = useMemo(() => {
    if (!onSlotClick) return undefined;
    return (slot: TimeSlot) => () => onSlotClick(slot);
  }, [onSlotClick]);

  return (
    <TimeSlotCardContainer className={className}>
      <TimeSlotHeader>
        <Typography variant="h6">{date}</Typography>
        <Typography variant="body2" color="text.secondary">
          {slots.length} slots
        </Typography>
      </TimeSlotHeader>

      <TimeSlotContent>
        {slots.map((slot) => (
          <TimeSlotItemComponent
            key={slot.time}
            slot={slot}
            onClick={handleSlotClick?.(slot)}
            disableHover={disableHover}
            showUtilizationBar={showUtilizationBar}
          />
        ))}
      </TimeSlotContent>
    </TimeSlotCardContainer>
  );
}; 