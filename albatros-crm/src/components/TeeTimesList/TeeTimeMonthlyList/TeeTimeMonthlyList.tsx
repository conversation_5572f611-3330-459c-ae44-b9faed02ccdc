import {
  Attach<PERSON><PERSON>,
  Calendar<PERSON>oday,
  Group,
  Speed,
  TrendingDown,
  TrendingFlat,
  TrendingUp
} from '@mui/icons-material';
import {
  Box,
  Chip,
  Fade,
  Grid,
  LinearProgress,
  Paper,
  Skeleton,
  Typography
} from '@mui/material';
import { format } from 'date-fns';
import React, { useMemo, useState } from 'react';
import { TeeTimeMonthlyListProps } from './types';

interface TimeSlotData {
  bookings: number;
  utilization: number;
}

interface WeekData {
  weekNumber: number;
  utilization: number;
  bookings: number;
  revenue: number;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  color?: 'primary' | 'success' | 'warning' | 'info';
  subtitle?: string;
}

const MetricCard = ({ title, value, icon, trend, color = 'primary', subtitle = '' }: MetricCardProps) => (
  <Paper sx={{ p: 2, height: '100%' }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <Box sx={{ 
        backgroundColor: `${color}.light`,
        borderRadius: '50%',
        p: 1,
        mr: 2
      }}>
        {icon}
      </Box>
      <Typography variant="subtitle2" color="text.secondary">
        {title}
      </Typography>
    </Box>
    <Typography variant="h4" sx={{ mb: 1 }}>
      {value}
    </Typography>
    {subtitle && (
      <Typography variant="body2" color="text.secondary">
        {subtitle}
      </Typography>
    )}
    {trend !== undefined && (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {trend > 0 ? (
          <TrendingUp color="success" fontSize="small" />
        ) : trend < 0 ? (
          <TrendingDown color="error" fontSize="small" />
        ) : (
          <TrendingFlat color="action" fontSize="small" />
        )}
        <Typography 
          variant="body2" 
          color={trend > 0 ? 'success.main' : trend < 0 ? 'error.main' : 'text.secondary'} 
          sx={{ ml: 0.5 }}
        >
          {trend === 0 ? 'No change' : `${Math.abs(trend)}% vs prev month`}
        </Typography>
      </Box>
    )}
  </Paper>
);

interface TimeSlotBarProps {
  time: string;
  utilization: number;
  bookings: number;
}

const TimeSlotBar = ({ time, utilization, bookings }: TimeSlotBarProps) => (
  <Box sx={{ mb: 1 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
      <Typography variant="body2">{time}</Typography>
      <Typography variant="body2" color="text.secondary">
        {bookings} bookings
      </Typography>
    </Box>
    <Box sx={{ width: '100%', position: 'relative' }}>
      <LinearProgress 
        variant="determinate" 
        value={utilization} 
        sx={{ 
          height: 8, 
          borderRadius: 4,
          backgroundColor: 'action.hover',
          '& .MuiLinearProgress-bar': {
            backgroundColor: utilization > 80 ? 'success.main' : utilization > 50 ? 'warning.main' : 'error.main'
          }
        }} 
      />
    </Box>
  </Box>
);

interface WeekPerformanceBarProps {
  weekNumber: number;
  utilization: number;
  bookings: number;
  revenue: number;
}

const WeekPerformanceBar = ({ weekNumber, utilization, bookings, revenue }: WeekPerformanceBarProps) => (
  <Box sx={{ mb: 2 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
      <Typography variant="body2">Week {weekNumber}</Typography>
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {bookings} bookings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          ${revenue.toLocaleString()}
        </Typography>
      </Box>
    </Box>
    <Box sx={{ width: '100%', position: 'relative' }}>
      <LinearProgress 
        variant="determinate" 
        value={utilization} 
        sx={{ 
          height: 8, 
          borderRadius: 4,
          backgroundColor: 'action.hover',
          '& .MuiLinearProgress-bar': {
            backgroundColor: utilization > 80 ? 'success.main' : utilization > 50 ? 'warning.main' : 'error.main'
          }
        }} 
      />
    </Box>
  </Box>
);

const MonthSkeleton = () => (
  <Fade in={true}>
    <Box>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[...Array(4)].map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
                <Skeleton variant="text" width={100} />
              </Box>
              <Skeleton variant="text" width={80} height={48} />
              <Skeleton variant="text" width={120} />
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Skeleton variant="text" width={200} height={32} sx={{ mb: 2 }} />
            {[...Array(7)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Skeleton variant="text" width={100} />
                  <Skeleton variant="text" width={60} />
                </Box>
                <Skeleton variant="rounded" width="100%" height={6} />
              </Box>
            ))}
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Skeleton variant="text" width={150} height={32} sx={{ mb: 2 }} />
            {[...Array(5)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Skeleton variant="text" width={80} />
                  <Skeleton variant="text" width={60} />
                </Box>
                <Skeleton variant="rounded" width="100%" height={8} />
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  </Fade>
);

export const TeeTimeMonthlyList: React.FC<TeeTimeMonthlyListProps> = ({
  month: propMonth,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentMonthIndex, setCurrentMonthIndex] = useState(0);

  const monthsData = useMemo(() => {
    const months = [];
    const currentDate = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    let monthStart = new Date(sixMonthsAgo);
    monthStart.setDate(1);

    while (monthStart <= currentDate) {
      const isRecentMonth = monthStart.getMonth() === currentDate.getMonth();
      const baseUtilization = isRecentMonth ? 75 : 60;
      const randomVariation = Math.floor(Math.random() * 30) - 15;
      const utilization = Math.min(Math.max(baseUtilization + randomVariation, 30), 95);

      const totalSlots = 5600; // 200 slots per day * 28 days
      const totalBookings = Math.floor((totalSlots * utilization) / 100);
      const totalRevenue = totalBookings * 100;

      const monthStats = {
        totalBookings,
        totalRevenue,
        totalSlots,
        averageUtilization: utilization,
        weeklyPerformance: generateWeeklyPerformance(utilization),
        popularTimes: generatePopularTimes(utilization),
      };

      months.push({
        date: new Date(monthStart),
        stats: monthStats
      });

      monthStart.setMonth(monthStart.getMonth() + 1);
    }

    return months.sort((a, b) => b.date.getTime() - a.date.getTime());
  }, []);

  const handleMonthChange = (direction: 'next' | 'prev') => {
    if ((direction === 'next' && currentMonthIndex <= 0) || 
        (direction === 'prev' && currentMonthIndex >= monthsData.length - 1)) {
      return;
    }
    
    setIsLoading(true);
    
    setTimeout(() => {
      if (direction === 'next') {
        setCurrentMonthIndex(prev => prev - 1);
      } else {
        setCurrentMonthIndex(prev => prev + 1);
      }
      setIsLoading(false);
    }, 300);
  };

  const currentMonth = monthsData[currentMonthIndex];

  if (!currentMonth) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>No data available for this month</Typography>
      </Box>
    );
  }

  const MonthContent = () => (
    <Fade in={!isLoading} timeout={200}>
      <Box>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Total Bookings"
              value={currentMonth.stats.totalBookings}
              icon={<Group color="primary" />}
              trend={5}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Revenue"
              value={`$${currentMonth.stats.totalRevenue.toLocaleString()}`}
              icon={<AttachMoney color="success" />}
              trend={8}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Utilization"
              value={`${Math.round(currentMonth.stats.averageUtilization)}%`}
              icon={<Speed color="warning" />}
              trend={0}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Weekly Avg. Bookings"
              value={Math.round(currentMonth.stats.totalBookings / 4)}
              icon={<CalendarToday color="info" />}
              trend={-3}
              color="info"
            />
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Weekly Performance
              </Typography>
              <Box sx={{ mt: 2 }}>
                {currentMonth.stats.weeklyPerformance.map((week) => (
                  <WeekPerformanceBar
                    key={week.weekNumber}
                    weekNumber={week.weekNumber}
                    utilization={week.utilization}
                    bookings={week.bookings}
                    revenue={week.revenue}
                  />
                ))}
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Popular Time Slots
              </Typography>
              <Box sx={{ mt: 2 }}>
                {currentMonth.stats.popularTimes.map((entry) => {
                  const [time, data] = entry;
                  return (
                    <TimeSlotBar 
                      key={time}
                      time={time}
                      utilization={data.utilization}
                      bookings={data.bookings}
                    />
                  );
                })}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Fade>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          Monthly Performance Overview
        </Typography>
      </Box>

      <Paper sx={{ mb: 4, p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <CalendarToday sx={{ mr: 2 }} color="primary" />
            <Typography variant="h5">
              {format(currentMonth.date, 'MMMM yyyy')}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography 
              variant="button" 
              onClick={() => handleMonthChange('prev')} 
              sx={{ 
                cursor: currentMonthIndex >= monthsData.length - 1 ? 'not-allowed' : 'pointer',
                opacity: currentMonthIndex >= monthsData.length - 1 ? 0.5 : 1,
                userSelect: 'none',
              }}
            >
              ← Previous Month
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip 
                label={currentMonth.stats.averageUtilization > 70 ? 'High Activity' : 
                       currentMonth.stats.averageUtilization > 50 ? 'Moderate Activity' : 'Low Activity'}
                color={currentMonth.stats.averageUtilization > 70 ? 'success' : 
                       currentMonth.stats.averageUtilization > 50 ? 'warning' : 'error'}
              />
              <Typography 
                variant="button" 
                onClick={() => handleMonthChange('next')} 
                sx={{ 
                  cursor: currentMonthIndex <= 0 ? 'not-allowed' : 'pointer',
                  opacity: currentMonthIndex <= 0 ? 0.5 : 1,
                  userSelect: 'none'
                }}
              >
                Next Month →
              </Typography>
            </Box>
          </Box>
        </Box>

        {isLoading ? <MonthSkeleton /> : <MonthContent />}
        </Paper>
    </Box>
  );
};

// Helper functions for mock data generation
const generatePopularTimes = (baseUtilization: number): Array<[string, TimeSlotData]> => {
  const times = ['7:00 AM', '8:00 AM', '9:00 AM', '2:00 PM', '3:00 PM'];
  return times.map(time => {
    const variation = Math.floor(Math.random() * 20) - 10;
    const utilization = Math.min(Math.max(baseUtilization + variation, 20), 100);
    const total = 20;
    const booked = Math.floor((total * utilization) / 100);
    return [time, { bookings: booked, utilization }];
  });
};

const generateWeeklyPerformance = (baseUtilization: number): WeekData[] => {
  const weeks = [];
  const weeksInMonth = 4; // Simplified to 4 weeks per month
  
  for (let week = 1; week <= weeksInMonth; week++) {
    const variation = Math.floor(Math.random() * 20) - 10;
    const utilization = Math.min(Math.max(baseUtilization + variation, 20), 100);
    const totalSlots = 1400; // 200 slots per day * 7 days
    const bookings = Math.floor((totalSlots * utilization) / 100);
    const revenue = bookings * 100; // $100 per booking

    weeks.push({
      weekNumber: week,
      utilization,
      bookings,
      revenue
    });
  }

  return weeks;
}; 