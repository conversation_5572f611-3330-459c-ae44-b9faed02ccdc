import { TeeTimeData } from '../types';

/**
 * Props for the TeeTimeMonthlyList component
 */
export interface TeeTimeMonthlyListProps {
  /** The ID of the golf course */
  courseId: string;
  /** Array of tee time data for the month */
  data: TeeTimeData[];
  /** The month to display */
  month: Date;
  /** Optional callback when a time slot is clicked */
  onSlotClick?: (date: string, time: string) => void;
  /** Optional className for additional styling */
  className?: string;
  /** Optional flag to show revenue information */
  showRevenue?: boolean;
  /** Optional flag to show utilization badges */
  showUtilizationBadges?: boolean;
}

/**
 * Data structure for a week's summary
 */
export interface WeekSummary {
  /** Total number of bookings for the week */
  totalBookings: number;
  /** Number of available slots for the week */
  availableSlots: number;
  /** Utilization percentage as a string */
  utilization: string;
  /** Revenue for the week as a formatted string */
  revenue: string;
}

/**
 * Data structure for a popular time slot
 */
export interface PopularTime {
  /** The time of the slot */
  time: string;
  /** Number of booked slots */
  booked: number;
  /** Number of available slots */
  available: number;
  /** Utilization level */
  utilization: 'High' | 'Medium' | 'Low';
}

/**
 * Data structure for a week's data
 */
export interface WeekData {
  /** The start date of the week */
  weekOf: string;
  /** Summary statistics for the week */
  summary: WeekSummary;
  /** Array of popular time slots */
  popularTimes: PopularTime[];
}

/**
 * Data structure for a month's data
 */
export interface MonthData {
  /** The month name */
  month: string;
  /** Array of week data */
  weeks: WeekData[];
} 