import { addMonths, startOfMonth, endOfMonth, format, eachDayOfInterval, subMonths } from 'date-fns';

interface TeeTimeSlot {
  time: string;
  bookings: number;
  maxBookings: number;
  isPeak: boolean;
  utilization: number;
}

interface DailyTeeTimeData {
  date: string;
  slots: TeeTimeSlot[];
  totalBookings: number;
}

const TIME_SLOTS = [
  '6:00 AM', '6:30 AM', '7:00 AM', '7:30 AM', '8:00 AM', '8:30 AM',
  '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
  '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM',
  '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'
];

// Enhanced seasonal factors with more realistic variations
const SEASONAL_FACTORS: { [key: string]: number } = {
  '0': 0.5,  // January - Very low season
  '1': 0.6,  // February - Still low but slightly better
  '2': 0.8,  // March - Beginning of spring season
  '3': 1.0,  // April - Peak spring season
  '4': 1.1,  // May - Strong shoulder season
  '5': 1.3,  // June - Peak summer season starts
  '6': 1.4,  // July - Absolute peak season
  '7': 1.4,  // August - Still peak season
  '8': 1.2,  // September - Strong shoulder season
  '9': 1.0,  // October - Fall season
  '10': 0.7, // November - Off season begins
  '11': 0.5, // December - Low season
};

// Define peak hours with more granular time slots
const PEAK_HOURS = {
  PRIME: ['7:30 AM', '8:00 AM', '8:30 AM', '9:00 AM'], // Most popular times
  SECONDARY: ['7:00 AM', '9:30 AM', '10:00 AM', '2:00 PM', '2:30 PM'], // Next most popular
  SHOULDER: ['6:30 AM', '10:30 AM', '1:30 PM', '3:00 PM'] // Moderately popular
};

// Weather patterns by month (chance of good weather)
const WEATHER_PATTERNS: { [key: string]: number } = {
  '0': 0.4,  // January
  '1': 0.4,  // February
  '2': 0.6,  // March
  '3': 0.7,  // April
  '4': 0.8,  // May
  '5': 0.9,  // June
  '6': 0.95, // July
  '7': 0.95, // August
  '8': 0.85, // September
  '9': 0.75, // October
  '10': 0.5, // November
  '11': 0.4, // December
};

export const generateDailyData = (date: Date): DailyTeeTimeData => {
  // Ensure we're working with a fresh Date object
  const currentDate = new Date(date.getTime());
  
  // Create a unique seed for this date that includes year and month
  const dateSeed = currentDate.getTime() + 
    (currentDate.getFullYear() * 12 + currentDate.getMonth()) * 24 * 60 * 60 * 1000;
  
  console.log(`Generating data for date: ${format(currentDate, 'yyyy-MM-dd')}, seed: ${dateSeed}`);
  
  const slots: TeeTimeSlot[] = TIME_SLOTS.map(time => {
    const isPeak = PEAK_HOURS.PRIME.includes(time) || PEAK_HOURS.SECONDARY.includes(time);
    const maxBookings = 4; // 4 players per slot
    
    // Base probability with more realistic factors
    let bookingProbability = 0.4; // Lower base probability
    
    // Seasonal adjustment with more pronounced effect
    const monthIndex = currentDate.getMonth().toString();
    const seasonalFactor = SEASONAL_FACTORS[monthIndex];
    bookingProbability *= seasonalFactor * 1.2; // Increase seasonal impact
    
    // Time slot popularity with enhanced variation
    if (PEAK_HOURS.PRIME.includes(time)) {
      bookingProbability *= 2.0; // Increased prime time premium
    } else if (PEAK_HOURS.SECONDARY.includes(time)) {
      bookingProbability *= 1.6; // Increased secondary time premium
    } else if (PEAK_HOURS.SHOULDER.includes(time)) {
      bookingProbability *= 1.3; // Increased shoulder time premium
    }
    
    // Weekend adjustment with more variation
    const dayOfWeek = currentDate.getDay();
    if (dayOfWeek === 6) bookingProbability *= 2.0; // Increased Saturday premium
    else if (dayOfWeek === 0) bookingProbability *= 1.8; // Increased Sunday premium
    else if (dayOfWeek === 5) bookingProbability *= 1.5; // Increased Friday premium
    else if (dayOfWeek === 4) bookingProbability *= 1.2; // Increased Thursday boost
    else bookingProbability *= 0.7; // More pronounced weekday reduction
    
    // Enhanced weather impact based on month and specific date
    const weatherBase = WEATHER_PATTERNS[monthIndex];
    const timeIndex = TIME_SLOTS.indexOf(time);
    const dateHash = dateSeed + timeIndex * 3600000; // Use larger time slot separation
    const weatherFactor = (Math.abs(Math.sin(dateHash)) * 0.4 + weatherBase);
    bookingProbability *= weatherFactor;
    
    // Add more variation based on the specific date and time
    const consistentRandom = Math.abs(Math.sin(dateHash / 7200000)); // Increased period
    bookingProbability *= (0.7 + consistentRandom * 0.6); // Wider range
    
    // Add stronger monthly variation
    const monthVariation = Math.abs(Math.sin((currentDate.getMonth() + currentDate.getFullYear() * 12) * Math.PI / 6));
    bookingProbability *= (0.8 + monthVariation * 0.4); // Increased monthly impact
    
    // Ensure probability is within bounds
    bookingProbability = Math.max(0.1, Math.min(1, bookingProbability));
    
    // Calculate final bookings with enhanced randomness
    const baseBookings = Math.floor(bookingProbability * maxBookings);
    const finalBookings = Math.min(baseBookings, maxBookings);
    const utilization = (finalBookings / maxBookings) * 100;

    return {
      time,
      bookings: finalBookings,
      maxBookings,
      isPeak,
      utilization,
    };
  });

  const result = {
    date: format(currentDate, 'yyyy-MM-dd'),
    slots,
    totalBookings: slots.reduce((sum, slot) => sum + slot.bookings, 0),
  };

  console.log(`Generated ${result.totalBookings} total bookings for ${result.date}`);
  return result;
};

// Generate historical data for the past 12 months and future months
export const generateHistoricalData = (currentDate: Date = new Date()): DailyTeeTimeData[] => {
  // Generate data for 12 months before and 12 months after the current date
  const startDate = startOfMonth(subMonths(currentDate, 12));
  const endDate = endOfMonth(addMonths(currentDate, 12));
  
  console.log('Generating historical data range:', 
    format(startDate, 'yyyy-MM-dd'), 'to', 
    format(endDate, 'yyyy-MM-dd')
  );
  
  const allDays = eachDayOfInterval({ start: startDate, end: endDate });
  const data = allDays.map(day => generateDailyData(new Date(day)));
  
  console.log(`Generated ${data.length} days of data`);
  return data;
};

// Remove the pre-generated mockMonthlyData export since we're generating data on demand now 