export interface TimeSlot {
  time: string;
  militaryTime: string; // Internal military time format
  isBooked: boolean;
  isPeak: boolean;
  price: number;
  bookings: number;
  maxBookings: number;
  utilization: number;
  booked: number;
  total: number;
}

export interface TeeTimeData {
  id: string | number;
  image: string;
  title: string;
  description: string;
  date: string;
  totalBookings: number;
  utilization: number;
  slots: TimeSlot[];
  isPeak?: boolean;
  booked?: number;
  available?: number;
  total?: number;
}

export interface TeeTimesListProps {
  courseId: string;
  timeView?: ViewType;
  onTeeTimeSelect?: (teeTime: TeeTimeData) => void;
  onSlotClick?: (slot: any) => void;  // Make this generic enough to handle both formats
}

export type ViewType = 'daily' | 'weekly' | 'monthly';

export interface TeeTimeMetrics {
  totalBookings: number;
  totalUtilization: number;
  totalSlots: number;
  peakSlots: number;
}

export interface PaginationSettings {
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
  onPageChange: (event: React.ChangeEvent<unknown>, value: number) => void;
}

export interface TeeTimeSlot {
  time: string;
  date?: string;
  players?: any[];
  isAvailable?: boolean;
  cost?: number;
  [key: string]: any;  // Allow for additional properties
}

export interface DailyViewProps {
  data: TeeTimeData[];
} 