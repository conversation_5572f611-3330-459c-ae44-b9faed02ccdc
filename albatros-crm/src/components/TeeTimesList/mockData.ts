import { TeeTimeData, TimeSlot } from './types';

const convertToAmPm = (militaryTime: string): string => {
  const [hours, minutes] = militaryTime.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
};

const generateTimeSlots = (date: Date): TimeSlot[] => {
  const slots: TimeSlot[] = [];
  
  for (let hour = 7; hour <= 19; hour++) {
    const isPeakHour = (hour >= 9 && hour < 11) || (hour >= 13 && hour < 15);
    const maxBookings = isPeakHour ? 8 : 6;
    const bookings = Math.floor(Math.random() * maxBookings);
    const militaryTime = `${hour.toString().padStart(2, '0')}:00`;
    
    slots.push({
      time: convertToAmPm(militaryTime),
      militaryTime,
      isBooked: bookings === maxBookings,
      isPeak: isPeakHour,
      price: isPeakHour ? 150 : 120,
      bookings,
      maxBookings,
      utilization: Math.round((bookings / maxBookings) * 100),
      booked: bookings,
      total: maxBookings
    });
  }

  return slots;
};

const generateDailyData = (date: Date): TeeTimeData => {
  const slots = generateTimeSlots(date);
  const totalBookings = slots.reduce((sum, slot) => sum + slot.bookings, 0);
  const totalMaxBookings = slots.reduce((sum, slot) => sum + slot.maxBookings, 0);
  
  return {
    id: date.getTime().toString(),
    image: '/default-tee-time.jpg',
    title: date.toLocaleDateString('en-US', { 
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    }),
    description: `${totalBookings} bookings out of ${totalMaxBookings} slots`,
    date: date.toLocaleDateString('en-US', { 
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    }),
    totalBookings,
    utilization: Math.round((totalBookings / totalMaxBookings) * 100),
    slots,
    isPeak: slots.some(slot => slot.isPeak),
    booked: totalBookings,
    available: totalMaxBookings - totalBookings,
    total: totalMaxBookings
  };
};

export const generateWeeklyMockData = (): TeeTimeData[] => {
  const data: TeeTimeData[] = [];
  const today = new Date();
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);
    data.push(generateDailyData(date));
  }
  
  return data;
};

export const generateMonthlyMockData = (): TeeTimeData[] => {
  const data: TeeTimeData[] = [];
  const today = new Date();
  
  for (let i = 0; i < 30; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);
    data.push(generateDailyData(date));
  }
  
  return data;
}; 