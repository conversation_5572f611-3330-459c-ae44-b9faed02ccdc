import { Box, styled } from '@mui/material';
import { alpha } from '@mui/material/styles';

export const DailyContainer = styled(Box)(({ theme }) => ({
  width: '100%',
}));

export const DailyHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

export const DailyTitle = styled('h2')(({ theme }) => ({
  margin: 0,
  color: theme.palette.text.primary,
  fontSize: '1.5rem',
  fontWeight: 600,
}));

export const PeriodContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
}));

export const PeriodHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(2),
}));

export const PeriodTitle = styled('h3')(({ theme }) => ({
  margin: 0,
  color: theme.palette.text.secondary,
  fontSize: '1.1rem',
  fontWeight: 500,
}));

interface TeeTimeCardProps {
  utilization: number;
}

export const TeeTimeCard = styled(Box)<TeeTimeCardProps>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  border: `1px solid ${theme.palette.divider}`,
  '&:hover': {
    boxShadow: theme.shadows[3],
    transform: 'translateY(-2px)',
  },
}));

export const TeeTimeImage = styled('img')({
  width: 80,
  height: 60,
  objectFit: 'cover',
  borderRadius: 8,
  marginRight: 16,
});

export const TeeTimeInfo = styled(Box)({
  flex: 1,
});

export const TeeTimeStats = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(3),
  marginLeft: theme.spacing(2),
}));

export const StatBox = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  minWidth: 80,
}));

interface PeakBadgeProps {
  isPeak?: boolean;
}

export const PeakBadge = styled('span')<PeakBadgeProps>(({ theme, isPeak }) => ({
  display: 'inline-block',
  padding: '2px 8px',
  marginLeft: 8,
  borderRadius: 12,
  fontSize: '0.75rem',
  backgroundColor: isPeak ? theme.palette.warning.light : 'transparent',
  color: isPeak ? theme.palette.warning.dark : theme.palette.text.secondary,
}));

interface UtilizationBarProps {
  utilization: number;
}

export const UtilizationBar = styled(Box)<UtilizationBarProps>(({ theme, utilization }) => {
  let color;
  if (utilization >= 80) {
    color = theme.palette.success.main;
  } else if (utilization >= 40) {
    color = theme.palette.warning.main;
  } else {
    color = theme.palette.error.main;
  }

  return {
    width: '100%',
    height: 6,
    backgroundColor: alpha(theme.palette.grey[200], 0.5),
    borderRadius: 3,
    marginTop: 8,
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      height: '100%',
      width: `${utilization}%`,
      backgroundColor: color,
      borderRadius: 3,
      transition: 'width 0.3s ease',
    },
  };
}); 