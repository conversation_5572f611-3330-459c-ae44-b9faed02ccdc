import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormHelperText,
  IconButton,
  InputLabel,
  OutlinedInput,
  Switch,
  Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import {
  StyledButton,
  StyledDialog
} from '../../../styles/styledComponents';
import { PhoneInput } from '../../shared/PhoneInput';
import { Player } from './types';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  isConfirmed: boolean;
  checkInTime?: string;
}

interface TouchedFields {
  [key: string]: boolean;
}

interface FormErrors {
  [key: string]: string;
}

interface PlayerEditFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (player: Player) => void;
  initialData?: Player | null;
  title: string;
  mode: 'add' | 'edit';
}

export const PlayerEditForm: React.FC<PlayerEditFormProps> = ({
  open,
  onClose,
  onSave,
  initialData,
  title,
  mode
}) => {
  const [formData, setFormData] = useState<FormData>({
    firstName: initialData?.firstName ?? '',
    lastName: initialData?.lastName ?? '',
    email: initialData?.email ?? '',
    phoneNumber: initialData?.phoneNumber ?? '',
    isConfirmed: initialData?.isConfirmed ?? false,
    checkInTime: initialData?.checkInTime,
  });
  const [touched, setTouched] = useState<TouchedFields>({});
  const [errors, setErrors] = useState<FormErrors>({});

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (open) {  // Only update when dialog opens
      if (initialData) {
        setFormData({
          firstName: initialData.firstName ?? '',
          lastName: initialData.lastName ?? '',
          email: initialData.email ?? '',
          phoneNumber: initialData.phoneNumber ?? '',
          isConfirmed: initialData.isConfirmed ?? false,
          checkInTime: initialData.checkInTime,
        });
      } else {
        // Only reset form when there's no initial data (i.e., adding new player)
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          isConfirmed: false,
          checkInTime: undefined,
        });
      }
    }
  }, [initialData, open]);

  const handlePhoneChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      phoneNumber: value
    }));

    setTouched(prev => ({
      ...prev,
      phoneNumber: true
    }));

    // Remove country code and get just the digits
    const numberWithoutCountryCode = value.replace(/^\+1\s?/, '');
    const digitsOnly = numberWithoutCountryCode.replace(/\D/g, '');
    
    setErrors(prev => ({
      ...prev,
      phoneNumber: !value ? 'Phone number is required' : 
             digitsOnly.length < 10 ? 'Please enter a valid phone number' : ''
    }));
  };

  const handleTextChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleBlur = (field: keyof FormData) => () => {
    setTouched((prev) => ({ ...prev, [field]: true }));
    validateField(field, formData[field]);
  };

  const validateField = (field: keyof FormData, value: any): boolean => {
    let error = '';
    
    switch (field) {
      case 'email':
        if (!value) {
          error = 'Email is required';
        } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
          error = 'Invalid email address';
        }
        break;
      case 'firstName':
        if (!value) {
          error = 'First name is required';
        }
        break;
      case 'lastName':
        if (!value) {
          error = 'Last name is required';
        }
        break;
      case 'phoneNumber':
        if (!value) {
          error = 'Phone number is required';
        } else {
          // For international numbers, we'll be more lenient with length
          const digitsOnly = value.replace(/\D/g, '');
          if (digitsOnly.length < 10) {
            error = 'Please enter a valid phone number';
          }
          if (digitsOnly.length > 15) {
            error = 'Phone number cannot exceed 15 digits';
          }
        }
        break;
      case 'isConfirmed':
        // Boolean fields don't need validation
        break;
      case 'checkInTime':
        // Optional field, no validation needed
        break;
    }

    setErrors((prev) => ({ ...prev, [field]: error }));
    return !error;
  };

  const isFormValid = () => {
    const fields: (keyof FormData)[] = ['firstName', 'lastName', 'email', 'phoneNumber'];
    return fields.every((field) => validateField(field, formData[field]));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => ({
      ...acc,
      [key]: true
    }), {});
    setTouched(allTouched);

    if (!isFormValid()) {
      return;
    }
    
    setIsSubmitting(true);
    try {
      const playerData: Player = {
        id: initialData?.id ?? '',
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        isConfirmed: formData.isConfirmed,
        checkInTime: formData.checkInTime,
      };
      await onSave(playerData);
      onClose();
    } catch (error) {
      console.error('Error saving player:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <StyledDialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">{title}</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <form onSubmit={handleSubmit}>
      <DialogContent>
          <Box display="flex" flexDirection="column" gap={2}>
            <Box display="flex" gap={2}>
              <FormControl error={touched.firstName && !!errors.firstName} fullWidth>
                <InputLabel htmlFor="firstName">First Name</InputLabel>
                <OutlinedInput
                  id="firstName"
                  value={formData.firstName}
                  onChange={handleTextChange('firstName')}
                  onBlur={handleBlur('firstName')}
                  label="First Name"
                  fullWidth
                />
                {touched.firstName && errors.firstName && (
                  <FormHelperText>{errors.firstName}</FormHelperText>
                )}
              </FormControl>

              <FormControl error={touched.lastName && !!errors.lastName} fullWidth>
                <InputLabel htmlFor="lastName">Last Name</InputLabel>
                <OutlinedInput
                  id="lastName"
                  value={formData.lastName}
                  onChange={handleTextChange('lastName')}
                  onBlur={handleBlur('lastName')}
                  label="Last Name"
              fullWidth
                />
                {touched.lastName && errors.lastName && (
                  <FormHelperText>{errors.lastName}</FormHelperText>
                )}
              </FormControl>
            </Box>

            <FormControl error={touched.email && !!errors.email} fullWidth>
              <InputLabel htmlFor="email">Email</InputLabel>
              <OutlinedInput
                id="email"
              value={formData.email}
                onChange={handleTextChange('email')}
                onBlur={handleBlur('email')}
                label="Email"
              fullWidth
              />
              {touched.email && errors.email && (
                <FormHelperText>{errors.email}</FormHelperText>
              )}
            </FormControl>

            <PhoneInput
              value={formData.phoneNumber}
              onChange={handlePhoneChange}
              error={touched.phoneNumber && Boolean(errors.phoneNumber)}
              helperText={touched.phoneNumber ? errors.phoneNumber : undefined}
              required
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isConfirmed}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isConfirmed: e.target.checked }))}
                />
              }
              label="Confirmed"
            />
          </Box>
      </DialogContent>
        <DialogActions>
          <StyledButton onClick={onClose}>Cancel</StyledButton>
        <StyledButton 
            type="submit"
            variant="contained"
            color="primary"
          disabled={isSubmitting}
        >
            {isSubmitting ? 'Saving...' : 'Save'}
        </StyledButton>
      </DialogActions>
      </form>
    </StyledDialog>
  );
}; 