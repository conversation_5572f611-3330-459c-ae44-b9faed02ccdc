import {
  AccessTime as AccessTimeIcon,
  Add as AddIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  Edit as EditIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Redo as RedoIcon,
  Star as StarIcon,
  Undo as UndoIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import {
  Alert,
  Badge,
  Box,
  Chip,
  Fade,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Skeleton,
  Snackbar,
  styled,
  Tooltip,
  Typography
} from '@mui/material';
import { format, startOfDay } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useCallback, useState, useEffect } from 'react';
import { useUndoableState } from '../../../hooks/useUndoableState';
import { MessageDialog } from './MessageDialog';
import { PlayerEditForm } from './PlayerEditForm';
import { Player, TeeTimeDailyListProps, TeeTimeSlot } from './types';

const TableContainer = styled(Paper)(({ theme }) => ({
  overflow: 'hidden',
  margin: theme.spacing(2, 0)
}));

const TableHeader = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTypography-root': {
    fontWeight: 500
  }
}));

const TimeSlotRow = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&:hover': {
    backgroundColor: theme.palette.action.hover
  },
  alignItems: 'center'
}));

const PlayerSlot = styled(Box, {
  shouldForwardProp: prop => prop !== 'isOccupied' && prop !== 'isConfirmed' && prop !== 'isCheckedIn'
})<{ isOccupied?: boolean; isConfirmed?: boolean; isCheckedIn?: boolean }>(
  ({ theme, isOccupied, isConfirmed, isCheckedIn }) => ({
    width: 36,
    height: 36,
    borderRadius: '50%',
    border: `2px solid ${isOccupied
        ? isCheckedIn
          ? theme.palette.success.dark
          : isConfirmed
            ? theme.palette.success.main
            : theme.palette.warning.main
        : theme.palette.divider
      }`,
    backgroundColor: isOccupied
      ? isCheckedIn
        ? theme.palette.success.light
        : isConfirmed
          ? theme.palette.success.light
          : theme.palette.warning.light
      : theme.palette.background.paper,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: theme.spacing(0, 0.5),
    color: isOccupied
      ? isCheckedIn
        ? theme.palette.success.dark
        : isConfirmed
          ? theme.palette.success.dark
          : theme.palette.warning.dark
      : theme.palette.text.secondary,
    cursor: 'pointer',
    transition: theme.transitions.create(
      ['background-color', 'border-color', 'transform'],
      { duration: theme.transitions.duration.short }
    ),
    '&:hover': {
      backgroundColor: isOccupied
        ? isCheckedIn
          ? theme.palette.success.light
          : isConfirmed
            ? theme.palette.success.light
            : theme.palette.warning.light
        : theme.palette.action.hover,
      transform: 'scale(1.1)'
    }
  })
);

const PlayerChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  transition: theme.transitions.create(['background-color', 'box-shadow'], {
    duration: theme.transitions.duration.short
  }),
  '&.confirmed': {
    backgroundColor: theme.palette.success.light,
    color: theme.palette.success.dark,
    '& .MuiChip-icon': {
      color: theme.palette.success.main
    }
  },
  '&.checked-in': {
    backgroundColor: theme.palette.success.dark,
    color: theme.palette.common.white,
    '& .MuiChip-icon': {
      color: theme.palette.common.white
    },
    boxShadow: theme.shadows[2]
  },
  '&.pending': {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.dark,
    '& .MuiChip-icon': {
      color: theme.palette.warning.main
    }
  },
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[1]
  }
}));

const PeakTimeChip = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.error.light,
  color: theme.palette.error.dark,
  '& .MuiChip-icon': {
    color: theme.palette.error.main
  }
}));

// Mock data helpers
const firstNames = [
  'James', 'John', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Thomas',
  'Charles', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Susan', 'Jessica',
  'Sarah', 'Karen', 'Nancy', 'Lisa', 'Margaret', 'Betty', 'Sandra', 'Ashley',
  'Dorothy', 'Emily', 'Michelle', 'Carol', 'Amanda', 'Melissa', 'Deborah'
];

const lastNames = [
  'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
  'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson',
  'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Thompson', 'White', 'Harris',
  'Clark', 'Lewis', 'Robinson', 'Walker', 'Hall', 'Young', 'Allen'
];

const generatePhoneNumber = () => {
  const areaCode = ['407', '321', '689'][Math.floor(Math.random() * 3)];
  const prefix = Math.floor(Math.random() * 900) + 100;
  const lineNum = Math.floor(Math.random() * 9000) + 1000;
  return `(${areaCode}) ${prefix}-${lineNum}`;
};

const generateEmail = (name: string) => {
  const cleanName = name.toLowerCase().replace(/\s+/g, '.');
  const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'icloud.com', 'aol.com'];
  const domain = domains[Math.floor(Math.random() * domains.length)];
  return `${cleanName}@${domain}`;
};

const DailySkeleton = () => (
  <Fade in={true}>
    <TableContainer>
      <TableHeader container>
        <Grid item xs={2}>
          <Skeleton variant="text" width={60} />
        </Grid>
        <Grid item xs={4}>
          <Skeleton variant="text" width={120} />
        </Grid>
        <Grid item xs={2}>
          <Skeleton variant="text" width={60} />
        </Grid>
        <Grid item xs={4}>
          <Skeleton variant="text" width={100} />
        </Grid>
      </TableHeader>
      {[...Array(8)].map((_, index) => (
        <TimeSlotRow container key={index}>
          <Grid item xs={2}>
            <Skeleton variant="text" width={80} />
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} variant="circular" width={36} height={36} />
              ))}
            </Box>
          </Grid>
          <Grid item xs={2}>
            <Skeleton variant="text" width={60} />
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[...Array(2)].map((_, i) => (
                <Skeleton key={i} variant="rounded" width={120} height={32} />
              ))}
            </Box>
          </Grid>
        </TimeSlotRow>
      ))}
    </TableContainer>
  </Fade>
);

const ActionButton = styled(IconButton)(({ theme }) => ({
  marginLeft: theme.spacing(1)
}));

const AnimatedTimeSlotRow = motion(TimeSlotRow);

const getDisplayName = (player: Player | null): string => {
  if (!player) return '';
  return `${player.firstName} ${player.lastName}`;
};

export const TeeTimeDailyList: React.FC<TeeTimeDailyListProps> = ({
  courseId,
  date = new Date(),
  data,
  onSlotClick,
  onPlayerConfirm,
  onPlayerCheckIn,
  className
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPlayer, setSelectedPlayer] = useState<{ id: string; time: string } | null>(null);
  const [isAddPlayerDialogOpen, setIsAddPlayerDialogOpen] = useState(false);
  const [isEditPlayerDialogOpen, setIsEditPlayerDialogOpen] = useState(false);
  const [isMessageDialogOpen, setIsMessageDialogOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<TeeTimeSlot | null>(null);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isLoading] = useState(false);
  const currentDate = startOfDay(new Date());
  const formattedDate = format(date, 'EEEE, MMMM d');
  const [hasUserInteraction, setHasUserInteraction] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const undoableState = useUndoableState<TeeTimeSlot[]>([]);
  const mockSlots = undoableState.state;
  const { canUndo, canRedo, undo, redo, set } = undoableState;

  useEffect(() => {
    if (!isInitialLoad) return;

    const slots: TeeTimeSlot[] = [];
    const startTime = new Date(date);
    startTime.setHours(7, 0, 0, 0); // Start at 7 AM
    const endTime = new Date(date);
    endTime.setHours(16, 0, 0, 0); // End at 4 PM

    let currentTime = new Date(startTime);
    while (currentTime < endTime) {
      const hour = currentTime.getHours();
      const isPeak = (hour >= 8 && hour <= 11); // Peak hours: 8 AM to 11 AM
      const timeSlot = format(currentTime, 'HHmm');
      
      const timeBasedProbability = (() => {
        if (isPeak) return 0.9; // Very likely to have bookings during peak
        if (hour < 8) return 0.3; // Early morning, fewer bookings
        if (hour > 14) return 0.4; // Late afternoon, moderate bookings
        return 0.6; // Regular hours, moderate-high bookings
      })();

      const players: Player[] = [];
      if (Math.random() < timeBasedProbability) {
        const numPlayers = isPeak ? 
          (Math.random() < 0.7 ? 4 : Math.floor(Math.random() * 2) + 2) : // Peak: 70% chance of 4 players, else 2-3
          (Math.floor(Math.random() * 3) + 1); // Non-peak: 1-3 players

        for (let i = 0; i < numPlayers; i++) {
          const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
          const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
          const isConfirmed = Math.random() > (isPeak ? 0.1 : 0.3); // More likely to be confirmed during peak
          const checkInTime = isConfirmed && currentTime.getHours() < new Date().getHours() ? 
            format(new Date(currentTime).setMinutes(currentTime.getMinutes() - Math.floor(Math.random() * 10)), 'HH:mm') : 
            undefined;

          players.push({
            id: `player-${timeSlot}-${i}`,
            firstName,
            lastName,
            isConfirmed,
            checkInTime,
            phoneNumber: generatePhoneNumber(),
            email: generateEmail(`${firstName}.${lastName}`)
          });
        }
      }

      slots.push({
        time: format(currentTime, 'h:mm a'),
        militaryTime: format(currentTime, 'HH:mm'),
        cost: isPeak ? 75 : 66,
        players,
        isPeak,
        maxPlayers: 4
      });

      currentTime.setMinutes(currentTime.getMinutes() + 10);
    }

    set(slots, true);
    setIsInitialLoad(false);
  }, [date, isInitialLoad, set]);

  const handleUserInteraction = useCallback(() => {
    if (isInitialLoad) {
      setIsInitialLoad(false);
    }
    setHasUserInteraction(true);
  }, [isInitialLoad]);

  const showSnackbar = useCallback((message: string) => {
    setSnackbarMessage(message);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedPlayer(null);
  }, []);

  const handleConfirm = useCallback(() => {
    if (selectedPlayer) {
      handleUserInteraction();
      const updatedSlots = mockSlots.map((slot: TeeTimeSlot) => {
        if (slot.militaryTime === selectedPlayer.time) {
          return {
            ...slot,
            players: slot.players.map((player: Player) =>
              player.id === selectedPlayer.id
                ? {
                  ...player,
                  isConfirmed: true
                }
                : player
            )
          };
        }
        return slot;
      });
      set(updatedSlots);
      if (onPlayerConfirm) {
        onPlayerConfirm(selectedPlayer.id, selectedPlayer.time);
      }
      showSnackbar('Player confirmed successfully');
    }
    setAnchorEl(null);
    handleMenuClose();
  }, [selectedPlayer, handleUserInteraction, onPlayerConfirm, mockSlots, set, showSnackbar, handleMenuClose]);

  const handleCheckIn = useCallback(() => {
    if (selectedPlayer) {
      handleUserInteraction();
      const updatedSlots = mockSlots.map((slot: TeeTimeSlot) => {
        if (slot.militaryTime === selectedPlayer.time) {
          return {
            ...slot,
            players: slot.players.map((player: Player) =>
              player.id === selectedPlayer.id
                ? {
                  ...player,
                  checkInTime: format(currentDate, 'HH:mm'),
                  isConfirmed: true
                }
                : player
            )
          };
        }
        return slot;
      });
      set(updatedSlots);
      if (onPlayerCheckIn) {
        onPlayerCheckIn(selectedPlayer.id, selectedPlayer.time);
      }
      showSnackbar('Player checked in successfully');
    }
    setAnchorEl(null);
    handleMenuClose();
  }, [selectedPlayer, handleUserInteraction, onPlayerCheckIn, currentDate, mockSlots, set, showSnackbar, handleMenuClose]);

  const handlePlayerAction = (
    playerId: string,
    slotTime: string,
    event: React.MouseEvent<HTMLElement>
  ) => {
    setSelectedPlayer({ id: playerId, time: slotTime });
    setAnchorEl(event.currentTarget);
  };

  const handleEmptySlotClick = (slot: TeeTimeSlot) => {
    setSelectedSlot(slot);
    setIsAddPlayerDialogOpen(true);
  };

  const handleEditPlayer = () => {
    setIsEditPlayerDialogOpen(true);
    setAnchorEl(null);
  };

  const handleAddPlayerDialogClose = useCallback(() => {
    setIsAddPlayerDialogOpen(false);
    setSelectedSlot(null);
  }, []);

  const handleEditPlayerDialogClose = useCallback(() => {
    setIsEditPlayerDialogOpen(false);
    setSelectedPlayer(null);
  }, []);

  const handleCancelBooking = useCallback(() => {
    if (selectedPlayer) {
      handleUserInteraction();
      const updatedSlots = mockSlots.map((slot: TeeTimeSlot) => {
        if (slot.militaryTime === selectedPlayer.time) {
          return {
            ...slot,
            players: slot.players.filter((player: Player) => player.id !== selectedPlayer.id)
          };
        }
        return slot;
      });
      set(updatedSlots);
      showSnackbar('Booking cancelled successfully');
    }
    setAnchorEl(null);
    handleMenuClose();
  }, [selectedPlayer, handleUserInteraction, mockSlots, set, showSnackbar, handleMenuClose]);

  const handleAddPlayer = useCallback((slot: TeeTimeSlot, playerData: Partial<Player>) => {
    handleUserInteraction();
    const updatedSlots = mockSlots.map((s: TeeTimeSlot) => {
      if (s.militaryTime === slot.militaryTime) {
        const newPlayer: Player = {
          id: `player-${Date.now()}`,
          firstName: playerData.firstName || '',
          lastName: playerData.lastName || '',
          phoneNumber: playerData.phoneNumber || '',
          email: playerData.email || '',
          isConfirmed: false
        };
        return {
          ...s,
          players: [...s.players, newPlayer]
        };
      }
      return s;
    });
    set(updatedSlots);
    showSnackbar('Player added successfully');
    handleAddPlayerDialogClose();
  }, [mockSlots, set, handleUserInteraction, showSnackbar, handleAddPlayerDialogClose]);

  const handleUpdatePlayer = useCallback((playerData: Partial<Player>) => {
    if (!selectedPlayer) return;
    handleUserInteraction();
    const updatedSlots = mockSlots.map((slot: TeeTimeSlot) => {
      if (slot.militaryTime === selectedPlayer.time) {
        return {
          ...slot,
          players: slot.players.map((player: Player) =>
            player.id === selectedPlayer.id
              ? {
                ...player,
                ...playerData
              }
              : player
          )
        };
      }
      return slot;
    });
    set(updatedSlots);
    showSnackbar('Player updated successfully');
    handleEditPlayerDialogClose();
  }, [selectedPlayer, handleUserInteraction, mockSlots, set, showSnackbar, handleEditPlayerDialogClose]);

  const getSelectedPlayerData = useCallback(() => {
    if (!selectedPlayer) return null;
    const slot = mockSlots.find((s: TeeTimeSlot) => s.militaryTime === selectedPlayer.time);
    if (!slot) return null;
    const player = slot.players.find((p: Player) => p.id === selectedPlayer.id);
    return player || null;
  }, [selectedPlayer, mockSlots]);

  const handleSendMessage = useCallback(() => {
    if (selectedPlayer) {
      handleUserInteraction();
      showSnackbar('Message sent successfully');
      setIsMessageDialogOpen(false);
    }
  }, [selectedPlayer, handleUserInteraction, showSnackbar]);

  if (isLoading) {
    return <DailySkeleton />;
  }

  return (
    <Box className={className}>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h5" gutterBottom>
          Tee Times for {formattedDate}
        </Typography>
        <Box>
          <Tooltip title={canUndo && hasUserInteraction ? 'Undo' : 'Nothing to undo'}>
            <span>
              <ActionButton
                onClick={undo}
                disabled={!canUndo || !hasUserInteraction || isInitialLoad}
                size="small"
              >
                <UndoIcon />
              </ActionButton>
            </span>
          </Tooltip>
          <Tooltip title={canRedo && hasUserInteraction ? 'Redo' : 'Nothing to redo'}>
            <span>
              <ActionButton
                onClick={redo}
                disabled={!canRedo || !hasUserInteraction}
                size="small"
              >
                <RedoIcon />
              </ActionButton>
            </span>
          </Tooltip>
        </Box>
      </Box>

      <TableContainer>
        <TableHeader container>
          <Grid item xs={2}>
            <Typography variant="subtitle1">Time</Typography>
          </Grid>
          <Grid item xs={4}>
            <Typography variant="subtitle1">Players</Typography>
          </Grid>
          <Grid item xs={2}>
            <Typography variant="subtitle1">Cost</Typography>
          </Grid>
          <Grid item xs={4}>
            <Typography variant="subtitle1">Details</Typography>
          </Grid>
        </TableHeader>

        <AnimatePresence>
          {mockSlots.map((slot: TeeTimeSlot) => (
            <AnimatedTimeSlotRow
              container
              key={slot.militaryTime}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <Grid item xs={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography>{slot.time}</Typography>
                  {slot.isPeak && (
                    <Tooltip title="Peak Time">
                      <StarIcon fontSize="small" color="error" />
                    </Tooltip>
                  )}
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {[...Array(slot.maxPlayers)].map((_, index) => {
                    const player = slot.players[index];
                    return (
                      <Tooltip
                        key={index}
                        title={player ?
                          <Box sx={{
                            p: 1.5,
                            borderRadius: 1
                          }}>
                            <Typography variant="subtitle2" sx={{
                              fontWeight: 700,
                              mb: 1,
                              color: 'common.white'
                            }}>
                              {getDisplayName(player)}
                            </Typography>
                            <Typography variant="body2" sx={{
                              mb: 0.5,
                              color: 'common.white',
                              fontWeight: 600
                            }}>
                              Phone: {player.phoneNumber}
                            </Typography>
                            <Typography variant="body2" sx={{
                              mb: 1,
                              color: 'common.white',
                              fontWeight: 600
                            }}>
                              Email: {player.email}
                            </Typography>
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1
                            }}>
                              {player.checkInTime ? (
                                <CheckCircleIcon fontSize="small" sx={{ color: 'common.white' }} />
                              ) : player.isConfirmed ? (
                                <CheckCircleIcon fontSize="small" sx={{ color: 'common.white' }} />
                              ) : (
                                <WarningIcon fontSize="small" sx={{ color: 'common.white' }} />
                              )}
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 600,
                                  color: 'common.white'
                                }}
                              >
                                {player.checkInTime
                                  ? 'Checked In'
                                  : player.isConfirmed
                                    ? 'Confirmed'
                                    : 'Pending'}
                              </Typography>
                            </Box>
                            {player.checkInTime && (
                              <Typography
                                variant="body2"
                                sx={{
                                  mt: 1,
                                  color: 'common.white',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                  fontWeight: 600
                                }}
                              >
                                <AccessTimeIcon fontSize="small" sx={{ color: 'common.white' }} />
                                Checked in at: {player.checkInTime}
                              </Typography>
                            )}
                          </Box>
                          : 'Add Player'
                        }
                      >
                        <PlayerSlot
                          isOccupied={!!player}
                          isConfirmed={player?.isConfirmed}
                          isCheckedIn={!!player?.checkInTime}
                          onClick={(e) => player ?
                            handlePlayerAction(player.id, slot.militaryTime, e) :
                            handleEmptySlotClick(slot)
                          }
                        >
                          {player ? (
                            <Badge
                              variant="dot"
                              color={player.checkInTime ? "success" : "default"}
                              overlap="circular"
                            >
                              <PersonIcon fontSize="small" />
                            </Badge>
                          ) : (
                            <AddIcon fontSize="small" />
                          )}
                        </PlayerSlot>
                      </Tooltip>
                    );
                  })}
                </Box>
              </Grid>
              <Grid item xs={2}>
                <Typography>
                  ${slot.cost}
                  {slot.isPeak && (
                    <PeakTimeChip
                      size="small"
                      icon={<StarIcon />}
                      label="Peak"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                  {slot.players.map((player: Player) => (
                    <PlayerChip
                      key={player.id}
                      icon={player.checkInTime ? <CheckCircleIcon /> : player.isConfirmed ? <CheckCircleIcon /> : <WarningIcon />}
                      label={
                        <Tooltip title={
                          <Box sx={{ p: 1 }}>
                            <Typography variant="body2">
                              Phone: {player.phoneNumber}
                            </Typography>
                            <Typography variant="body2">
                              Email: {player.email}
                            </Typography>
                            {player.checkInTime && (
                              <Typography variant="body2">
                                Checked in: {player.checkInTime}
                              </Typography>
                            )}
                          </Box>
                        }>
                          <span>{getDisplayName(player)}</span>
                        </Tooltip>
                      }
                      className={player.checkInTime ? 'checked-in' : player.isConfirmed ? 'confirmed' : 'pending'}
                      onClick={(e) => handlePlayerAction(player.id, slot.militaryTime, e)}
                      variant="filled"
                      sx={{
                        transform: 'scale(1)',
                        transition: 'transform 0.2s ease',
                        '&:hover': {
                          transform: 'scale(1.05)'
                        }
                      }}
                    />
                  ))}
                </Box>
              </Grid>
            </AnimatedTimeSlotRow>
          ))}
        </AnimatePresence>
      </TableContainer>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleConfirm}>
          <CheckCircleIcon sx={{ mr: 1 }} color="success" />
          Confirm Player
        </MenuItem>
        <MenuItem onClick={handleCheckIn}>
          <AccessTimeIcon sx={{ mr: 1 }} color="primary" />
          Check In
        </MenuItem>
        <MenuItem onClick={() => {
          setIsMessageDialogOpen(true);
          setAnchorEl(null);
          handleMenuClose();
        }}>
          <EmailIcon sx={{ mr: 1 }} color="info" />
          Send Message
        </MenuItem>
        <MenuItem onClick={handleCancelBooking}>
          <CancelIcon sx={{ mr: 1 }} color="error" />
          Cancel Booking
        </MenuItem>
        <MenuItem onClick={handleEditPlayer}>
          <EditIcon sx={{ mr: 1 }} color="info" />
          Edit Details
        </MenuItem>
      </Menu>

      <PlayerEditForm
        open={isAddPlayerDialogOpen}
        onClose={handleAddPlayerDialogClose}
        onSave={(playerData) => selectedSlot && handleAddPlayer(selectedSlot, playerData)}
        title={`Add Player to ${selectedSlot?.time}`}
        mode="add"
      />

      <PlayerEditForm
        open={isEditPlayerDialogOpen}
        onClose={handleEditPlayerDialogClose}
        onSave={handleUpdatePlayer}
        initialData={getSelectedPlayerData()}
        title={`Edit Player Details - ${getDisplayName(getSelectedPlayerData())}`}
        mode="edit"
      />

      {selectedPlayer && (
        <MessageDialog
          open={isMessageDialogOpen}
          onClose={() => setIsMessageDialogOpen(false)}
          onSend={handleSendMessage}
          playerName={getDisplayName(getSelectedPlayerData())}
          playerEmail={getSelectedPlayerData()?.email || ''}
          playerPhone={getSelectedPlayerData()?.phoneNumber || ''}
          teeTime={mockSlots.find((s: TeeTimeSlot) => s.militaryTime === selectedPlayer.time)?.time || ''}
        />
      )}

      <Snackbar
        open={!!snackbarMessage}
        autoHideDuration={3000}
        onClose={() => setSnackbarMessage('')}
      >
        <Alert
          onClose={() => setSnackbarMessage('')}
          severity="success"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}; 
