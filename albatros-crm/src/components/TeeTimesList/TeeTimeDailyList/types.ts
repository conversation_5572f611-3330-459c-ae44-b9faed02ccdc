import { TeeTimeData } from '../types';

/**
 * Props for the TeeTimeDailyList component
 */
export interface TeeTimeDailyListProps {
  /** The ID of the golf course */
  courseId: string;
  /** The date to display */
  date?: Date;
  /** Array of tee time data for the day */
  data?: TeeTimeData[];
  /** Optional callback when a time slot is clicked */
  onSlotClick?: (slot: TeeTimeSlot) => void;
  /** Optional callback when a player confirms a slot */
  onPlayerConfirm?: (playerId: string, slotTime: string) => void;
  /** Optional callback when a player checks in */
  onPlayerCheckIn?: (playerId: string, slotTime: string) => void;
  /** Optional className for additional styling */
  className?: string;
}

/**
 * Data structure for a tee time item in the daily view
 */
export interface DailyTeeTimeItem {
  /** Unique identifier for the tee time */
  id: number;
  /** URL of the image associated with the tee time */
  image: string;
  /** Title of the tee time */
  title: string;
  /** Description of the tee time */
  description: string;
  /** Number of booked slots */
  booked: number;
  /** Number of available slots */
  available: number;
  /** Total number of slots */
  total: number;
  /** Time of the tee time in AM/PM format */
  time: string;
  /** Internal military time format */
  militaryTime: string;
  /** Hour of the tee time (0-23) */
  hour: number;
  /** Whether this is a peak time slot */
  isPeak: boolean;
  /** Utilization percentage */
  utilization: number;
  /** Period of the day (Morning, Afternoon, Evening) */
  period?: string;
}

/**
 * Data structure for grouped tee time items
 */
export interface GroupedTeeTimeData {
  period: string;
  items: DailyTeeTimeItem[];
}

export interface Player {
  id: string;
  firstName: string;
  lastName: string;
  isConfirmed: boolean;
  checkInTime?: string;
  phoneNumber?: string;
  email?: string;
}

export interface TeeTimeSlot {
  time: string;
  militaryTime: string;
  cost: number;
  players: Player[];
  isPeak: boolean;
  maxPlayers: number;
}

export interface TeeTimeGroup {
  hour: number;
  slots: TeeTimeSlot[];
} 