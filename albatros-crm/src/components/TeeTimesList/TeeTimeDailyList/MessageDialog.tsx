import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert
} from '@mui/material';
import { Email, Sms } from '@mui/icons-material';

export interface MessageDialogProps {
  open: boolean;
  onClose: () => void;
  onSend: (type: 'email' | 'sms', message: string) => void;
  playerName: string;
  playerEmail: string;
  playerPhone: string;
  teeTime: string;
}

const MESSAGE_TEMPLATES = {
  email: {
    checkIn: (name: string, time: string) => 
      `Dear ${name},\n\nThis is a friendly reminder about your upcoming tee time at ${time}. ` +
      `Please remember to check in when you arrive.\n\nBest regards,\nGolf Club Staff`,
    reminder: (name: string, time: string) =>
      `Dear ${name},\n\nJust a reminder that you have a tee time scheduled for ${time}. ` +
      `We look forward to seeing you!\n\nBest regards,\nGolf Club Staff`
  },
  sms: {
    checkIn: (name: string, time: string) =>
      `Hi ${name}! Please remember to check in for your ${time} tee time when you arrive.`,
    reminder: (name: string, time: string) =>
      `Hi ${name}! Just a reminder about your tee time at ${time}. See you soon!`
  }
};

export const MessageDialog: React.FC<MessageDialogProps> = ({
  open,
  onClose,
  onSend,
  playerName,
  playerEmail,
  playerPhone,
  teeTime
}) => {
  const [messageType, setMessageType] = useState<'email' | 'sms'>('email');
  const [templateType, setTemplateType] = useState<'checkIn' | 'reminder'>('reminder');
  const [message, setMessage] = useState('');

  const handleTemplateChange = (template: 'checkIn' | 'reminder') => {
    setTemplateType(template);
    setMessage(MESSAGE_TEMPLATES[messageType][template](playerName, teeTime));
  };

  const handleTypeChange = (type: 'email' | 'sms') => {
    setMessageType(type);
    setMessage(MESSAGE_TEMPLATES[type][templateType](playerName, teeTime));
  };

  React.useEffect(() => {
    if (open) {
      setTemplateType('reminder');
      setMessage(MESSAGE_TEMPLATES[messageType]['reminder'](playerName, teeTime));
    }
  }, [open, messageType, playerName, teeTime]);

  const handleSend = () => {
    onSend(messageType, message);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Send Message to {playerName}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3, mt: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {messageType === 'email' ? `Email: ${playerEmail}` : `Phone: ${playerPhone}`}
          </Typography>
          <Alert severity="info" sx={{ mt: 1 }}>
            Tee Time: {teeTime}
          </Alert>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <FormControl fullWidth size="small">
            <InputLabel>Message Type</InputLabel>
            <Select
              value={messageType}
              label="Message Type"
              onChange={(e) => handleTypeChange(e.target.value as 'email' | 'sms')}
            >
              <MenuItem value="email">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Email fontSize="small" />
                  <span>Email</span>
                </Box>
              </MenuItem>
              <MenuItem value="sms">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Sms fontSize="small" />
                  <span>SMS</span>
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth size="small">
            <InputLabel>Template</InputLabel>
            <Select
              value={templateType}
              label="Template"
              onChange={(e) => handleTemplateChange(e.target.value as 'checkIn' | 'reminder')}
            >
              <MenuItem value="reminder">General Reminder</MenuItem>
              <MenuItem value="checkIn">Check-in Reminder</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <TextField
          fullWidth
          multiline
          rows={6}
          label="Message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          variant="outlined"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleSend} 
          variant="contained" 
          startIcon={messageType === 'email' ? <Email /> : <Sms />}
        >
          Send {messageType === 'email' ? 'Email' : 'SMS'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 