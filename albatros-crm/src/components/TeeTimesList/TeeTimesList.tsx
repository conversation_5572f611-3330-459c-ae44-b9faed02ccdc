import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  styled,
} from '@mui/material';
import { TeeTimeWeeklyList } from './TeeTimeWeeklyList';
import { TeeTimeMonthlyList } from './TeeTimeMonthlyList';
import { TeeTimesListProps, TeeTimeData, ViewType } from './types';
import { ViewToggle } from './ViewToggle';
import { TeeTimeDailyList } from './TeeTimeDailyList';
import { generateMockDataForDays } from './utils';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
}));

const ViewHeader = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  width: '100%'
}));

const ViewToggleContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '50%',
  transform: 'translateX(-50%)',
  display: 'flex',
  justifyContent: 'center',
  zIndex: 1
}));

// Update TeeTimeData interface to include revenue and utilization
interface ExtendedTeeTimeData extends TeeTimeData {
  revenue: number;
  utilization: number;
}

export const TeeTimesList: React.FC<TeeTimesListProps> = ({ 
  courseId,
  timeView = 'daily',
  onTeeTimeSelect,
  onSlotClick 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentView, setCurrentView] = useState<ViewType>(timeView);
  const [data, setData] = useState<ExtendedTeeTimeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // TODO: Replace with actual API call
        const mockData = generateMockDataForDays(30) as ExtendedTeeTimeData[];
        setData(mockData);
      } catch (err) {
        setError('Failed to fetch tee time data');
        console.error(err);
      } finally {
        setLoading(false);
        // Scroll after data loads
        setTimeout(() => {
          containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100);
      }
    };

    fetchData();
  }, [courseId]);

  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
    setTimeout(() => {
      containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
  };

  const handleDailySlotClick = useCallback((slot: any) => {
    console.log('Daily slot clicked:', slot);
    if (onSlotClick) {
      onSlotClick(slot);
    }
  }, [onSlotClick]);

  const handleWeeklyMonthlySlotClick = useCallback((date: string, time: string) => {
    console.log('Weekly/Monthly slot clicked:', { date, time });
    // Convert to the format expected by the parent component
    const slot = {
      time,
      date,
      // Add any other required properties
    };
    if (onSlotClick) {
      onSlotClick(slot);
    }
  }, [onSlotClick]);

  const renderView = () => {
    switch (currentView) {
      case 'daily':
        return (
          <TeeTimeDailyList
            data={data}
            date={new Date()}
            courseId={courseId}
            onSlotClick={handleDailySlotClick}
          />
        );
      case 'weekly':
        return (
          <TeeTimeWeeklyList
            data={data}
            onSlotClick={handleWeeklyMonthlySlotClick}
            courseId={courseId}
            startDate={new Date()}
            endDate={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)}
          />
        );
      case 'monthly':
        return (
          <TeeTimeMonthlyList
            data={data}
            onSlotClick={handleWeeklyMonthlySlotClick}
            courseId={courseId}
            month={new Date()}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return <Typography>Loading...</Typography>;
  }

  if (error) {
    return <Typography color="error">{error}</Typography>;
  }

  return (
    <Box ref={containerRef}>
      <Grid container spacing={3}>
        <Grid item xs={12} sx={{ mt: 4 }}>
          <ViewHeader>
            <Typography variant="h5" sx={{ zIndex: 2 }}>Tee Times</Typography>
            <ViewToggleContainer>
              <ViewToggle
                currentView={currentView}
                onViewChange={handleViewChange}
                size="small"
              />
            </ViewToggleContainer>
          </ViewHeader>
          <StyledPaper>
            {renderView()}
          </StyledPaper>
        </Grid>
      </Grid>
    </Box>
  );
};
export default TeeTimesList;
