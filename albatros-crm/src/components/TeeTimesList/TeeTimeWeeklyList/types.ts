import { TeeTimeData } from '../types';

/**
 * Props for the TeeTimeWeeklyList component
 */
export interface TeeTimeWeeklyListProps {
  /** The ID of the golf course */
  courseId: string;
  /** Array of tee time data for the week */
  data: TeeTimeData[];
  /** The start date of the week */
  startDate: Date;
  /** The end date of the week */
  endDate: Date;
  /** Optional callback when a time slot is clicked */
  onSlotClick?: (date: string, time: string) => void;
  /** Optional className for additional styling */
  className?: string;
}

/**
 * Data structure for a week's data
 */
export interface WeekData {
  /** The start date of the week formatted as "Month Day" */
  weekOf: string;
  /** Array of day data */
  days: DayData[];
}

/**
 * Data structure for a day's data
 */
export interface DayData {
  /** The day of the week (e.g., "Monday") */
  day: string;
  /** The date formatted as "MMM DD" (e.g., "Jan 15") */
  date: string;
  /** Array of time slots for the day */
  slots: TeeTimeData['slots'];
} 