import { Box, Typography, Grid, styled } from '@mui/material';
import { alpha } from '@mui/material/styles';

export const WeekContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
}));

export const WeekHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

export const WeekTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.primary,
  fontWeight: 600,
}));

export const SummaryGrid = styled(Grid)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

export const SummaryCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: alpha(theme.palette.primary.main, 0.05),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
}));

export const SummaryTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  marginBottom: theme.spacing(1),
}));

export const SummaryValue = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.primary,
  fontWeight: 600,
}));

export const StatCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

export const PopularTimesContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: theme.spacing(2),
  gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
  marginTop: theme.spacing(2),
}));

export const PopularTimeCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
}));

export const TimeTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(1),
}));

export const TimeDetails = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

interface UtilizationBadgeProps {
  utilization: 'High' | 'Medium' | 'Low';
}

export const UtilizationBadge = styled('span')<UtilizationBadgeProps>(({ theme, utilization }) => {
  const getColor = () => {
    switch (utilization) {
      case 'High':
        return theme.palette.success.main;
      case 'Medium':
        return theme.palette.warning.main;
      case 'Low':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const backgroundColor = alpha(getColor(), 0.1);
  const color = getColor();

  return {
    padding: '4px 8px',
    borderRadius: '12px',
    fontSize: '0.75rem',
    fontWeight: 500,
    backgroundColor,
    color,
  };
}); 