import React, { useMemo, useState } from 'react';
import { 
  Typography, 
  Box, 
  Grid, 
  Paper,
  LinearProgress,
  Chip,
  Skeleton,
  Fade
} from '@mui/material';
import { 
  TrendingUp, 
  TrendingDown,
  AccessTime,
  Group,
  AttachMoney,
  Speed,
  Today,
  TrendingFlat
} from '@mui/icons-material';
import { TeeTimeWeeklyListProps } from './types';

const MetricCard = ({ title, value, icon, trend, color = 'primary', subtitle = '' }: any) => (
  <Paper sx={{ p: 2, height: '100%' }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <Box sx={{ 
        backgroundColor: `${color}.light`,
        borderRadius: '50%',
        p: 1,
        mr: 2
      }}>
        {icon}
      </Box>
      <Typography variant="subtitle2" color="text.secondary">
        {title}
      </Typography>
    </Box>
    <Typography variant="h4" sx={{ mb: 1 }}>
      {value}
    </Typography>
    {subtitle && (
      <Typography variant="body2" color="text.secondary">
        {subtitle}
      </Typography>
    )}
    {trend !== undefined && (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {trend > 0 ? (
          <TrendingUp color="success" fontSize="small" />
        ) : trend < 0 ? (
          <TrendingDown color="error" fontSize="small" />
        ) : (
          <TrendingFlat color="action" fontSize="small" />
        )}
        <Typography 
          variant="body2" 
          color={trend > 0 ? 'success.main' : trend < 0 ? 'error.main' : 'text.secondary'} 
          sx={{ ml: 0.5 }}
        >
          {trend === 0 ? 'No change' : `${Math.abs(trend)}% vs prev week`}
        </Typography>
      </Box>
    )}
  </Paper>
);

const TimeSlotBar = ({ time, utilization, bookings, total }: any) => (
  <Box sx={{ mb: 2 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
      <Typography variant="body2">{time}</Typography>
      <Typography variant="body2" color="text.secondary">
        {bookings}/{total} slots booked
      </Typography>
    </Box>
    <Box sx={{ width: '100%', position: 'relative' }}>
      <LinearProgress 
        variant="determinate" 
        value={utilization} 
        sx={{ 
          height: 8, 
          borderRadius: 4,
          backgroundColor: 'action.hover',
          '& .MuiLinearProgress-bar': {
            backgroundColor: utilization > 80 ? 'success.main' : utilization > 50 ? 'warning.main' : 'error.main'
          }
        }} 
      />
    </Box>
  </Box>
);

const DayPerformanceBar = ({ day, utilization, isToday }: any) => (
  <Box sx={{ mb: 2 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography variant="body2" sx={{ mr: 1 }}>{day}</Typography>
        {isToday && (
          <Chip label="Today" size="small" color="primary" />
        )}
      </Box>
      <Typography variant="body2" color="text.secondary">
        {utilization}% utilized
      </Typography>
    </Box>
    <LinearProgress 
      variant="determinate" 
      value={utilization} 
      sx={{ 
        height: 6, 
        borderRadius: 3,
        backgroundColor: 'action.hover',
        '& .MuiLinearProgress-bar': {
          backgroundColor: utilization > 80 ? 'success.main' : utilization > 50 ? 'warning.main' : 'error.main'
        }
      }} 
    />
  </Box>
);

const WeekSkeleton = () => (
  <Fade in={true}>
    <Paper sx={{ mb: 4, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Skeleton variant="circular" width={24} height={24} sx={{ mr: 2 }} />
          <Skeleton variant="text" width={200} height={32} />
        </Box>
        <Skeleton variant="rounded" width={120} height={32} />
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[...Array(4)].map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
                <Skeleton variant="text" width={100} />
              </Box>
              <Skeleton variant="text" width={80} height={48} />
              <Skeleton variant="text" width={120} />
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Skeleton variant="text" width={200} height={32} sx={{ mb: 2 }} />
            {[...Array(7)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Skeleton variant="text" width={100} />
                  <Skeleton variant="text" width={60} />
                </Box>
                <Skeleton variant="rounded" width="100%" height={6} />
              </Box>
            ))}
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Skeleton variant="text" width={150} height={32} sx={{ mb: 2 }} />
            {[...Array(5)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Skeleton variant="text" width={80} />
                  <Skeleton variant="text" width={60} />
                </Box>
                <Skeleton variant="rounded" width="100%" height={8} />
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Paper>
  </Fade>
);

// Helper functions for mock data generation
const generatePopularTimes = (baseUtilization: number) => {
  const times = ['7:00 AM', '8:00 AM', '9:00 AM', '2:00 PM', '3:00 PM'];
  return times.map(time => {
    const variation = Math.floor(Math.random() * 20) - 10; // ±10% variation
    const utilization = Math.min(Math.max(baseUtilization + variation, 20), 100);
    const total = 20; // slots per time
    const booked = Math.floor((total * utilization) / 100);
    return {
      time,
      booked,
      available: total - booked,
      utilization
    };
  });
};

const generateDailyUtilization = (baseUtilization: number) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days.map(day => {
    const variation = Math.floor(Math.random() * 20) - 10; // ±10% variation
    const utilization = Math.min(Math.max(baseUtilization + variation, 20), 100);
    return {
      day,
      utilization
    };
  }).sort((a, b) => b.utilization - a.utilization);
};

const generateBusyDays = (baseUtilization: number) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days.filter(() => baseUtilization > 70 && Math.random() > 0.5);
};

export const TeeTimeWeeklyList: React.FC<TeeTimeWeeklyListProps> = ({
  courseId,
  data,
  startDate,
  endDate,
  onSlotClick,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentWeekIndex, setCurrentWeekIndex] = useState(0);

  const weekData = useMemo(() => {
    const weeks = [];
    const currentDate = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    // Start from 6 months ago and work up to current week
    let weekStart = new Date(sixMonthsAgo);
    weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // Start from Sunday

    while (weekStart <= currentDate) {
      // Generate more realistic mock data based on how recent the week is
      const isRecentWeek = new Date(weekStart).getMonth() === currentDate.getMonth();
      const baseUtilization = isRecentWeek ? 75 : 60; // Higher utilization for recent weeks
      const randomVariation = Math.floor(Math.random() * 30) - 15; // ±15% variation
      const utilization = Math.min(Math.max(baseUtilization + randomVariation, 30), 95); // Keep between 30-95%
      
      const totalSlots = 200;
      const totalBookings = Math.floor((totalSlots * utilization) / 100);

      weeks.push({
        weekStart: new Date(weekStart),
        weekOf: weekStart.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
      }),
        totalBookings,
        totalSlots,
        revenue: totalBookings * 100,
        peakTimeBookings: Math.floor(totalBookings * 0.4),
        utilization,
        popularTimes: generatePopularTimes(utilization),
        dailyUtilization: generateDailyUtilization(utilization),
        busyDays: generateBusyDays(utilization),
      });

      // Move to next week
      weekStart.setDate(weekStart.getDate() + 7);
    }

    return weeks.sort((a, b) => b.weekStart.getTime() - a.weekStart.getTime()); // Sort newest first
  }, []);

  const handleWeekChange = (direction: 'next' | 'prev') => {
    // Only proceed if we can actually change weeks
    if ((direction === 'next' && currentWeekIndex <= 0) || 
        (direction === 'prev' && currentWeekIndex >= weekData.length - 1)) {
      return;
    }
    
    setIsLoading(true);
    
    // Short delay to simulate data loading
    setTimeout(() => {
      if (direction === 'next') {
        setCurrentWeekIndex(prev => prev - 1);
      } else {
        setCurrentWeekIndex(prev => prev + 1);
      }
      setIsLoading(false);
    }, 300);
  };

  const currentWeek = weekData[currentWeekIndex];

  if (!currentWeek) {
  return (
      <Box sx={{ p: 3 }}>
        <Typography>No data available for this week</Typography>
      </Box>
    );
  }

  const WeekContent = () => (
      <Fade in={!isLoading} timeout={200}>
        <Box>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard
                    title="Total Bookings"
              value={currentWeek.totalBookings}
                    icon={<Group color="primary" />}
                    trend={5}
              subtitle={`${currentWeek.busyDays.length} busy days`}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard
                    title="Revenue"
              value={`$${currentWeek.revenue.toLocaleString()}`}
                    icon={<AttachMoney color="success" />}
                    trend={8}
                    color="success"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard
                    title="Utilization"
              value={`${currentWeek.utilization}%`}
                    icon={<Speed color="warning" />}
                    trend={0}
                    color="warning"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard
                    title="Peak Time Bookings"
              value={currentWeek.peakTimeBookings}
                    icon={<AccessTime color="info" />}
                    trend={-3}
                    color="info"
                  />
                </Grid>
              </Grid>

              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Paper sx={{ p: 2, height: '100%' }}>
                    <Typography variant="h6" gutterBottom>
                      Daily Performance
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                {currentWeek.dailyUtilization.map((day: any) => (
                        <DayPerformanceBar 
                          key={day.day}
                          day={day.day}
                          utilization={day.utilization}
                          isToday={day.day === new Date().toLocaleDateString('en-US', { weekday: 'long' })}
                        />
                      ))}
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, height: '100%' }}>
                    <Typography variant="h6" gutterBottom>
                      Popular Time Slots
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                {currentWeek.popularTimes.map((time: any) => (
                        <TimeSlotBar 
                          key={time.time}
                          time={time.time}
                          utilization={time.utilization}
                          bookings={time.booked}
                          total={time.booked + time.available}
                  />
                ))}
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
        </Box>
      </Fade>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          Weekly Performance Overview
        </Typography>
      </Box>

      <Paper sx={{ mb: 4, p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Today sx={{ mr: 2 }} color="primary" />
            <Typography variant="h5">
              Week of {currentWeek.weekOf}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography 
              variant="button" 
              onClick={() => handleWeekChange('prev')} 
              sx={{ 
                cursor: currentWeekIndex >= weekData.length - 1 ? 'not-allowed' : 'pointer',
                opacity: currentWeekIndex >= weekData.length - 1 ? 0.5 : 1,
                userSelect: 'none',
              }}
            >
              ← Previous Week
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip 
                label={currentWeek.utilization > 70 ? 'High Activity' : 
                       currentWeek.utilization > 50 ? 'Moderate Activity' : 'Low Activity'}
                color={currentWeek.utilization > 70 ? 'success' : 
                       currentWeek.utilization > 50 ? 'warning' : 'error'}
              />
              <Typography 
                variant="button" 
                onClick={() => handleWeekChange('next')} 
                sx={{ 
                  cursor: currentWeekIndex <= 0 ? 'not-allowed' : 'pointer',
                  opacity: currentWeekIndex <= 0 ? 0.5 : 1,
                  userSelect: 'none'
                }}
              >
                Next Week →
              </Typography>
            </Box>
          </Box>
        </Box>

        {isLoading ? <WeekSkeleton /> : <WeekContent />}
      </Paper>
    </Box>
  );
}; 