import { styled } from '@mui/material';
import { ToggleButtonGroup } from '@mui/material';

export const ViewToggleContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
}));

export const StyledToggleButtonGroup = styled(ToggleButtonGroup, {
  shouldForwardProp: (prop) => prop !== 'showIcons',
})<{ showIcons?: boolean }>(({ theme, showIcons = true }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius * 4,
  padding: '4px',
  boxShadow: theme.shadows[1],
  transition: theme.transitions.create(['box-shadow'], {
    duration: theme.transitions.duration.shorter,
  }),
  '&:hover': {
    boxShadow: theme.shadows[2],
  },
  '& .MuiToggleButton-root': {
    border: 'none',
    borderRadius: theme.shape.borderRadius * 3,
    marginLeft: 0,
    marginRight: 0,
    padding: theme.spacing(0.75, 2),
    color: theme.palette.text.secondary,
    fontSize: '0.875rem',
    fontWeight: 500,
    textTransform: 'none',
    transition: theme.transitions.create(['background-color', 'color'], {
      duration: theme.transitions.duration.shorter,
    }),
    '&.Mui-selected': {
      backgroundColor: theme.palette.grey[300],
      color: theme.palette.text.primary,
      '&:hover': {
        backgroundColor: theme.palette.grey[400],
      },
    },
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    '&.Mui-disabled': {
      opacity: 0.5,
    },
    '& .MuiSvgIcon-root': {
      marginRight: showIcons ? theme.spacing(1) : 0,
    },
  },
}));

export const ToggleButtonContent = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
})); 