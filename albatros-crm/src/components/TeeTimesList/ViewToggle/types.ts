import { ViewType } from '../types';

/**
 * Available sizes for the toggle buttons
 */
export type ToggleSize = 'small' | 'medium' | 'large';

/**
 * Props for the ViewToggle component
 */
export interface ViewToggleProps {
  /** The currently selected view */
  currentView: ViewType;
  /** Callback when the view changes */
  onViewChange: (view: ViewType) => void;
  /** Optional flag to disable the toggle */
  disabled?: boolean;
  /** Optional size for the toggle buttons */
  size?: ToggleSize;
  /** Optional className for additional styling */
  className?: string;
  /** Optional flag to show icons in the buttons */
  showIcons?: boolean;
  /** Optional flag to show labels in the buttons */
  showLabels?: boolean;
  /** Optional flag to make the toggle full width */
  fullWidth?: boolean;
} 