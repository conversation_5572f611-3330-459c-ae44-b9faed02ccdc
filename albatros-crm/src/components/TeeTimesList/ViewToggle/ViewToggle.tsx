import React, { useMemo } from 'react';
import { ToggleButton } from '@mui/material';
import {
  ViewDay as DailyIcon,
  ViewWeek as WeeklyIcon,
  CalendarMonth as MonthlyIcon,
} from '@mui/icons-material';
import { ViewToggleProps } from './types';
import { StyledToggleButtonGroup, ToggleButtonContent } from './styles';

const VIEW_OPTIONS = [
  {
    value: 'daily',
    label: 'Daily View',
    icon: <DailyIcon />,
  },
  {
    value: 'weekly',
    label: 'Weekly View',
    icon: <WeeklyIcon />,
  },
  {
    value: 'monthly',
    label: 'Monthly View',
    icon: <MonthlyIcon />,
  },
] as const;

export const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onViewChange,
  disabled = false,
  size = 'medium',
  className,
  showIcons = true,
  showLabels = true,
}) => {
  const handleChange = useMemo(() => {
    return (_: React.MouseEvent<HTMLElement>, newView: string | null) => {
      if (newView && newView !== currentView) {
        onViewChange(newView as 'weekly' | 'monthly');
      }
    };
  }, [onViewChange, currentView]);

  return (
    <StyledToggleButtonGroup
      value={currentView}
      exclusive
      onChange={handleChange}
      disabled={disabled}
      size={size}
      className={className}
      showIcons={showIcons}
      aria-label="view toggle"
      sx={{ userSelect: 'none' }}
    >
      {VIEW_OPTIONS.map(({ value, label, icon }) => (
        <ToggleButton
          key={value}
          value={value}
          aria-label={label.toLowerCase()}
          sx={{ userSelect: 'none' }}
        >
          <ToggleButtonContent>
            {showIcons && icon}
            {showLabels && label}
          </ToggleButtonContent>
        </ToggleButton>
      ))}
    </StyledToggleButtonGroup>
  );
}; 