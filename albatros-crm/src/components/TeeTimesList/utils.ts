import { TeeTimeData, TimeSlot } from './types';

const convertToAmPm = (militaryTime: string): string => {
  const [hours, minutes] = militaryTime.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
};

const isHolidayDate = (date: Date): boolean => {
  const holidays = [
    '2024-01-01', // New Year's Day
    '2024-07-04', // Independence Day
    '2024-12-25', // Christmas
    // Add more holidays as needed
  ];
  return holidays.includes(date.toISOString().split('T')[0]);
};

export const generateMockDataForDays = (days: number): TeeTimeData[] => {
  const data: TeeTimeData[] = [];
  const today = new Date();

  for (let i = 0; i < days; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const isHoliday = isHolidayDate(date);
    const dateString = date.toISOString().split('T')[0];
    const slots: TimeSlot[] = [];
    let totalBookings = 0;
    let totalUtilization = 0;

    // Generate time slots for the day
    for (let hour = 7; hour <= 19; hour++) {
      const isPeakHour = hour >= 9 && hour <= 16;
      const maxBookings = isPeakHour ? 8 : 6;
      const baseBookings = isWeekend || isHoliday
        ? Math.floor(maxBookings * 0.8) // Higher base bookings for weekends/holidays
        : Math.floor(maxBookings * 0.4); // Lower base bookings for weekdays

      // Add some random variation
      const randomFactor = 0.8 + Math.random() * 0.4; // Random factor between 0.8 and 1.2
      const bookings = Math.min(
        Math.floor(baseBookings * randomFactor),
        maxBookings
      );

      const militaryTime = `${hour.toString().padStart(2, '0')}:00`;
      const slot: TimeSlot = {
        time: convertToAmPm(militaryTime),
        militaryTime,
        isBooked: bookings === maxBookings,
        isPeak: isPeakHour,
        price: isPeakHour ? 150 : 120,
        bookings,
        maxBookings,
        utilization: Math.round((bookings / maxBookings) * 100),
        booked: bookings,
        total: maxBookings,
      };

      slots.push(slot);
      totalBookings += bookings;
      totalUtilization += slot.utilization;
    }

    const averageUtilization = Math.round(totalUtilization / slots.length);

    data.push({
      id: `day-${i}`,
      date: dateString,
      totalBookings,
      utilization: averageUtilization,
      slots,
      image: '/default-tee-time.jpg',
      title: `${date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })} Tee Times`,
      description: `${totalBookings} bookings for ${slots.length} time slots`,
      booked: totalBookings,
      available: slots.reduce((acc, slot) => acc + (slot.maxBookings - slot.bookings), 0),
      total: slots.reduce((acc, slot) => acc + slot.maxBookings, 0),
    });
  }

  return data;
}; 