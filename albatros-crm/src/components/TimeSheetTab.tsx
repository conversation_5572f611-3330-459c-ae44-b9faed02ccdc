import React, { useState } from 'react';
import styles from './BackOffice.module.css';

const employeeShifts = [
  { name: '<PERSON>', role: 'General Manager', clockIn: '08:00 AM', clockOut: '04:00 PM', hours: '8 hrs', status: 'Approved' },
  { name: '<PERSON>', role: 'Superintendent', clockIn: '07:30 AM', clockOut: '03:30 PM', hours: '8 hrs', status: 'Pending' },
  { name: '<PERSON>', role: 'Beverage Specialist', clockIn: '10:00 AM', clockOut: '06:00 PM', hours: '8 hrs', status: 'Pending' },
  { name: '<PERSON><PERSON>', role: 'Grounds Keeper', clockIn: '06:00 AM', clockOut: '02:00 PM', hours: '8 hrs', status: 'Approved' },
];

// Required roles for the course
const REQUIRED_ROLES = [
  'General Manager',
  'Superintendent',
  'Beverage Specialist',
  'Grounds Keeper',
];

// Initial mock weekly shifts
const initialWeeklyShifts = [
  { name: '<PERSON>', role: 'General Manager', day: 'Monday', start: '08:00 AM', end: '04:00 PM' },
  { name: '<PERSON>', role: 'Superintendent', day: 'Monday', start: '07:30 AM', end: '03:30 PM' },
  { name: '<PERSON>', role: 'Beverage Specialist', day: 'Tuesday', start: '10:00 AM', end: '06:00 PM' },
  { name: '<PERSON>ie <PERSON>', role: 'Grounds Keeper', day: 'Wednesday', start: '06:00 AM', end: '02:00 PM' },
  { name: '<PERSON> Helms', role: 'General Manager', day: 'Thursday', start: '08:00 AM', end: '04:00 PM' },
  { name: 'Kyle Eastwood', role: 'Superintendent', day: 'Friday', start: '07:30 AM', end: '03:30 PM' },
  { name: 'Jennifer Lopez', role: 'Beverage Specialist', day: 'Saturday', start: '10:00 AM', end: '06:00 PM' },
];

// Mock employees
const employees = [
  { id: '1', name: 'Bruce Fly', role: 'Sous Chef' },
  { id: '2', name: 'Chase Matthews', role: 'General Manager' },
  { id: '3', name: 'Clayton Kennedy', role: 'Front of House' },
  { id: '4', name: 'Courtney Chan', role: 'Prep Cook' },
  { id: '5', name: 'Crystal Ferguson', role: 'Bartender' },
  { id: '6', name: 'Miranda Helms', role: 'General Manager' },
  { id: '7', name: 'Kyle Eastwood', role: 'Superintendent' },
  { id: '8', name: 'Jennifer Lopez', role: 'Beverage Specialist' },
  { id: '9', name: 'Scottie Woods', role: 'Grounds Keeper' },
];

const generateTimeOptions = () => {
  const times = [];
  let hour = 5;
  let minute = 0;
  while (hour < 22 || (hour === 22 && minute === 0)) {
    const h = hour % 12 === 0 ? 12 : hour % 12;
    const ampm = hour < 12 ? 'AM' : 'PM';
    const label = `${h.toString().padStart(2, '0')}:${minute === 0 ? '00' : '30'} ${ampm}`;
    times.push(label);
    if (minute === 0) {
      minute = 30;
    } else {
      minute = 0;
      hour++;
    }
  }
  return times;
};
const timeOptions = generateTimeOptions();

// Add mock employee availability data
const initialAvailability = employees.map(emp => ({
  id: emp.id,
  name: emp.name,
  role: emp.role,
  status: 'Active',
  availability: [
    { day: 'Sunday', available: false, start: '', end: '' },
    { day: 'Monday', available: true, start: '08:00 AM', end: '05:00 PM' },
    { day: 'Tuesday', available: true, start: '08:00 AM', end: '05:00 PM' },
    { day: 'Wednesday', available: true, start: '08:00 AM', end: '05:00 PM' },
    { day: 'Thursday', available: true, start: '08:00 AM', end: '05:00 PM' },
    { day: 'Friday', available: true, start: '08:00 AM', end: '05:00 PM' },
    { day: 'Saturday', available: false, start: '', end: '' },
  ]
}));

function getStartOfWeek(date = new Date()) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  const start = new Date(d.setDate(diff));
  start.setHours(0, 0, 0, 0);
  return start;
}
function formatDate(date: Date) {
  return date.toISOString().split('T')[0];
}
function addDays(date: Date, days: number) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

const TimeSheetTab: React.FC = () => {
  const weekDays = [
    'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
  ];
  const today = new Date();
  const [currentWeekStart, setCurrentWeekStart] = useState(getStartOfWeek(today));
  const [schedules, setSchedules] = useState<{ [week: string]: typeof initialWeeklyShifts }>({
    [formatDate(getStartOfWeek(today))]: initialWeeklyShifts
  });
  const weekKey = formatDate(currentWeekStart);
  const weeklyShifts = schedules[weekKey] || [];
  const [modalOpen, setModalOpen] = useState(false);
  const [modalDay, setModalDay] = useState('');
  const [form, setForm] = useState({ employeeId: '', start: '', end: '' });
  const [editingIdx, setEditingIdx] = useState<number | null>(null);
  const [employeeAvailability, setEmployeeAvailability] = useState(initialAvailability);
  const [availabilityModalOpen, setAvailabilityModalOpen] = useState(false);
  const [selectedEmployeeIdx, setSelectedEmployeeIdx] = useState<number | null>(null);

  // Add/Edit shift modal handlers
  const openModal = (day: string, shiftIdx: number | null = null) => {
    setModalDay(day);
    if (shiftIdx !== null) {
      const shift = weeklyShifts[shiftIdx];
      const emp = employees.find(e => e.name === shift.name);
      setForm({
        employeeId: emp ? emp.id : '',
        start: shift.start,
        end: shift.end,
      });
      setEditingIdx(shiftIdx);
    } else {
      setForm({ employeeId: '', start: '', end: '' });
      setEditingIdx(null);
    }
    setModalOpen(true);
  };
  const closeModal = () => setModalOpen(false);
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };
  const handleAddOrEditShift = (e: React.FormEvent) => {
    e.preventDefault();
    const emp = employees.find(e => e.id === form.employeeId);
    if (!emp) return;
    const newShift = { name: emp.name, role: emp.role, day: modalDay, start: form.start, end: form.end };
    if (editingIdx !== null) {
      setWeeklyShifts(weeklyShifts.map((s, i) => i === editingIdx ? newShift : s));
    } else {
      setWeeklyShifts([...weeklyShifts, newShift]);
    }
    setModalOpen(false);
  };
  const handleRemoveShift = (idx: number) => {
    setWeeklyShifts(weeklyShifts.filter((_, i) => i !== idx));
  };

  // For each day, check for missing roles
  const getMissingRoles = (day: string) => {
    const rolesScheduled = weeklyShifts.filter(s => s.day === day).map(s => s.role);
    return REQUIRED_ROLES.filter(role => !rolesScheduled.includes(role));
  };

  // Helper: get available employees for a day (not scheduled, available, and if editing, include current)
  const getAvailableEmployees = () => {
    const scheduledIds = weeklyShifts
      .filter(s => s.day === modalDay)
      .map(s => {
        const emp = employees.find(e => e.name === s.name);
        return emp ? emp.id : null;
      })
      .filter(Boolean);
    // Only employees available for this day
    const dayIdx = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
    ].indexOf(modalDay);
    if (editingIdx !== null) {
      const currentEmp = employees.find(e => e.id === form.employeeId);
      return employees.filter(e => {
        const avail = employeeAvailability.find(a => a.id === e.id)?.availability[dayIdx];
        return (
          (!scheduledIds.includes(e.id) || (currentEmp && e.id === currentEmp.id)) &&
          avail && avail.available
        );
      });
    }
    return employees.filter(e => {
      const avail = employeeAvailability.find(a => a.id === e.id)?.availability[dayIdx];
      return !scheduledIds.includes(e.id) && avail && avail.available;
    });
  };

  // Auto Create Schedule
  const handleAutoCreate = () => {
    const newShifts = [];
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    for (const day of days) {
      const dayIdx = days.indexOf(day);
      const scheduledIds: string[] = [];
      for (const role of REQUIRED_ROLES) {
        // Find available employees for this role, not already scheduled for this day
        const eligible = employees.filter(e => {
          const avail = employeeAvailability.find(a => a.id === e.id)?.availability[dayIdx];
          return (
            e.role === role &&
            avail && avail.available &&
            !scheduledIds.includes(e.id)
          );
        });
        if (eligible.length > 0) {
          const emp = eligible[0]; // Pick the first eligible
          const avail = employeeAvailability.find(a => a.id === emp.id)?.availability[dayIdx];
          newShifts.push({
            name: emp.name,
            role: emp.role,
            day,
            start: avail?.start || '08:00 AM',
            end: avail?.end || '05:00 PM',
          });
          scheduledIds.push(emp.id);
        }
      }
    }
    setWeeklyShifts(newShifts);
  };

  const openAvailabilityModal = (idx: number) => {
    setSelectedEmployeeIdx(idx);
    setAvailabilityModalOpen(true);
  };
  const closeAvailabilityModal = () => setAvailabilityModalOpen(false);
  const handleAvailabilityChange = (dayIdx: number, field: 'available' | 'start' | 'end', value: any) => {
    if (selectedEmployeeIdx === null) return;
    setEmployeeAvailability(prev => prev.map((emp, idx) => {
      if (idx !== selectedEmployeeIdx) return emp;
      const newAvail = emp.availability.map((a, i) =>
        i === dayIdx ? { ...a, [field]: field === 'available' ? value === 'true' : value } : a
      );
      return { ...emp, availability: newAvail };
    }));
  };

  // Week navigation
  const handlePrevWeek = () => setCurrentWeekStart(addDays(currentWeekStart, -7));
  const handleNextWeek = () => setCurrentWeekStart(addDays(currentWeekStart, 7));

  // Update schedule for the current week
  const setWeeklyShifts = (shifts: typeof initialWeeklyShifts) => {
    setSchedules(prev => ({ ...prev, [weekKey]: shifts }));
  };

  return (
    <div className={styles.summaryTab}>
      <h2 className={styles.heading}>Employee Shifts</h2>
      <div className={styles.shiftsTableWrapper}>
        <table className={styles.shiftsTable}>
          <thead>
            <tr>
              <th>Employee</th>
              <th>Clock In</th>
              <th>Clock Out</th>
              <th>Hours</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {employeeShifts.map((shift, idx) => (
              <tr key={idx}>
                <td>
                  <div className={styles.employeeInfo}>
                    <div className={styles.avatar}>{shift.name[0]}</div>
                    <div>
                      <div className={styles.employeeName}>{shift.name}</div>
                      <div className={styles.employeeRole}>{shift.role}</div>
                    </div>
                  </div>
                </td>
                <td>{shift.clockIn}</td>
                <td>{shift.clockOut}</td>
                <td>{shift.hours}</td>
                <td>
                  <span className={
                    shift.status === 'Approved' ? styles.statusApproved : styles.statusPending
                  }>
                    {shift.status}
                  </span>
                </td>
                <td>
                  <span className={styles.actionIcon}>✔️</span>
                  <span className={styles.actionIcon}>❌</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Schedule Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
        <h2 className={styles.heading} style={{ marginBottom: 0 }}>Schedule</h2>
        <button
          style={{ background: '#38bdf8', color: '#fff', border: 'none', borderRadius: 8, padding: '8px 20px', fontWeight: 600, fontSize: '1rem', cursor: 'pointer' }}
          onClick={handleAutoCreate}
        >
          Auto Create
        </button>
        <button
          style={{ background: '#f1f5f9', color: '#6366f1', border: 'none', borderRadius: 8, padding: '8px 16px', fontWeight: 600, fontSize: '1rem', cursor: 'pointer' }}
          onClick={handlePrevWeek}
        >
          Previous Week
        </button>
        <button
          style={{ background: '#f1f5f9', color: '#6366f1', border: 'none', borderRadius: 8, padding: '8px 16px', fontWeight: 600, fontSize: '1rem', cursor: 'pointer' }}
          onClick={handleNextWeek}
        >
          Next Week
        </button>
        <span style={{ fontWeight: 500, color: '#64748b', marginLeft: 12 }}>
          {formatDate(currentWeekStart)} - {formatDate(addDays(currentWeekStart, 6))}
        </span>
      </div>
      <div style={{ background: '#fff', borderRadius: 12, boxShadow: '0 1px 4px rgba(0,0,0,0.04)', padding: 24, marginTop: 8 }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 16 }}>
          {weekDays.map((day: string) => {
            const shiftsForDay = weeklyShifts.filter(s => s.day === day);
            const missingRoles = getMissingRoles(day);
            return (
              <div key={day} style={{ minWidth: 0 }}>
                <div style={{ fontWeight: 600, color: '#6366f1', marginBottom: 8, textAlign: 'center' }}>{day}</div>
                {missingRoles.length > 0 && (
                  <div style={{ background: '#fee2e2', color: '#b91c1c', borderRadius: 6, padding: '6px 8px', fontSize: 13, marginBottom: 8, textAlign: 'center', fontWeight: 500 }}>
                    Missing: {missingRoles.join(', ')}
                  </div>
                )}
                {shiftsForDay.length === 0 && (
                  <div style={{ color: '#b0b7c3', fontSize: 13, textAlign: 'center', marginBottom: 8 }}>No Shifts</div>
                )}
                {shiftsForDay.map((shift, idx) => (
                  <div key={idx} style={{
                    background: '#f1f5f9',
                    borderRadius: 8,
                    padding: '12px 10px',
                    marginBottom: 10,
                    boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    position: 'relative',
                  }}>
                    <div style={{ fontWeight: 500, color: '#0f172a', marginBottom: 4 }}>{shift.name}</div>
                    <div style={{ color: '#64748b', fontSize: 14 }}>{shift.role}</div>
                    <div style={{ color: '#64748b', fontSize: 14 }}>{shift.start} - {shift.end}</div>
                    <button
                      onClick={() => handleRemoveShift(weeklyShifts.findIndex(s => s === shift))}
                      style={{ position: 'absolute', top: 6, right: 8, background: 'none', border: 'none', color: '#b91c1c', fontWeight: 700, fontSize: 16, cursor: 'pointer' }}
                      title="Remove shift"
                    >×</button>
                    <button
                      onClick={() => openModal(day, weeklyShifts.findIndex(s => s === shift))}
                      style={{ position: 'absolute', top: 6, left: 8, background: '#e0e7ff', color: '#3730a3', border: 'none', borderRadius: 6, padding: '2px 8px', fontWeight: 500, fontSize: 13, cursor: 'pointer' }}
                      title="Edit shift"
                    >Edit</button>
                  </div>
                ))}
                <button
                  style={{ background: '#6366f1', color: '#fff', border: 'none', borderRadius: 8, padding: '6px 12px', fontWeight: 600, fontSize: '0.95rem', cursor: 'pointer', marginTop: 4, width: '100%' }}
                  onClick={() => openModal(day)}
                >
                  Add Shift
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Employee Availability Section */}
      <h2 className={styles.heading}>Employee Availability</h2>
      <div style={{ background: '#fff', borderRadius: 12, boxShadow: '0 1px 4px rgba(0,0,0,0.04)', padding: 0, marginTop: 8, maxHeight: 340, overflowY: 'auto' }}>
        <table className={styles.shiftsTable} style={{ margin: 0 }}>
          <thead>
            <tr>
              <th>Name</th>
              <th>Role</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {employeeAvailability.map((emp, idx) => (
              <tr key={emp.id} style={{ cursor: 'pointer' }} onClick={() => openAvailabilityModal(idx)}>
                <td>{emp.name}</td>
                <td>{emp.role}</td>
                <td>
                  <span className={emp.status === 'Active' ? styles.statusApproved : styles.statusPending}>{emp.status}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add/Edit Shift Modal */}
      {modalOpen && (
        <div style={{
          position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', background: 'rgba(0,0,0,0.18)', zIndex: 1000,
          display: 'flex', alignItems: 'center', justifyContent: 'center'
        }}>
          <div style={{ background: '#fff', borderRadius: 12, padding: 32, minWidth: 320, boxShadow: '0 2px 12px rgba(0,0,0,0.12)' }}>
            <h3 style={{ marginBottom: 18, color: '#6366f1' }}>{editingIdx !== null ? 'Edit' : 'Add'} Shift for {modalDay}</h3>
            <form onSubmit={handleAddOrEditShift}>
              <div style={{ marginBottom: 12 }}>
                <label style={{ display: 'block', fontWeight: 500, marginBottom: 4 }}>Employee</label>
                <select name="employeeId" value={form.employeeId} onChange={handleFormChange} required style={{ width: '100%', padding: 8, borderRadius: 6, border: '1px solid #e5e7eb' }}>
                  <option value="">Select employee</option>
                  {getAvailableEmployees().map(emp => (
                    <option key={emp.id} value={emp.id}>{emp.name} ({emp.role})</option>
                  ))}
                </select>
              </div>
              <div style={{ marginBottom: 12, display: 'flex', gap: 8 }}>
                <div style={{ flex: 1 }}>
                  <label style={{ display: 'block', fontWeight: 500, marginBottom: 4 }}>Start Time</label>
                  <select name="start" value={form.start} onChange={handleFormChange} required style={{ width: '100%', padding: 8, borderRadius: 6, border: '1px solid #e5e7eb' }}>
                    <option value="">Select time</option>
                    {timeOptions.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>
                <div style={{ flex: 1 }}>
                  <label style={{ display: 'block', fontWeight: 500, marginBottom: 4 }}>End Time</label>
                  <select name="end" value={form.end} onChange={handleFormChange} required style={{ width: '100%', padding: 8, borderRadius: 6, border: '1px solid #e5e7eb' }}>
                    <option value="">Select time</option>
                    {timeOptions.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>
              </div>
              <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
                <button type="button" onClick={closeModal} style={{ background: '#f3f4f6', color: '#64748b', border: 'none', borderRadius: 6, padding: '8px 18px', fontWeight: 500, fontSize: '1rem', cursor: 'pointer' }}>Cancel</button>
                <button type="submit" style={{ background: '#6366f1', color: '#fff', border: 'none', borderRadius: 6, padding: '8px 18px', fontWeight: 600, fontSize: '1rem', cursor: 'pointer' }}>{editingIdx !== null ? 'Save' : 'Add Shift'}</button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Employee Availability Modal */}
      {availabilityModalOpen && selectedEmployeeIdx !== null && (
        <div style={{
          position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', background: 'rgba(0,0,0,0.18)', zIndex: 1000,
          display: 'flex', alignItems: 'center', justifyContent: 'center'
        }}>
          <div style={{ background: '#fff', borderRadius: 16, padding: 32, minWidth: 380, boxShadow: '0 2px 16px rgba(0,0,0,0.14)' }}>
            <h3 style={{ marginBottom: 18, color: '#6366f1', textAlign: 'center' }}>Availability for {employeeAvailability[selectedEmployeeIdx].name}</h3>
            <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: 18 }}>
              <thead>
                <tr>
                  <th style={{ textAlign: 'left', padding: 8 }}>Day</th>
                  <th style={{ textAlign: 'center', padding: 8 }}>Available?</th>
                  <th style={{ textAlign: 'center', padding: 8 }}>Start</th>
                  <th style={{ textAlign: 'center', padding: 8 }}>End</th>
                </tr>
              </thead>
              <tbody>
                {employeeAvailability[selectedEmployeeIdx].availability.map((a, dayIdx) => (
                  <tr key={a.day}>
                    <td style={{ padding: 8 }}>{a.day}</td>
                    <td style={{ textAlign: 'center', padding: 8 }}>
                      <select
                        value={a.available ? 'true' : 'false'}
                        onChange={e => handleAvailabilityChange(dayIdx, 'available', e.target.value)}
                        style={{ padding: 6, borderRadius: 6, border: '1px solid #e5e7eb' }}
                      >
                        <option value="true">Available</option>
                        <option value="false">Not Available</option>
                      </select>
                    </td>
                    <td style={{ textAlign: 'center', padding: 8 }}>
                      <select
                        value={a.start}
                        onChange={e => handleAvailabilityChange(dayIdx, 'start', e.target.value)}
                        style={{ padding: 6, borderRadius: 6, border: '1px solid #e5e7eb' }}
                        disabled={!a.available}
                      >
                        <option value="">Start</option>
                        {timeOptions.map(t => <option key={t} value={t}>{t}</option>)}
                      </select>
                    </td>
                    <td style={{ textAlign: 'center', padding: 8 }}>
                      <select
                        value={a.end}
                        onChange={e => handleAvailabilityChange(dayIdx, 'end', e.target.value)}
                        style={{ padding: 6, borderRadius: 6, border: '1px solid #e5e7eb' }}
                        disabled={!a.available}
                      >
                        <option value="">End</option>
                        {timeOptions.map(t => <option key={t} value={t}>{t}</option>)}
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
              <button type="button" onClick={closeAvailabilityModal} style={{ background: '#6366f1', color: '#fff', border: 'none', borderRadius: 8, padding: '10px 28px', fontWeight: 600, fontSize: '1rem', cursor: 'pointer' }}>Close</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeSheetTab; 