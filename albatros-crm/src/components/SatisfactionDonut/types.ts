export interface SatisfactionDonutProps {
  courseId: string;
}

export interface SatisfactionData {
  score: number;
  responses: number;
  distribution: {
    excellent: number;
    good: number;
    needsImprovement: number;
  };
}

export interface SegmentDetails {
  keyFactors: string[];
  recommendations: string;
}

export interface Segment {
  id: string;
  label: string;
  percentage: number;
  color: string;
  startAngle: number;
  responses: number;
  details: SegmentDetails;
} 