import { Box, Button } from '@mui/material';
import { styled } from '@mui/material/styles';

export const DonutContainer = styled(Box)(({ theme }) => ({
  background: '#FFFFFF',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
  height: '400px'
}));

export const ViewMetricsButton = styled(Button)(({ theme }) => ({
  color: theme.palette.text.secondary,
  textTransform: 'none',
  marginLeft: 'auto',
  '&:hover': {
    background: 'transparent',
  }
})); 