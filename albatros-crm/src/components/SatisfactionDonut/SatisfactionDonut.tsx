import React, { useState } from 'react';
import { Box, Typography, Tooltip, Popover, Button } from '@mui/material';
import { KeyboardArrowDown } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { SatisfactionDonutProps, SatisfactionData, Segment } from './types';

const DonutContainer = styled(Box)(({ theme }) => ({
  background: '#FFFFFF',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
  height: '400px'
}));

const ViewMetricsButton = styled(Button)(({ theme }) => ({
  color: theme.palette.text.secondary,
  textTransform: 'none',
  marginLeft: 'auto',
  '&:hover': {
    background: 'transparent',
  }
}));

const SatisfactionDonut: React.FC<SatisfactionDonutProps> = ({ courseId }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selectedSegment, setSelectedSegment] = useState<Segment | null>(null);
  const [hoveredSegment, setHoveredSegment] = useState<Segment | null>(null);

  // Mock data that would normally come from an API
  const satisfactionData: SatisfactionData = {
    score: 4.8,
    responses: 150,
    distribution: {
      excellent: 45.0,
      good: 35.0,
      needsImprovement: 20.0
    }
  };

  const segments: Segment[] = [
    {
      id: 'excellent',
      label: 'Excellent',
      percentage: satisfactionData.distribution.excellent,
      color: 'url(#greenGradient)',
      startAngle: -90,
      responses: Math.round(satisfactionData.responses * (satisfactionData.distribution.excellent / 100)),
      details: {
        keyFactors: [
          'Exceptional course maintenance',
          'Professional staff interactions',
          'Smooth booking process'
        ],
        recommendations: 'Maintain high standards and consider loyalty rewards'
      }
    },
    {
      id: 'good',
      label: 'Good',
      percentage: satisfactionData.distribution.good,
      color: 'url(#orangeGradient)',
      startAngle: satisfactionData.distribution.excellent * 3.6 - 90,
      responses: Math.round(satisfactionData.responses * (satisfactionData.distribution.good / 100)),
      details: {
        keyFactors: [
          'Well-maintained facilities',
          'Friendly staff service',
          'Reasonable wait times'
        ],
        recommendations: 'Focus on small improvements to elevate experience'
      }
    },
    {
      id: 'needsImprovement',
      label: 'Needs Improvement',
      percentage: satisfactionData.distribution.needsImprovement,
      color: 'url(#redGradient)',
      startAngle: (satisfactionData.distribution.excellent + satisfactionData.distribution.good) * 3.6 - 90,
      responses: Math.round(satisfactionData.responses * (satisfactionData.distribution.needsImprovement / 100)),
      details: {
        keyFactors: [
          'Tee time availability',
          'Course pace management',
          'Facility amenities'
        ],
        recommendations: 'Implement immediate action plan for identified areas'
      }
    }
  ];

  const handleSegmentClick = (event: React.MouseEvent<SVGCircleElement>, segment: Segment): void => {
    setSelectedSegment(segment);
    setAnchorEl(event.currentTarget.parentElement as HTMLElement);
  };

  const handlePopoverClose = (): void => {
    setAnchorEl(null);
    setSelectedSegment(null);
  };

  const handleSegmentHover = (segment: Segment): void => {
    setHoveredSegment(segment);
  };

  const handleSegmentLeave = (): void => {
    setHoveredSegment(null);
  };

  const open = Boolean(anchorEl);
  
  return (
    <DonutContainer>
      <Box display="flex" alignItems="center" mb={2}>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Customers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Golfer Satisfaction
          </Typography>
        </Box>
        <ViewMetricsButton
          endIcon={<KeyboardArrowDown />}
        >
          View Metrics
        </ViewMetricsButton>
      </Box>
      
      <Box position="relative" width={200} height={200} margin="0 auto">
        <svg width="200" height="200" viewBox="0 0 200 200">
          <defs>
            <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#4CAF50" />
              <stop offset="100%" stopColor="#45a049" />
            </linearGradient>
            <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#FFA726" />
              <stop offset="100%" stopColor="#FB8C00" />
            </linearGradient>
            <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#EF5350" />
              <stop offset="100%" stopColor="#D32F2F" />
            </linearGradient>
          </defs>
          
          {segments.map((segment) => {
            const isHovered = hoveredSegment?.id === segment.id;
            const translateX = isHovered ? (Math.cos((segment.startAngle + 45) * Math.PI / 180) * 5) : 0;
            const translateY = isHovered ? (Math.sin((segment.startAngle + 45) * Math.PI / 180) * 5) : 0;

            return (
              <Tooltip
                key={segment.id}
                title={`${segment.label}: ${segment.percentage}%`}
                placement="top"
                arrow
              >
                <g
                  transform={`translate(${translateX} ${translateY})`}
                  style={{ transition: 'transform 0.3s ease' }}
                  onMouseEnter={() => handleSegmentHover(segment)}
                  onMouseLeave={handleSegmentLeave}
                >
                  <circle
                    cx="100"
                    cy="100"
                    r="80"
                    fill="none"
                    stroke={segment.color}
                    strokeWidth={isHovered ? "22" : "20"}
                    strokeDasharray={`${(Math.PI * 160) * (segment.percentage / 100)} ${Math.PI * 160}`}
                    transform={`rotate(${segment.startAngle} 100 100)`}
                    style={{
                      cursor: 'pointer',
                      transition: 'stroke-width 0.3s ease, filter 0.3s ease',
                      filter: isHovered ? 'brightness(1.1) drop-shadow(0px 0px 3px rgba(0,0,0,0.3))' : 'none'
                    }}
                    onClick={(e) => handleSegmentClick(e, segment)}
                  />
                </g>
              </Tooltip>
            );
          })}
        </svg>
        
        <Typography
          variant="h3"
          component="div"
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            fontWeight: 'bold'
          }}
        >
          {satisfactionData.score}
        </Typography>
      </Box>

      <Typography
        variant="body2"
        color="text.secondary"
        align="center"
        mt={2}
      >
        Based on {satisfactionData.responses} responses
      </Typography>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'left',
        }}
      >
        {selectedSegment && (
          <Box sx={{ p: 2, maxWidth: 300 }}>
            <Typography variant="h6" gutterBottom>
              {selectedSegment.label} Satisfaction
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              {selectedSegment.responses} golfers ({selectedSegment.percentage}%) rated their experience as {selectedSegment.label.toLowerCase()}.
            </Typography>
            <Typography variant="body2" gutterBottom>
              Key factors:
            </Typography>
            <ul style={{ margin: '8px 0', paddingLeft: 20 }}>
              {selectedSegment.details.keyFactors.map((factor, index) => (
                <li key={index}>{factor}</li>
              ))}
            </ul>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Recommendation:
            </Typography>
            <Typography variant="body2">
              {selectedSegment.details.recommendations}
            </Typography>
          </Box>
        )}
      </Popover>
    </DonutContainer>
  );
};

export default SatisfactionDonut; 