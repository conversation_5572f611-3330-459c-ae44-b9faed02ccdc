import React, { useState } from 'react';
import SummaryTab from './SummaryTab';
import TimeSheetTab from './TimeSheetTab';
import MarketingTab from './MarketingTab';
import AccountingTab from './AccountingTab';
import PromosTab from './PromosTab';
import ReviewsTab from './ReviewsTab';
import styles from './BackOffice.module.css';

const TABS = [
  { label: 'Summary', component: <SummaryTab /> },
  { label: 'Time Sheet', component: <TimeSheetTab /> },
  { label: 'Marketing', component: <MarketingTab /> },
  { label: 'Accounting', component: <AccountingTab /> },
  { label: 'Promos', component: <PromosTab /> },
  { label: 'Reviews', component: <ReviewsTab /> },
];

const BackOfficeModule: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className={styles.backOfficeContainer}>
      <div style={{ background: 'transparent', borderRadius: 0, marginBottom: 0, padding: 0 }}>
        <div style={{ display: 'flex', gap: 32, borderBottom: '1px solid #e5e7eb', background: 'transparent', padding: '0 24px', marginBottom: 0 }}>
          {TABS.map((tab, idx) => (
            <button
              key={tab.label}
              onClick={() => setActiveTab(idx)}
              style={{
                background: 'none',
                border: 'none',
                outline: 'none',
                color: activeTab === idx ? '#14b8a6' : '#334155',
                fontWeight: activeTab === idx ? 600 : 400,
                fontSize: '1rem',
                padding: '16px 18px 8px 18px',
                borderBottom: activeTab === idx ? '2px solid #14b8a6' : '2px solid transparent',
                cursor: 'pointer',
                transition: 'color 0.2s',
                marginBottom: '-1px',
                backgroundClip: 'padding-box',
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>
      <div className={styles.tabContent}>
        {TABS[activeTab].component}
      </div>
    </div>
  );
};

export default BackOfficeModule; 