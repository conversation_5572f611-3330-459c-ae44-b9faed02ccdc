import React from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import {
  People,
  GolfCourse,
  TrendingUp,
  Warning,
  CalendarToday,
  Group,
  Event,
  AccessTime,
  AttachMoney,
  Speed,
  Store
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { StyledPaper } from '../../styles/styledComponents';
import { IconType, MetricsCardSize, ColorScheme, MetricsCardProps } from './types';

const iconMap = {
  people: People,
  golf_course: GolfCourse,
  trending_up: TrendingUp,
  warning: Warning,
  calendar: CalendarToday,
  group: Group,
  event: Event,
  access_time: AccessTime,
  money: AttachMoney,
  speed: Speed,
  store: Store
};

const CardContainer = styled(StyledPaper, {
  shouldForwardProp: (prop) => prop !== 'isHoverable' && prop !== 'size',
})<{ isHoverable?: boolean; size?: MetricsCardSize }>(({ theme, isHoverable = false, size = 'medium' }) => ({
  padding: size === 'small' ? theme.spacing(1.5) : theme.spacing(2),
  height: '100%',
  cursor: isHoverable ? 'pointer' : 'default',
  transition: theme.transitions.create(['transform', 'box-shadow']),
  ...(isHoverable && {
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    },
  }),
}));

const IconContainer = styled(Box)<{ colorScheme: ColorScheme; bgColor?: string }>(({ theme, colorScheme, bgColor }) => ({
  backgroundColor: bgColor || (colorScheme === 'custom' ? 'transparent' : theme.palette[colorScheme].light),
  borderRadius: '50%',
  padding: theme.spacing(1),
  marginRight: theme.spacing(2),
}));

const ValueText = styled(Typography)<{ size: MetricsCardSize }>(({ theme, size }) => ({
  marginBottom: theme.spacing(1),
  fontSize: size === 'small' ? theme.typography.h6.fontSize : theme.typography.h4.fontSize,
}));

const LabelText = styled(Typography)<{ size: MetricsCardSize }>(({ theme, size }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  fontSize: size === 'small' ? theme.typography.subtitle1.fontSize : theme.typography.h6.fontSize,
}));

export const MetricsCard: React.FC<MetricsCardProps> = ({
  icon,
  label,
  value,
  change,
  bgColor,
  className,
  size = 'medium',
  colorScheme = 'primary',
  tooltip,
  isLoading,
  error,
  onClick,
  isHoverable,
  subtitle,
  comparisonText,
  ariaLabel
}) => {
  const IconComponent = iconMap[icon];

  const cardContent = (
    <CardContainer
      className={className}
      onClick={onClick}
      isHoverable={isHoverable || Boolean(onClick)}
      size={size}
      aria-label={ariaLabel}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconContainer colorScheme={colorScheme} bgColor={bgColor}>
          {IconComponent && <IconComponent color={colorScheme === 'custom' ? undefined : colorScheme} />}
        </IconContainer>
        <LabelText size={size}>{label}</LabelText>
      </Box>
      
      <ValueText size={size}>
        {isLoading ? 'Loading...' : error ? 'Error' : value}
      </ValueText>

      {(subtitle || change !== undefined || comparisonText) && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {change !== undefined && (
            <Typography 
              variant="body2" 
              color={change >= 0 ? 'success.main' : 'error.main'}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {change >= 0 ? '+' : ''}{change}%
              <TrendingUp 
                sx={{ 
                  ml: 0.5, 
                  transform: change >= 0 ? 'none' : 'rotate(180deg)'
                }} 
              />
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
          {comparisonText && (
            <Typography variant="body2" color="text.secondary">
              {comparisonText}
            </Typography>
          )}
        </Box>
      )}
    </CardContainer>
  );

  return tooltip ? (
    <Tooltip title={tooltip}>
      {cardContent}
    </Tooltip>
  ) : cardContent;
}; 