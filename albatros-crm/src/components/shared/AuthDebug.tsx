import React from 'react';
import { Box, Typography, Alert } from '@mui/material';
import { useXanoAuth } from '../../api/hooks/useXanoAuth';

const AuthDebug: React.FC = () => {
  try {
    const { token, user, loading, error, isAuthenticated } = useXanoAuth();
    
    return (
      <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 1, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Auth Debug Info
        </Typography>
        <Typography variant="body2">
          <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body2">
          <strong>Has Token:</strong> {token ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body2">
          <strong>Has User:</strong> {user ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body2">
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </Typography>
        {error && (
          <Alert severity="error" sx={{ mt: 1 }}>
            Error: {error}
          </Alert>
        )}
        {user && (
          <Typography variant="body2">
            <strong>User:</strong> {user.name || user.email || 'Unknown'}
          </Typography>
        )}
      </Box>
    );
  } catch (error: any) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        <Typography variant="h6">Auth Provider Error</Typography>
        <Typography variant="body2">
          {error.message || 'useXanoAuth must be used within XanoAuthProvider'}
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Make sure the XanoAuthProvider is wrapping your app in index.tsx
        </Typography>
      </Alert>
    );
  }
};

export default AuthDebug;
