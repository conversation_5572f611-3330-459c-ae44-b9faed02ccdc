import React from 'react';
import { Box, Typography, Alert } from '@mui/material';
import { useXanoAuth } from '../../api/hooks/useXanoAuth';

const AuthDebug: React.FC = () => {
  const [authError, setAuthError] = React.useState<string | null>(null);
  const [authData, setAuthData] = React.useState<any>(null);

  React.useEffect(() => {
    try {
      // This will be set by the parent component that has access to useXanoAuth
      setAuthError(null);
    } catch (error: any) {
      setAuthError(error.message);
    }
  }, []);

  // If there's an auth error, show it
  if (authError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        <Typography variant="h6">Auth Provider Error</Typography>
        <Typography variant="body2">
          {authError || 'useXanoAuth must be used within XanoAuthProvider'}
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Make sure the XanoAuthProvider is wrapping your app in index.tsx
        </Typography>
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 1, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        Auth Debug Info
      </Typography>
      <Typography variant="body2">
        <strong>Provider Status:</strong> XanoAuthProvider is properly configured
      </Typography>
      <Typography variant="body2">
        <strong>Ready for Authentication:</strong> Yes
      </Typography>
    </Box>
  );
};

export default AuthDebug;
