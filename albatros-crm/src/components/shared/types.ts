// From MetricsCard.tsx
export type IconType = 
  | 'people'
  | 'golf_course'
  | 'trending_up'
  | 'warning'
  | 'calendar'
  | 'group'
  | 'event'
  | 'access_time'
  | 'money'
  | 'speed'
  | 'store';

export type MetricsCardSize = 'small' | 'medium' | 'large';
export type ColorScheme = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'custom';

export interface MetricsCardProps {
  icon: IconType;
  label: string;
  value: string | number;
  change?: number;
  bgColor?: string;
  className?: string;
  size?: MetricsCardSize;
  colorScheme?: ColorScheme;
  tooltip?: string;
  isLoading?: boolean;
  error?: string;
  onClick?: () => void;
  isHoverable?: boolean;
  subtitle?: string;
  comparisonText?: string;
  ariaLabel?: string;
}

// From PhoneInput.tsx
export interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  name?: string;
  label?: string;
  fullWidth?: boolean;
} 