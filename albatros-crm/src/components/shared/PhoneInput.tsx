import React from 'react';
import { MuiTelInput } from 'mui-tel-input';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { PhoneInputProps } from './types';

const StyledPhoneInput = styled(MuiTelInput)(({ theme }) => ({
  width: '100%',
  '& .MuiOutlinedInput-root': {
    height: '40px',
    overflow: 'hidden',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.text.primary,
    },
    '& input': {
      borderRadius: 'inherit',
      padding: '8.5px 14px'
    }
  },
}));

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  error,
  helperText,
  required = false,
  name = 'phone',
  label = 'Phone Number',
  fullWidth = true
}) => {
  return (
    <Box sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>
      <StyledPhoneInput
        value={value}
        onChange={onChange}
        error={error}
        helperText={helperText}
        required={required}
        name={name}
        label={label}
        defaultCountry="US"
        forceCallingCode
        preferredCountries={['US', 'CA']}
        langOfCountryName="en"
      />
    </Box>
  );
};

// Validation function that can be used across components
export const isValidPhoneNumber = (phone: string): boolean => {
  if (!phone) return false;
  return /^\(\d{3}\) \d{3}-\d{4}$/.test(phone);
};

export default PhoneInput; 