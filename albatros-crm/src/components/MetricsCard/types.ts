import { ReactNode } from 'react';

/**
 * Available icon types for the metrics card
 */
export type IconType = 
  | 'people'
  | 'golf_course'
  | 'trending_up'
  | 'warning'
  | 'calendar'
  | 'group'
  | 'event'
  | 'access_time'
  | 'money'
  | 'speed'
  | 'store';

/**
 * Available size options for the metrics card
 */
export type MetricsCardSize = 'small' | 'medium' | 'large';

/**
 * Available color schemes for the metrics card
 */
export type ColorScheme = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'custom';

/**
 * Props for the MetricsCard component
 */
export interface MetricsCardProps {
  /** The icon to display in the card */
  icon: IconType;
  /** The label text to display */
  label: string;
  /** The value to display (can be a number or string) */
  value: string | number;
  /** Optional percentage change to display */
  change?: number;
  /** Optional background color for the icon circle. Only used when colorScheme is 'custom' */
  bgColor?: string;
  /** Optional className for additional styling */
  className?: string;
  /** Optional size variant for the card */
  size?: MetricsCardSize;
  /** Optional color scheme for the card */
  colorScheme?: ColorScheme;
  /** Optional tooltip text to show on hover */
  tooltip?: string;
  /** Optional loading state */
  isLoading?: boolean;
  /** Optional error state */
  error?: string;
  /** Optional callback when the card is clicked */
  onClick?: () => void;
  /** Whether the card should be hoverable */
  isHoverable?: boolean;
  /** Optional subtitle text */
  subtitle?: string;
  /** Optional comparison text (e.g. "vs last week") */
  comparisonText?: string;
  /** Optional aria-label for better accessibility */
  ariaLabel?: string;
} 