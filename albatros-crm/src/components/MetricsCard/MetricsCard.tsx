import React from 'react';
import { Box, Paper, Typography, Tooltip } from '@mui/material';
import { MetricsCardProps } from './types';
import {
  People,
  GolfCourse,
  TrendingUp,
  Warning,
  CalendarToday,
  Group,
  Event,
  AccessTime,
  AttachMoney,
  Speed,
  Store
} from '@mui/icons-material';

const iconMap = {
  people: People,
  golf_course: GolfCourse,
  trending_up: TrendingUp,
  warning: Warning,
  calendar: CalendarToday,
  group: Group,
  event: Event,
  access_time: AccessTime,
  money: AttachMoney,
  speed: Speed,
  store: Store
};

export const MetricsCard: React.FC<MetricsCardProps> = ({
  icon,
  label,
  value,
  change,
  bgColor,
  className,
  size = 'medium',
  colorScheme = 'primary',
  tooltip,
  isLoading,
  error,
  onClick,
  isHoverable,
  subtitle,
  comparisonText,
  ariaLabel
}) => {
  const IconComponent = iconMap[icon as keyof typeof iconMap];

  const cardContent = (
    <Paper 
      sx={{ 
        p: size === 'small' ? 1.5 : 2,
        height: '100%',
        cursor: onClick || isHoverable ? 'pointer' : 'default',
        '&:hover': {
          transform: (onClick || isHoverable) ? 'translateY(-2px)' : 'none',
          transition: 'transform 0.2s ease-in-out'
        }
      }}
      className={className}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ 
          backgroundColor: bgColor || `${colorScheme}.light`,
          borderRadius: '50%',
          p: 1,
          mr: 2
        }}>
          {IconComponent && <IconComponent color={colorScheme === 'custom' ? undefined : colorScheme} />}
        </Box>
        <Typography 
          variant={size === 'small' ? 'subtitle1' : 'h6'} 
          sx={{ 
            fontWeight: 600,
            color: 'text.primary'
          }}
        >
          {label}
        </Typography>
      </Box>
      
      <Typography variant={size === 'small' ? 'h6' : 'h4'} sx={{ mb: 1 }}>
        {isLoading ? 'Loading...' : error ? 'Error' : value}
      </Typography>

      {(subtitle || change !== undefined || comparisonText) && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {change !== undefined && (
            <Typography 
              variant="body2" 
              color={change >= 0 ? 'success.main' : 'error.main'}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {change >= 0 ? '+' : ''}{change}%
              <TrendingUp 
                sx={{ 
                  ml: 0.5, 
                  transform: change >= 0 ? 'none' : 'rotate(180deg)'
                }} 
              />
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
          {comparisonText && (
            <Typography variant="body2" color="text.secondary">
              {comparisonText}
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );

  return tooltip ? (
    <Tooltip title={tooltip}>
      {cardContent}
    </Tooltip>
  ) : cardContent;
}; 