import { styled } from '@mui/material/styles';
import { Paper, Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import { MetricsCardProps, ColorScheme } from './types';

const colors: Record<Exclude<ColorScheme, 'custom'>, { main: string; light: string }> = {
  primary: { main: '#3B82F6', light: '#EFF6FF' },
  secondary: { main: '#6B7280', light: '#F3F4F6' },
  success: { main: '#10B981', light: '#F0FDF4' },
  warning: { main: '#F59E0B', light: '#FEF3C7' },
  error: { main: '#EF4444', light: '#FEE2E2' },
  info: { main: '#3B82F6', light: '#EFF6FF' },
};

const getSizeStyles = (size: MetricsCardProps['size'] = 'medium') => {
  const sizes = {
    small: { padding: '12px', iconSize: 32 },
    medium: { padding: '16px', iconSize: 40 },
    large: { padding: '20px', iconSize: 48 },
  };
  return sizes[size];
};

const getColorStyles = (colorScheme: ColorScheme = 'primary', bgColor?: string) => {
  if (colorScheme === 'custom' && bgColor) {
    return {
      backgroundColor: bgColor,
      color: '#000000',
    };
  }
  return {
    backgroundColor: colors[colorScheme as Exclude<ColorScheme, 'custom'>].light,
    color: colors[colorScheme as Exclude<ColorScheme, 'custom'>].main,
  };
};

export const MetricsCardContainer = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'colorScheme' && prop !== 'size' && prop !== 'isHoverable',
})<{ colorScheme?: ColorScheme; size?: MetricsCardProps['size']; isHoverable?: boolean }>(
  ({ theme, colorScheme, size, isHoverable }) => ({
    position: 'relative',
    padding: getSizeStyles(size).padding,
    borderRadius: theme.shape.borderRadius * 2,
    transition: 'all 0.2s ease-in-out',
    ...getColorStyles(colorScheme),
    ...(isHoverable && {
      cursor: 'pointer',
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: theme.shadows[4],
      },
    }),
  })
);

export const IconContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'colorScheme',
})<{ colorScheme?: ColorScheme }>(({ theme, colorScheme = 'primary' }) => ({
  width: 48,
  height: 48,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  ...getColorStyles(colorScheme),
}));

export const MetricsContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

export const Label = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  fontWeight: 500,
}));

export const Value = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
}));

export const ChangeIndicator = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'isPositive',
})<{ isPositive?: boolean }>(({ theme, isPositive }) => ({
  fontSize: '0.875rem',
  color: isPositive ? theme.palette.success.main : theme.palette.error.main,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

export const Subtitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  marginTop: theme.spacing(0.5),
}));

export const ComparisonText = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  marginTop: theme.spacing(0.5),
}));

export const LoadingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.7)',
  borderRadius: 'inherit',
  zIndex: 1,
}));

export const ErrorMessage = styled(Typography)(({ theme }) => ({
  color: theme.palette.error.main,
  fontSize: '0.875rem',
  marginTop: theme.spacing(1),
})); 