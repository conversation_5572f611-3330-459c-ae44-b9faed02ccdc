import React, { useState } from 'react';
import styles from './BackOffice.module.css';

const promoCampaigns = [
  { 
    id: 1, 
    name: 'Summer Golf Package', 
    type: 'Discount', 
    discount: '20% off green fees',
    status: 'Active',
    startDate: '2024-06-01',
    endDate: '2024-08-31',
    targetAudience: 'All Members',
    redemptionCount: 45,
    totalRevenue: 2250,
    cost: 450,
    roi: 400
  },
  { 
    id: 2, 
    name: 'New Member Welcome', 
    type: 'Free Round', 
    discount: '1 free round',
    status: 'Active',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    targetAudience: 'New Members',
    redemptionCount: 23,
    totalRevenue: 1150,
    cost: 0,
    roi: 1150
  },
  { 
    id: 3, 
    name: 'Weekday Special', 
    type: 'Discount', 
    discount: '15% off Mon-Wed',
    status: 'Scheduled',
    startDate: '2024-07-01',
    endDate: '2024-09-30',
    targetAudience: 'Regular Members',
    redemptionCount: 0,
    totalRevenue: 0,
    cost: 0,
    roi: 0
  },
  { 
    id: 4, 
    name: 'Pro Shop Bundle', 
    type: 'Package', 
    discount: 'Buy 2 get 1 free',
    status: 'Inactive',
    startDate: '2024-03-01',
    endDate: '2024-05-31',
    targetAudience: 'All Members',
    redemptionCount: 67,
    totalRevenue: 3350,
    cost: 670,
    roi: 2680
  },
  { 
    id: 5, 
    name: 'Senior Discount', 
    type: 'Discount', 
    discount: '25% off for seniors',
    status: 'Active',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    targetAudience: 'Senior Members',
    redemptionCount: 89,
    totalRevenue: 4450,
    cost: 1112,
    roi: 3338
  },
  { 
    id: 6, 
    name: 'Referral Bonus', 
    type: 'Reward', 
    discount: '$50 credit for referrals',
    status: 'Active',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    targetAudience: 'All Members',
    redemptionCount: 12,
    totalRevenue: 600,
    cost: 600,
    roi: 0
  },
];

const promoAnalytics = [
  {
    name: 'Summer Golf Package',
    type: 'Discount',
    redemptionRate: 15.2,
    avgRevenuePerRedemption: 50,
    totalRedemptions: 45,
    totalRevenue: 2250,
    cost: 450,
    roi: 400,
    startDate: '2024-06-01',
    endDate: '2024-08-31',
  },
  {
    name: 'New Member Welcome',
    type: 'Free Round',
    redemptionRate: 76.7,
    avgRevenuePerRedemption: 50,
    totalRedemptions: 23,
    totalRevenue: 1150,
    cost: 0,
    roi: 1150,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
  },
  {
    name: 'Pro Shop Bundle',
    type: 'Package',
    redemptionRate: 22.3,
    avgRevenuePerRedemption: 50,
    totalRedemptions: 67,
    totalRevenue: 3350,
    cost: 670,
    roi: 2680,
    startDate: '2024-03-01',
    endDate: '2024-05-31',
  },
  {
    name: 'Senior Discount',
    type: 'Discount',
    redemptionRate: 34.1,
    avgRevenuePerRedemption: 50,
    totalRedemptions: 89,
    totalRevenue: 4450,
    cost: 1112,
    roi: 3338,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
  },
];

const memberSegments = [
  { id: 1, name: 'All Members', count: 295, tags: ['All'] },
  { id: 2, name: 'New Members', count: 30, tags: ['New', 'First Year'] },
  { id: 3, name: 'Regular Members', count: 180, tags: ['Regular', 'Active'] },
  { id: 4, name: 'Premium Members', count: 85, tags: ['Premium', 'VIP'] },
  { id: 5, name: 'Senior Members', count: 65, tags: ['Senior', '65+'] },
  { id: 6, name: 'Junior Members', count: 20, tags: ['Junior', 'Under 18'] },
];

const PromosTab: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedPromo, setSelectedPromo] = useState<any>(null);
  const [showCreatePromoModal, setShowCreatePromoModal] = useState(false);
  const [promoForm, setPromoForm] = useState({
    name: '',
    type: 'Discount',
    discount: '',
    startDate: '',
    endDate: '',
    targetAudience: '',
    budget: '',
    description: ''
  });
  const [discountType, setDiscountType] = useState<'percentage' | 'dollar' | 'bogo' | 'custom'>('percentage');
  const [discountAmount, setDiscountAmount] = useState<number>(1);
  const [bogoText, setBogoText] = useState('');
  const [customOffer, setCustomOffer] = useState('');
  const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
  const [promoToDeactivate, setPromoToDeactivate] = useState<any>(null);

  const handleOpenModal = (promo: any) => {
    setSelectedPromo(promo);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedPromo(null);
  };

  const handleCreatePromo = () => {
    setShowCreatePromoModal(true);
  };

  const handleCloseCreatePromoModal = () => {
    setShowCreatePromoModal(false);
    setPromoForm({
      name: '',
      type: 'Discount',
      discount: '',
      startDate: '',
      endDate: '',
      targetAudience: '',
      budget: '',
      description: ''
    });
  };

  const handleSavePromo = () => {
    // Here you would save the promo to your backend
    console.log('Saving promo:', promoForm);
    handleCloseCreatePromoModal();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return '#10b981';
      case 'Scheduled': return '#f59e0b';
      case 'Inactive': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Discount': return '#3b82f6';
      case 'Free Round': return '#10b981';
      case 'Package': return '#8b5cf6';
      case 'Reward': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  return (
    <div className={styles.marketingTab}>
      {/* Promotions Section */}
      <div className={styles.templatesSection}>
        <div className={styles.templatesHeader}>
          <h2 className={styles.heading}>Promotions</h2>
          <div className={styles.templatesActions}>
            <button className={styles.secondaryButton}>See All</button>
            <button className={styles.primaryButton} onClick={handleCreatePromo}>Create Promotion</button>
          </div>
        </div>
        <div className={styles.templatesGrid}>
          {promoCampaigns.map((promo, idx) => (
            <div key={idx} className={styles.templateCard}>
              <div className={styles.templateHeader}>
                <span className={styles.templateType} style={{ backgroundColor: getTypeColor(promo.type) }}>
                  {promo.type}
                </span>
                <span 
                  className={styles.templateStatus} 
                  style={{ backgroundColor: getStatusColor(promo.status) }}
                >
                  {promo.status}
                </span>
              </div>
              <h3 className={styles.templateName}>{promo.name}</h3>
              <p className={styles.templateDescription}>{promo.discount}</p>
              <div className={styles.templateMeta}>
                <span className={styles.templateDate}>
                  {new Date(promo.startDate).toLocaleDateString()} - {new Date(promo.endDate).toLocaleDateString()}
                </span>
                <span className={styles.templateAudience}>{promo.targetAudience}</span>
              </div>
              <div className={styles.promoMetrics}>
                <div className={styles.metric}>
                  <span className={styles.metricLabel}>Redemptions</span>
                  <span className={styles.metricValue}>{promo.redemptionCount}</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.metricLabel}>Revenue</span>
                  <span className={styles.metricValue}>${promo.totalRevenue}</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.metricLabel}>ROI</span>
                  <span className={styles.metricValue} style={{ color: promo.roi > 0 ? '#10b981' : '#ef4444' }}>
                    {promo.roi > 0 ? '+' : ''}${promo.roi}
                  </span>
                </div>
              </div>
              <button 
                className={styles.viewButton}
                onClick={() => handleOpenModal(promo)}
              >
                View Details
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Analytics Section */}
      <div className={styles.analyticsSection}>
        <h2 className={styles.heading}>Promotion Analytics</h2>
        <div className={styles.analyticsGrid}>
          {promoAnalytics.map((analytics, idx) => (
            <div key={idx} className={styles.analyticsCard}>
              <div className={styles.analyticsHeader}>
                <h3 className={styles.analyticsName}>{analytics.name}</h3>
                <span className={styles.analyticsType} style={{ backgroundColor: getTypeColor(analytics.type) }}>
                  {analytics.type}
                </span>
              </div>
              <div className={styles.analyticsMetrics}>
                <div className={styles.analyticsMetric}>
                  <span className={styles.metricLabel}>Redemption Rate</span>
                  <span className={styles.metricValue}>{analytics.redemptionRate}%</span>
                </div>
                <div className={styles.analyticsMetric}>
                  <span className={styles.metricLabel}>Total Revenue</span>
                  <span className={styles.metricValue}>${analytics.totalRevenue}</span>
                </div>
                <div className={styles.analyticsMetric}>
                  <span className={styles.metricLabel}>ROI</span>
                  <span className={styles.metricValue} style={{ color: analytics.roi > 0 ? '#10b981' : '#ef4444' }}>
                    {analytics.roi > 0 ? '+' : ''}${analytics.roi}
                  </span>
                </div>
                <div className={styles.analyticsMetric}>
                  <span className={styles.metricLabel}>Avg Revenue/Redemption</span>
                  <span className={styles.metricValue}>${analytics.avgRevenuePerRedemption}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Member Segments Section */}
      <div className={styles.segmentsSection}>
        <h2 className={styles.heading}>Member Segments</h2>
        <div className={styles.segmentsGrid}>
          {memberSegments.map((segment, idx) => (
            <div key={idx} className={styles.segmentCard}>
              <h3 className={styles.segmentName}>{segment.name}</h3>
              <p className={styles.segmentCount}>{segment.count} members</p>
              <div className={styles.segmentTags}>
                {segment.tags.map((tag, tagIdx) => (
                  <span key={tagIdx} className={styles.segmentTag}>{tag}</span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Promotion Modal */}
      {showCreatePromoModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal} style={{ maxWidth: 900, minWidth: 600 }}>
            <div className={styles.modalHeader}>
              <h2>Create New Promotion</h2>
              <button className={styles.closeButton} onClick={handleCloseCreatePromoModal}>×</button>
            </div>
            <div className={styles.modalContent} style={{ display: 'flex', flexDirection: 'row', gap: 32 }}>
              <div style={{ flex: 1, minWidth: 0 }}>
                <div className={styles.formGroup}>
                  <label>Promotion Name</label>
                  <input
                    type="text"
                    value={promoForm.name}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter promotion name"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Type</label>
                  <select
                    value={promoForm.type}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, type: e.target.value }))}
                  >
                    <option value="Discount">Discount</option>
                    <option value="Free Round">Free Round</option>
                    <option value="Package">Package</option>
                    <option value="Reward">Reward</option>
                  </select>
                </div>
                <div className={styles.formGroup}>
                  <label>Discount/Offer</label>
                  <div style={{ display: 'flex', gap: 10, flexDirection: 'column' }}>
                    <select
                      value={discountType}
                      onChange={e => {
                        const val = e.target.value as 'percentage' | 'dollar' | 'bogo' | 'custom';
                        setDiscountType(val);
                        // Reset values on type change
                        if (val === 'percentage' || val === 'dollar') {
                          setPromoForm(prev => ({ ...prev, discount: `${discountAmount}${val === 'percentage' ? '%' : '$'} off` }));
                        } else if (val === 'bogo') {
                          setBogoText('Buy 1 Get 1 Free');
                          setPromoForm(prev => ({ ...prev, discount: 'Buy 1 Get 1 Free' }));
                        } else if (val === 'custom') {
                          setCustomOffer('');
                          setPromoForm(prev => ({ ...prev, discount: '' }));
                        }
                      }}
                      style={{ width: 180 }}
                    >
                      <option value="percentage">Percentage</option>
                      <option value="dollar">Dollar</option>
                      <option value="bogo">BOGO</option>
                      <option value="custom">Custom</option>
                    </select>
                    {(discountType === 'percentage' || discountType === 'dollar') && (
                      <div style={{ display: 'flex', gap: 10 }}>
                        <select
                          value={discountAmount}
                          onChange={e => {
                            const amt = Number(e.target.value);
                            setDiscountAmount(amt);
                            setPromoForm(prev => ({
                              ...prev,
                              discount: `${amt}${discountType === 'percentage' ? '%' : '$'} off`
                            }));
                          }}
                          style={{ width: 80 }}
                        >
                          {Array.from({ length: 50 }, (_, i) => i + 1).map(num => (
                            <option key={num} value={num}>{num}</option>
                          ))}
                        </select>
                        <span style={{ alignSelf: 'center', color: '#64748b', fontWeight: 500, marginLeft: 6 }}>
                          {discountType === 'percentage' ? '% off' : '$ off'}
                        </span>
                      </div>
                    )}
                    {discountType === 'bogo' && (
                      <input
                        type="text"
                        value={bogoText}
                        onChange={e => {
                          setBogoText(e.target.value);
                          setPromoForm(prev => ({ ...prev, discount: e.target.value }));
                        }}
                        placeholder="e.g., Buy 2 Get 1 Free"
                        style={{ width: '100%' }}
                      />
                    )}
                    {discountType === 'custom' && (
                      <textarea
                        value={customOffer}
                        onChange={e => {
                          setCustomOffer(e.target.value);
                          setPromoForm(prev => ({ ...prev, discount: e.target.value }));
                        }}
                        placeholder="Describe your custom offer (e.g., Play here 3 times within the next month and get 1 free round (must be used within 2 weeks))"
                        rows={3}
                        style={{ width: '100%' }}
                      />
                    )}
                  </div>
                </div>
                <div className={styles.formGroup}>
                  <label>Target Audience</label>
                  <select
                    value={promoForm.targetAudience}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, targetAudience: e.target.value }))}
                  >
                    <option value="">Select audience</option>
                    {memberSegments.map(segment => (
                      <option key={segment.id} value={segment.name}>{segment.name}</option>
                    ))}
                  </select>
                </div>
                <div className={styles.formGroup}>
                  <label>Budget (Optional)</label>
                  <input
                    type="number"
                    value={promoForm.budget}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, budget: e.target.value }))}
                    placeholder="Enter budget amount"
                  />
                </div>
              </div>
              <div style={{ flex: 1, minWidth: 0 }}>
                <div className={styles.formGroup}>
                  <label>Start Date</label>
                  <input
                    type="date"
                    value={promoForm.startDate}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>End Date</label>
                  <input
                    type="date"
                    value={promoForm.endDate}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Description</label>
                  <textarea
                    value={promoForm.description}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter promotion description"
                    rows={5}
                  />
                </div>
              </div>
            </div>
            <div className={styles.modalActions}>
              <button className={styles.secondaryButton} onClick={handleCloseCreatePromoModal}>
                Cancel
              </button>
              <button className={styles.primaryButton} onClick={handleSavePromo}>
                Create Promotion
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Promotion Details Modal */}
      {modalOpen && selectedPromo && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal} style={{ maxWidth: 800, minWidth: 500 }}>
            <div className={styles.modalHeader}>
              <h2>{selectedPromo.name}</h2>
              <button className={styles.closeButton} onClick={handleCloseModal}>×</button>
            </div>
            <div className={styles.modalContent}>
              <div className={styles.promoDetails}>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Type:</span>
                  <span className={styles.detailValue}>{selectedPromo.type}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Offer:</span>
                  <span className={styles.detailValue}>{selectedPromo.discount}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Status:</span>
                  <span className={styles.detailValue} style={{ color: getStatusColor(selectedPromo.status) }}>
                    {selectedPromo.status}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Duration:</span>
                  <span className={styles.detailValue}>
                    {new Date(selectedPromo.startDate).toLocaleDateString()} - {new Date(selectedPromo.endDate).toLocaleDateString()}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Target Audience:</span>
                  <span className={styles.detailValue}>{selectedPromo.targetAudience}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Redemptions:</span>
                  <span className={styles.detailValue}>{selectedPromo.redemptionCount}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Total Revenue:</span>
                  <span className={styles.detailValue}>${selectedPromo.totalRevenue}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Cost:</span>
                  <span className={styles.detailValue}>${selectedPromo.cost}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>ROI:</span>
                  <span className={styles.detailValue} style={{ color: selectedPromo.roi > 0 ? '#10b981' : '#ef4444' }}>
                    {selectedPromo.roi > 0 ? '+' : ''}${selectedPromo.roi}
                  </span>
                </div>
              </div>
            </div>
            <div className={styles.modalActions}>
              <button className={styles.secondaryButton} onClick={handleCloseModal}>
                Close
              </button>
              <button
                className={styles.primaryButton}
                onClick={() => {
                  if (selectedPromo.status === 'Active') {
                    setPromoToDeactivate(selectedPromo);
                    setShowDeactivateDialog(true);
                  } else {
                    // Could open edit modal here if desired
                    handleCloseModal();
                  }
                }}
              >
                Edit Promotion
              </button>
            </div>
          </div>
          {/* Deactivate Dialog */}
          {showDeactivateDialog && promoToDeactivate && (
            <div className={styles.modalOverlay}>
              <div className={styles.modal} style={{ maxWidth: 400, minWidth: 300, textAlign: 'center' }}>
                <h3>Deactivate Promotion?</h3>
                <p>Are you sure you want to deactivate <b>{promoToDeactivate.name}</b>?</p>
                <div className={styles.modalActions} style={{ justifyContent: 'center' }}>
                  <button className={styles.secondaryButton} onClick={() => setShowDeactivateDialog(false)}>
                    Cancel
                  </button>
                  <button
                    className={styles.primaryButton}
                    onClick={() => {
                      // Mock: set status to Inactive
                      promoToDeactivate.status = 'Inactive';
                      setShowDeactivateDialog(false);
                      setSelectedPromo({ ...promoToDeactivate });
                    }}
                  >
                    Deactivate
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PromosTab; 