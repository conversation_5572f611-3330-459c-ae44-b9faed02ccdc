.backOfficeContainer {
  background: #f8fafc;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  min-height: 80vh;
}

.tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.tab {
  background: none;
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  color: #64748b;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.activeTab {
  background: #fff;
  color: #0f172a;
  font-weight: 600;
  border-bottom: 2px solid #38bdf8;
}

.tabContent {
  background: #fff;
  border-radius: 0 0 12px 12px;
  padding: 24px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.summaryTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.heading {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0f172a;
}

.shiftsTableWrapper {
  overflow-x: auto;
}

.shiftsTable {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.shiftsTable th, .shiftsTable td {
  padding: 12px 16px;
  text-align: left;
}

.shiftsTable th {
  background: #f1f5f9;
  color: #64748b;
  font-weight: 500;
}

.shiftsTable tr:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.employeeInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #38bdf8;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
}

.employeeName {
  font-weight: 600;
  color: #0f172a;
}

.employeeRole {
  font-size: 0.9rem;
  color: #64748b;
}

.statusApproved {
  background: #d1fae5;
  color: #059669;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
}

.statusPending {
  background: #fef3c7;
  color: #b45309;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
}

.actionIcon {
  margin-right: 8px;
  cursor: pointer;
  font-size: 1.1rem;
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 20px;
}

.templateCard {
  background: #f1f5f9;
  border-radius: 10px;
  padding: 18px 20px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.templateHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.templateType {
  background: #bae6fd;
  color: #0369a1;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateTypeSMS {
  background: #fef3c7;
  color: #b45309;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateTypeInApp {
  background: #e0e7ff;
  color: #3730a3;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusActive {
  background: #d1fae5;
  color: #059669;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusDraft {
  background: #fef3c7;
  color: #b45309;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateStatusInactive {
  background: #f1f5f9;
  color: #64748b;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.templateName {
  font-weight: 600;
  color: #0f172a;
  font-size: 1.05rem;
}

.templateMeta {
  color: #64748b;
  font-size: 0.92rem;
}

.templateActions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.editButton {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.editButton:hover {
  background: #e2e8f0;
  color: #334155;
  border-color: #94a3b8;
}

.marketingTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.templatesSection {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px 24px 18px 24px;
  margin-bottom: 0;
}

.templatesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.templatesActions {
  display: flex;
  gap: 12px;
}

.primaryButton {
  background: #38bdf8;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.primaryButton:hover {
  background: #0ea5e9;
}

.secondaryButton {
  background: #f1f5f9;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.secondaryButton:hover {
  background: #e2e8f0;
}

.analyticsSection {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.analyticsTableWrapper {
  overflow-x: auto;
}

.analyticsTable {
  width: 100%;
  border-collapse: collapse;
}

.analyticsTable th, .analyticsTable td {
  padding: 12px 16px;
  text-align: left;
}

.analyticsTable th {
  background: #f1f5f9;
  color: #64748b;
  font-weight: 500;
}

.analyticsTable tr:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.analyticsCircle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.analyticsLabel {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #334155;
}

.moreButton {
  background: none;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 6px 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.moreButton:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalContent {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  width: 90%;
  max-width: 900px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.templateEditorContainer {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
  background: #f8fafc;
  padding: 24px;
  border-radius: 8px;
}

.templateEditorHeader {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.templateEditorActions {
  display: flex;
  gap: 12px;
}

.templateEditorMeta {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.templateTypeSwitcher {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 4px;
  display: flex;
}

.templateTypeSelect {
  padding: 6px 12px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: #64748b;
  cursor: pointer;
}

.templateTypeSelect.active {
  background: #fff;
  color: #0f172a;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.templateSubjectInput {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #fff;
  font-size: 1rem;
}

.templateEditorBox {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.templateEditorToolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.toolbarButton {
  background: none;
  border: none;
  padding: 4px;
  font-size: 1.2rem;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s;
}

.toolbarButton:hover {
  background: #e2e8f0;
}

.toolbarSelect {
  border: none;
  background: transparent;
  padding: 4px;
  color: #334155;
}

.toolbarSpacer {
  height: 20px;
  width: 1px;
  background: #e2e8f0;
}

.variableDropdown {
  position: relative;
}

.variableDropdown button {
  background: #e2e8f0;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
}

.templateEditorTextarea {
  width: 100%;
  height: 300px;
  padding: 12px;
  border: none;
  resize: vertical;
  font-family: inherit;
  font-size: 1rem;
}

.rulesSection {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.rulesHeading {
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 8px;
}

.rulesRow {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rulesLabel {
  color: #64748b;
  font-size: 0.9rem;
  flex-shrink: 0;
  width: 100px;
}

.rulesFlow {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rulesNode {
  background: #f1f5f9;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 0.9rem;
  color: #334155;
  border: 1px solid #e2e8f0;
}

.rulesArrow {
  color: #94a3b8;
}

.rulesDelay {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rulesSelect {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px;
  background: #fff;
  font-size: 0.9rem;
}

.rulesMiniBtn {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 2px 6px;
  cursor: pointer;
  color: #64748b;
}

.rulesMiniBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rulesMiniBtn:hover:not(:disabled) {
  background: #f8fafc;
}

.rulesAddBtn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: 1px dashed #cbd5e1;
  border-radius: 6px;
  padding: 6px 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  margin-top: 12px;
}

.rulesAddBtn:hover {
  background: #f8fafc;
  border-color: #94a3b8;
}

.templateEditorQuill .ql-editor {
  min-height: 250px;
}

.templateEditorQuill {
  background: #fff;
}

.smsValidation {
  font-size: 0.85rem;
  color: #64748b;
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
}

.smsWarning {
  color: #f59e0b;
}

.smsPreview {
  margin-top: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f1f5f9;
  font-family: monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.inAppPreview {
  margin-top: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.accountingTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.metricsRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.metricCard {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.metricIcon {
  font-size: 2rem;
  color: #38bdf8;
}

.metricValue {
  font-size: 1.75rem;
  font-weight: 700;
  color: #0f172a;
}

.metricLabel {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 4px;
}

.metricSub {
  font-size: 0.85rem;
  color: #94a3b8;
}

.metricChange {
  font-size: 0.9rem;
  font-weight: 600;
}

.metricExtra {
  font-size: 0.8rem;
  color: #94a3b8;
  margin-top: 4px;
}

.pnlCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.pnlHeader {
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.pnlBody {
  padding: 24px;
}

.chartsRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.chartCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px;
  height: 350px;
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.timeframeSelect {
  background: #f1f5f9;
  border: none;
  border-radius: 6px;
  padding: 4px 8px;
}

.reconcileBtn {
  background: #10b981;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.reconcileBtn:hover {
  background: #059669;
}

.opexInsights {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.reconcileModal {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.reconcileModal .modalContent {
  width: 90%;
  max-width: 600px;
}

.modalCloseBtn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.filtersRow {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
}

.filterSelect {
  min-width: 150px;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.exportBtn {
  background: #f1f5f9;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
  margin-left: auto;
}

.exportBtn:hover {
  background: #e2e8f0;
}

.dateRangeWrapper {
  position: relative;
}

.dateRangeBtn {
  background: #fff;
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dateRangeBtn:hover {
  background: #f8fafc;
}

.dateRangePickerPopover {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 100;
}

.campaignsSection {
  margin-top: 24px;
}

.campaignsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.campaignsActions {
  display: flex;
  gap: 12px;
}

.campaignsTableWrapper {
  overflow-x: auto;
}

.campaignsTable {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.campaignsTable th, .campaignsTable td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.campaignsTable th {
  background: #f8fafc;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.8rem;
}

.campaignsTable tr:hover {
  background: #f8fafc;
}

.campaignName {
  font-weight: 600;
  color: #38bdf8;
  cursor: pointer;
}

.campaignForm {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formGroup label {
  font-weight: 500;
  color: #334155;
}

.formInput, .formSelect {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background: #fff;
  width: 100%;
}

.formInput:focus, .formSelect:focus {
  outline: none;
  border-color: #38bdf8;
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.recipientFilters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.filterRow {
  display: flex;
  gap: 12px;
  align-items: center;
}

.recipientHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.recipientActions {
  display: flex;
  gap: 8px;
}

.recipientsList {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.recipientItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
}

.recipientItem:last-child {
  border-bottom: none;
}

.recipientItem:hover {
  background: #f8fafc;
}

.recipientCheckbox {
  margin-right: 12px;
  accent-color: #38bdf8;
  width: 16px;
  height: 16px;
}

.recipientCheckbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #38bdf8;
}

.recipientInfo {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.recipientInfo strong {
  font-weight: 500;
  color: #0f172a;
}

.recipientInfo span {
  font-size: 0.9rem;
  color: #64748b;
}

.recipientTags {
  display: flex;
  gap: 6px;
  align-items: center;
}

.recipientTag {
  background: #e0f2fe;
  color: #0ea5e9;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 500;
}

.modalActions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.modalContent {
  padding: 20px;
}

.modalContent h2 {
  margin-top: 0;
  font-size: 1.5rem;
  color: #0f172a;
}

.modalContent h3 {
  margin-top: 24px;
  margin-bottom: 12px;
  font-size: 1.2rem;
  color: #334155;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 8px;
}

.templateEditorLayout {
  display: grid;
  grid-template-columns: 320px 1fr 280px;
  gap: 32px;
  height: calc(100vh - 120px);
  padding: 32px;
  background: #f3f4f6;
}

.previewPanel {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 32px 0;
}

.previewCard {
  width: 100%;
  max-width: 700px;
  min-height: 480px;
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.10);
  overflow: hidden;
  position: relative;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  padding: 0 0 32px 0;
  margin-top: 8px;
}

.editorActionButtons {
  position: absolute;
  right: 32px;
  bottom: 32px;
  z-index: 10;
  display: flex;
  gap: 12px;
}

.blockPalettePanel {
  background: #fff;
  border-radius: 18px;
  padding: 28px 18px 28px 18px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  height: fit-content;
  min-width: 260px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px 14px;
  align-items: stretch;
}

.blockPalettePanel h4 {
  grid-column: 1 / -1;
  font-weight: 700;
  color: #334155;
  margin-bottom: 10px;
  font-size: 1.1rem;
  letter-spacing: 0.01em;
  text-align: center;
}

.blockPaletteItem, .blockPalettePro {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 0 10px 0;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  margin-bottom: 0;
  cursor: grab;
  background: #f8fafc;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  transition: border-color 0.2s, box-shadow 0.2s, background 0.2s;
  min-height: 70px;
  min-width: 0;
  font-size: 1.02rem;
  font-weight: 500;
  user-select: none;
}

.blockPaletteItem:hover, .blockPalettePro:hover {
  border-color: #6366f1;
  background: #eef2ff;
  box-shadow: 0 4px 12px rgba(99,102,241,0.08);
}

.blockPaletteIcon {
  font-size: 1.5rem;
  color: #6366f1;
  margin-bottom: 4px;
}

.proBadge {
  background: #f59e0b;
  color: #fff;
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 8px;
}

.previewBlock {
  padding: 20px 32px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
  background: transparent;
}

.previewBlock:last-child {
  border-bottom: none;
}

.settingsPanel {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.formGroup input:focus,
.formGroup select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.marketingTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Promo-specific styles */
.promoMetrics {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 60px;
}

.metricLabel {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

.metricValue {
  font-size: 0.95rem;
  font-weight: 600;
  color: #0f172a;
}

.analyticsSection {
  margin-top: 40px;
}

.analyticsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.analyticsCard {
  background: #f8fafc;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.analyticsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.analyticsName {
  font-weight: 600;
  color: #0f172a;
  font-size: 1.1rem;
  margin: 0;
}

.analyticsType {
  background: #bae6fd;
  color: #0369a1;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
}

.analyticsMetrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.analyticsMetric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.segmentsSection {
  margin-top: 40px;
}

.segmentsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.segmentCard {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.segmentCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.segmentName {
  font-weight: 600;
  color: #0f172a;
  font-size: 1rem;
  margin: 0 0 8px 0;
}

.segmentCount {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0 0 12px 0;
}

.segmentTags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.segmentTag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.promoDetails {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detailRow:last-child {
  border-bottom: none;
}

.detailLabel {
  font-weight: 500;
  color: #64748b;
  font-size: 0.9rem;
}

.detailValue {
  font-weight: 600;
  color: #0f172a;
  font-size: 0.9rem;
}

.templateDescription {
  color: #64748b;
  font-size: 0.9rem;
  margin: 4px 0 8px 0;
}

.templateDate {
  color: #64748b;
  font-size: 0.85rem;
}

.templateAudience {
  color: #64748b;
  font-size: 0.85rem;
  font-weight: 500;
}

.templateStatus {
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
}

.viewButton {
  background: #38bdf8;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 12px;
  align-self: flex-start;
}

.viewButton:hover {
  background: #0ea5e9;
}

.analyticsModalGrid {
  display: flex;
  flex-direction: row;
  gap: 32px;
  margin-bottom: 24px;
  align-items: flex-start;
}

.analyticsDonuts {
  display: flex;
  flex-direction: row;
  gap: 18px;
  background: #fff;
  border-radius: 12px;
  padding: 18px 18px 8px 18px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.analyticsModalDetails {
  min-width: 180px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 1rem;
  color: #374151;
  background: #f8fafc;
  border-radius: 10px;
  padding: 18px 18px 8px 18px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
