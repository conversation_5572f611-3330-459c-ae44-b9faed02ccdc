import { styled } from '@mui/material/styles';
import { Box, Typography, CircularProgress } from '@mui/material';

export const TeeTimeCardContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isHoverable',
})<{ isHoverable?: boolean }>(({ theme, isHoverable = true }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  transition: theme.transitions.create(['box-shadow', 'transform']),
  ...(isHoverable && {
    '&:hover': {
      boxShadow: theme.shadows[4],
      transform: 'translateY(-2px)',
    },
  }),
}));

export const CardImage = styled('img')({
  width: 120,
  height: 80,
  objectFit: 'cover',
  borderRadius: 4,
});

export const CardContent = styled(Box)({
  flex: 1,
});

export const CardTitle = styled('h3')(({ theme }) => ({
  margin: 0,
  fontSize: '1.1rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
}));

export const CardDescription = styled('p')(({ theme }) => ({
  margin: theme.spacing(0.5, 0),
  fontSize: '0.9rem',
  color: theme.palette.text.secondary,
}));

export const CardRightContent = styled(Box)({
  display: 'flex',
  alignItems: 'center',
});

export const LoadingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.7)',
  borderRadius: theme.shape.borderRadius,
}));

export const ErrorMessage = styled('div')(({ theme }) => ({
  color: theme.palette.error.main,
  fontSize: '0.9rem',
  marginTop: theme.spacing(0.5),
})); 