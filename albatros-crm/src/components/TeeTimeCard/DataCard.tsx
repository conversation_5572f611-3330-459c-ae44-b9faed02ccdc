import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import { TeeTimeCardProps } from './types';

const CardContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isHoverable',
})<{ isHoverable?: boolean }>(({ theme, isHoverable = true }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  transition: theme.transitions.create(['box-shadow', 'transform']),
  ...(isHoverable && {
    '&:hover': {
      boxShadow: theme.shadows[4],
      transform: 'translateY(-2px)',
    },
  }),
}));

const CardImage = styled('img')({
  width: 120,
  height: 80,
  objectFit: 'cover',
  borderRadius: 4,
});

const CardContent = styled(Box)({
  flex: 1,
});

const CardTitle = styled(Typography)(({ theme }) => ({
  margin: 0,
  fontSize: '1.1rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
}));

const CardDescription = styled(Typography)(({ theme }) => ({
  margin: theme.spacing(0.5, 0),
  fontSize: '0.9rem',
  color: theme.palette.text.secondary,
}));

const CardRightContent = styled(Box)({
  display: 'flex',
  alignItems: 'center',
});

const LoadingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.7)',
  borderRadius: theme.shape.borderRadius,
}));

const ErrorMessage = styled(Typography)(({ theme }) => ({
  color: theme.palette.error.main,
  fontSize: '0.9rem',
  marginTop: theme.spacing(0.5),
}));

export const TeeTimeCard: React.FC<TeeTimeCardProps> = ({
  image,
  title,
  description,
  rightContent,
  className,
  onClick,
  isHoverable,
  ariaLabel,
  isLoading,
  error,
}) => {
  return (
    <CardContainer
      className={className}
      isHoverable={isHoverable}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      aria-label={ariaLabel}
    >
      {image && <CardImage src={image} alt={title} />}
      <CardContent>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
        {error && <ErrorMessage>{error}</ErrorMessage>}
      </CardContent>
      {rightContent && <CardRightContent>{rightContent}</CardRightContent>}
      {isLoading && (
        <LoadingOverlay>
          <CircularProgress size={24} />
        </LoadingOverlay>
      )}
    </CardContainer>
  );
}; 