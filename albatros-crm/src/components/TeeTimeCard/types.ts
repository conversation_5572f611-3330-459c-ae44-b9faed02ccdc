import { ReactNode } from 'react';

/**
 * Props for the TeeTimeCard component
 */
export interface TeeTimeCardProps {
  /** The image to display in the card */
  image?: string;
  /** The title text to display */
  title: string;
  /** The description text to display */
  description?: string;
  /** Optional content to display in the right section */
  rightContent?: ReactNode;
  /** Optional className for additional styling */
  className?: string;
  /** Optional callback when the card is clicked */
  onClick?: () => void;
  /** Whether the card should be hoverable */
  isHoverable?: boolean;
  /** Optional aria-label for better accessibility */
  ariaLabel?: string;
  /** Optional loading state */
  isLoading?: boolean;
  /** Optional error state */
  error?: string;
} 