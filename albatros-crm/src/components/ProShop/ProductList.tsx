import React from 'react';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Grid,
  IconButton,
  Typography,
  styled,
  useTheme,
  Tooltip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Smartphone as SmartphoneIcon,
} from '@mui/icons-material';
import { Product } from './types';

interface ProductListProps {
  items: Product[];
  onMobileAppToggle: (itemId: string, currentValue: boolean) => Promise<void>;
  onEditItem: (item: Product) => void;
  onDeleteItem?: (item: Product) => void;
  isEditMode: boolean;
}

const StyledCard = styled(Card)<{ isEditMode?: boolean }>(({ theme, isEditMode }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[2],
  },
  cursor: 'pointer',
  position: 'relative',
  ...(isEditMode && {
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      border: '2px solid transparent',
      transition: 'all 0.3s ease',
    },
    '&:hover::after': {
      borderColor: theme.palette.primary.main,
    },
  }),
}));

const StyledCardMedia = styled(CardMedia)(({ theme }) => ({
  height: 200,
  backgroundSize: 'contain',
  backgroundPosition: 'center',
  backgroundColor: theme.palette.grey[100],
  position: 'relative',
  padding: theme.spacing(2),
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.4) 100%)',
  },
}));

const DetailImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: '250px',
  objectFit: 'contain',
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
}));

const PriceChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  fontWeight: 'bold',
  '& .MuiChip-label': {
    padding: theme.spacing(0.5, 1),
  },
}));

const StockChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  left: theme.spacing(1),
  fontWeight: 'bold',
  '& .MuiChip-label': {
    padding: theme.spacing(0.5, 1),
  },
}));

const MobileAppIcon = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  right: theme.spacing(1),
  color: theme.palette.primary.main,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

const ProductList: React.FC<ProductListProps> = ({
  items,
  onMobileAppToggle,
  onEditItem,
  onDeleteItem,
  isEditMode,
}) => {
  const theme = useTheme();

  const handleItemClick = (item: Product) => {
    onEditItem(item);
  };

  const handleDelete = (e: React.MouseEvent, item: Product) => {
    e.stopPropagation();
    if (onDeleteItem) {
      onDeleteItem(item);
    }
  };

  const handleMobileAppClick = async (e: React.MouseEvent, item: Product) => {
    e.stopPropagation();
    await onMobileAppToggle(item.id, item.mobileApp);
  };

  return (
    <Grid container spacing={3}>
      {items.map((item) => (
        <Grid item xs={12} sm={6} md={4} key={item.id}>
          <StyledCard 
            onClick={() => handleItemClick(item)}
            isEditMode={isEditMode}
          >
            {isEditMode && onDeleteItem && (
              <IconButton
                size="small"
                color="error"
                onClick={(e) => handleDelete(e, item)}
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  zIndex: 2,
                  bgcolor: 'background.paper',
                  '&:hover': {
                    bgcolor: 'error.light',
                    color: 'error.contrastText',
                  },
                }}
              >
                <DeleteIcon />
              </IconButton>
            )}
            <StyledCardMedia
              image={item.imageUrl || '/images/placeholder.jpg'}
              title={item.name}
            >
              <PriceChip 
                label={`$${item.price.toFixed(2)}`}
                color="primary"
              />
              <StockChip 
                label={item.inStock ? `In Stock (${item.quantity})` : 'Out of Stock'}
                color={item.inStock ? "success" : "error"}
              />
            </StyledCardMedia>
            <CardContent sx={{ position: 'relative', pb: 4 }}>
              <Typography variant="h6" gutterBottom>
                {item.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {item.description}
              </Typography>
              {item.brand && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Brand: {item.brand}
                </Typography>
              )}
              {item.mobileApp && !isEditMode && (
                <Tooltip title="Available in Mobile App">
                  <MobileAppIcon>
                    <SmartphoneIcon fontSize="small" />
                  </MobileAppIcon>
                </Tooltip>
              )}
              {isEditMode && (
                <Tooltip title={item.mobileApp ? 'Remove from Mobile App' : 'Add to Mobile App'}>
                  <IconButton
                    size="small"
                    color={item.mobileApp ? "primary" : "default"}
                    onClick={(e) => handleMobileAppClick(e, item)}
                    sx={{
                      position: 'absolute',
                      bottom: theme.spacing(1),
                      right: theme.spacing(1),
                      bgcolor: 'background.paper',
                      '&:hover': {
                        bgcolor: item.mobileApp ? 'primary.light' : 'action.hover',
                      },
                    }}
                  >
                    <SmartphoneIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </CardContent>
          </StyledCard>
        </Grid>
      ))}
    </Grid>
  );
};

export default ProductList; 