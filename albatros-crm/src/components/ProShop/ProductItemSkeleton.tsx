import React from 'react';
import { Box, Card, CardContent, Grid, Skeleton, styled } from '@mui/material';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const ProductItemSkeleton: React.FC = () => {
  return (
    <Grid container spacing={3} sx={{ maxHeight: 400 }}>
      {[...Array(3)].map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <StyledCard>
            {/* Image area with chips */}
            <Box sx={{ position: 'relative', height: 200, bgcolor: 'grey.100', p: 2 }}>
              <Skeleton 
                variant="rectangular" 
                width="100%" 
                height="100%" 
                sx={{ 
                  bgcolor: 'grey.300',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: 'calc(100% - 32px)', // Accounting for padding
                  height: 'calc(100% - 32px)', // Accounting for padding
                }} 
              />
              {/* Price chip */}
              <Skeleton 
                variant="rectangular" 
                width={80} 
                height={32} 
                sx={{ 
                  position: 'absolute',
                  bottom: 8,
                  right: 8,
                  borderRadius: 1
                }} 
              />
              {/* Stock chip */}
              <Skeleton 
                variant="rectangular" 
                width={100} 
                height={32} 
                sx={{ 
                  position: 'absolute',
                  bottom: 8,
                  left: 8,
                  borderRadius: 1
                }} 
              />
            </Box>
            
            <CardContent>
              {/* Title */}
              <Skeleton variant="text" width="80%" height={32} sx={{ mb: 1 }} />
              {/* Description */}
              <Skeleton variant="text" width="100%" height={20} sx={{ mb: 0.5 }} />
              <Skeleton variant="text" width="90%" height={20} sx={{ mb: 1 }} />
              {/* Brand */}
              <Skeleton variant="text" width="40%" height={16} />
            </CardContent>
          </StyledCard>
        </Grid>
      ))}
    </Grid>
  );
};

export default ProductItemSkeleton; 