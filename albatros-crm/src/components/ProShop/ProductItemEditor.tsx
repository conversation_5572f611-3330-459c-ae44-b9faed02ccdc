import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Switch,
  FormControlLabel,
  Box,
  Typography,
  styled,
  Chip,
} from '@mui/material';
import { ProductEditorProps, Product } from './types';
import { MenuItemImage } from '../NineteenthHole/MenuItemImage';

const DetailDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    maxWidth: '600px',
    width: '100%',
    borderRadius: theme.shape.borderRadius,
    overflow: 'hidden',
  },
}));

const DetailImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: '250px',
  objectFit: 'contain',
  marginBottom: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

const DetailSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '&:last-child': {
    marginBottom: 0,
  },
}));

const DetailTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.main,
  fontWeight: 'bold',
}));

const defaultProduct: Product = {
  id: '',
  name: '',
  description: '',
  price: 0,
  category: '',
  imageUrl: '',
  inStock: true,
  quantity: 0,
  brand: '',
  size: '',
  color: '',
  mobileApp: true,
  order: 0,
};

const ProductItemEditor: React.FC<ProductEditorProps> = ({
  open,
  onClose,
  onSave,
  categories,
  item,
  isEditMode = false,
}) => {
  const [editedItem, setEditedItem] = useState<Product>(defaultProduct);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (item) {
      setEditedItem(item);
    } else {
      setEditedItem({
        ...defaultProduct,
        id: Date.now().toString(),
        order: categories.length + 1,
      });
    }
  }, [item, categories]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!editedItem.name.trim()) {
      newErrors.name = 'Name is required';
    }
    if (!editedItem.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (editedItem.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }
    if (!editedItem.category) {
      newErrors.category = 'Category is required';
    }
    if (editedItem.quantity < 0) {
      newErrors.quantity = 'Quantity cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field: keyof Product, value: any) => {
    setEditedItem(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when field is edited
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(editedItem);
      onClose();
    }
  };

  if (!isEditMode) {
    return (
      <DetailDialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>{editedItem.name}</DialogTitle>
        <DialogContent>
          <DetailImage src={editedItem.imageUrl} alt={editedItem.name} />
          
          <DetailSection>
            <DetailTitle>Description</DetailTitle>
            <Typography>{editedItem.description}</Typography>
          </DetailSection>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Price</DetailTitle>
                <Typography variant="h6" color="primary">
                  ${editedItem.price.toFixed(2)}
                </Typography>
              </DetailSection>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Category</DetailTitle>
                <Chip 
                  label={categories.find(c => c.name === editedItem.category)?.displayName || editedItem.category}
                  color="primary"
                  variant="outlined"
                />
              </DetailSection>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Brand</DetailTitle>
                <Typography>{editedItem.brand || 'N/A'}</Typography>
              </DetailSection>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Size</DetailTitle>
                <Typography>{editedItem.size || 'N/A'}</Typography>
              </DetailSection>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Color</DetailTitle>
                <Typography>{editedItem.color || 'N/A'}</Typography>
              </DetailSection>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DetailSection>
                <DetailTitle>Quantity</DetailTitle>
                <Typography>{editedItem.quantity}</Typography>
              </DetailSection>
            </Grid>

            <Grid item xs={12}>
              <DetailSection>
                <DetailTitle>Status</DetailTitle>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Chip 
                    label={editedItem.inStock ? 'In Stock' : 'Out of Stock'} 
                    color={editedItem.inStock ? 'success' : 'error'} 
                  />
                  <Chip 
                    label={editedItem.mobileApp ? 'Available in App' : 'Not in App'} 
                    color={editedItem.mobileApp ? 'primary' : 'default'} 
                  />
                </Box>
              </DetailSection>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </DetailDialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {item ? 'Edit Product' : 'Add New Product'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Image Preview */}
          <Grid item xs={12}>
            <Box 
              sx={{ 
                width: '100%', 
                height: 300,
                mb: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                borderRadius: 1
              }}
            >
              {editedItem.imageUrl ? (
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    '& img': {
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }
                  }}
                >
                  <MenuItemImage src={editedItem.imageUrl} alt={editedItem.name} />
                </Box>
              ) : (
                <Typography color="text.secondary">
                  No image available
                </Typography>
              )}
            </Box>
          </Grid>

          {/* Image URL Input */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Image URL"
              value={editedItem.imageUrl}
              onChange={(e) => handleChange('imageUrl', e.target.value)}
              placeholder="Enter image URL"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Name"
              value={editedItem.name}
              onChange={(e) => handleChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={editedItem.description}
              onChange={(e) => handleChange('description', e.target.value)}
              error={!!errors.description}
              helperText={errors.description}
              multiline
              rows={3}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.category}>
              <InputLabel>Category</InputLabel>
              <Select
                value={editedItem.category}
                onChange={(e) => handleChange('category', e.target.value)}
                label="Category"
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.name}>
                    {category.displayName}
                  </MenuItem>
                ))}
              </Select>
              {errors.category && (
                <Typography color="error" variant="caption">
                  {errors.category}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Price"
              type="number"
              value={editedItem.price}
              onChange={(e) => handleChange('price', parseFloat(e.target.value))}
              error={!!errors.price}
              helperText={errors.price}
              required
              InputProps={{
                startAdornment: <Typography>$</Typography>,
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Brand"
              value={editedItem.brand}
              onChange={(e) => handleChange('brand', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Quantity"
              type="number"
              value={editedItem.quantity}
              onChange={(e) => handleChange('quantity', parseInt(e.target.value, 10))}
              error={!!errors.quantity}
              helperText={errors.quantity}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Size"
              value={editedItem.size}
              onChange={(e) => handleChange('size', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Color"
              value={editedItem.color}
              onChange={(e) => handleChange('color', e.target.value)}
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={editedItem.inStock}
                  onChange={(e) => handleChange('inStock', e.target.checked)}
                />
              }
              label="In Stock"
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={editedItem.mobileApp}
                  onChange={(e) => handleChange('mobileApp', e.target.checked)}
                />
              }
              label="Show in Mobile App"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductItemEditor; 