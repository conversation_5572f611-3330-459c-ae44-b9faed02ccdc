import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Avatar,
  styled,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Inventory as InventoryIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const ReportsTab: React.FC = () => {
  return (
    <Box>
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.light', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6">Total Revenue</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$124,550</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +15.3% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.light', mr: 2 }}>
                  <ShoppingCartIcon />
                </Avatar>
                <Typography variant="h6">Total Sales</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>1,234</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +8.2% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.light', mr: 2 }}>
                  <InventoryIcon />
                </Avatar>
                <Typography variant="h6">Low Stock</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>12</Typography>
              <Typography variant="body2" color="warning.main">
                Items need restock
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.light', mr: 2 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Typography variant="h6">Avg. Order Value</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$98.50</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +5.7% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Top Products */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Top Selling Products</Typography>
          {[
            { name: 'Pro V1 Golf Balls', sales: 245, progress: 90 },
            { name: 'Golf Gloves - Premium', sales: 189, progress: 75 },
            { name: 'Titleist Driver', sales: 156, progress: 65 },
            { name: 'Golf Tees Pack', sales: 134, progress: 55 },
          ].map((product, index) => (
            <Box key={index} sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">{product.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {product.sales} sales
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={product.progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'primary.light',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: 'primary.main',
                  },
                }}
              />
            </Box>
          ))}
        </CardContent>
      </Card>

      {/* Monthly Revenue */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Monthly Revenue</Typography>
          <Box sx={{ height: 300, display: 'flex', alignItems: 'flex-end', gap: 2 }}>
            {[65, 85, 73, 92, 88, 95, 78, 89, 84, 92, 95, 98].map((value, index) => (
              <Box
                key={index}
                sx={{
                  height: `${value}%`,
                  flex: 1,
                  backgroundColor: 'primary.main',
                  borderRadius: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                    transform: 'scaleY(1.02)',
                  },
                }}
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month) => (
              <Typography key={month} variant="body2" color="text.secondary">
                {month}
              </Typography>
            ))}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ReportsTab; 