import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Avatar,
  Chip,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  styled,
} from '@mui/material';
import {
  Search as SearchIcon,
  LocalShipping as ShippingIcon,
  Inventory as InventoryIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingIcon,
  Pending as PendingIcon,
  LocalShipping as DeliveryIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const OrderCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[3],
  },
}));

const StatusChip = styled(Chip)(({ theme }) => ({
  fontWeight: 600,
  '&.pending': {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.dark,
  },
  '&.processing': {
    backgroundColor: theme.palette.info.light,
    color: theme.palette.info.dark,
  },
  '&.shipped': {
    backgroundColor: theme.palette.success.light,
    color: theme.palette.success.dark,
  },
  '&.cancelled': {
    backgroundColor: theme.palette.error.light,
    color: theme.palette.error.dark,
  },
}));

const OrdersTab: React.FC = () => {
  return (
    <Box>
      {/* Statistics Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.light', mr: 2 }}>
                  <ShippingIcon />
                </Avatar>
                <Typography variant="h6">Total Orders</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>156</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingIcon sx={{ mr: 0.5 }} />
                +12.5% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.light', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6">Revenue</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$24,550</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingIcon sx={{ mr: 0.5 }} />
                +8.2% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.light', mr: 2 }}>
                  <PendingIcon />
                </Avatar>
                <Typography variant="h6">Pending</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>23</Typography>
              <Typography variant="body2" color="text.secondary">
                Awaiting processing
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.light', mr: 2 }}>
                  <DeliveryIcon />
                </Avatar>
                <Typography variant="h6">Shipped</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>45</Typography>
              <Typography variant="body2" color="text.secondary">
                In transit
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Box sx={{ 
        display: 'flex', 
        gap: 2, 
        mb: 4,
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'stretch', sm: 'center' },
      }}>
        <TextField
          placeholder="Search orders..."
          variant="outlined"
          size="small"
          sx={{ flex: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Status</InputLabel>
          <Select label="Status" defaultValue="all">
            <MenuItem value="all">All Orders</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="processing">Processing</MenuItem>
            <MenuItem value="shipped">Shipped</MenuItem>
            <MenuItem value="cancelled">Cancelled</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Time</InputLabel>
          <Select label="Time" defaultValue="all">
            <MenuItem value="all">All Time</MenuItem>
            <MenuItem value="today">Today</MenuItem>
            <MenuItem value="week">This Week</MenuItem>
            <MenuItem value="month">This Month</MenuItem>
            <MenuItem value="year">This Year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Orders List */}
      <Box>
        {[
          { id: '1', customer: 'John Smith', items: 3, total: 249.99, status: 'pending', date: '2024-03-15' },
          { id: '2', customer: 'Sarah Johnson', items: 1, total: 89.99, status: 'processing', date: '2024-03-14' },
          { id: '3', customer: 'Mike Brown', items: 2, total: 159.99, status: 'shipped', date: '2024-03-13' },
          { id: '4', customer: 'Emily Davis', items: 4, total: 399.99, status: 'cancelled', date: '2024-03-12' },
        ].map((order) => (
          <OrderCard key={order.id}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={4}>
                  <Typography variant="subtitle1" fontWeight={600}>
                    Order #{order.id}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {order.customer}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Items
                  </Typography>
                  <Typography variant="subtitle2">
                    {order.items} items
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Total
                  </Typography>
                  <Typography variant="subtitle2" fontWeight={600}>
                    ${order.total}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Date
                  </Typography>
                  <Typography variant="subtitle2">
                    {new Date(order.date).toLocaleDateString()}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2} sx={{ textAlign: 'right' }}>
                  <StatusChip
                    label={order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    className={order.status}
                    icon={
                      order.status === 'pending' ? <PendingIcon /> :
                      order.status === 'processing' ? <InventoryIcon /> :
                      order.status === 'shipped' ? <ShippingIcon /> :
                      <CancelIcon />
                    }
                  />
                </Grid>
                {order.status === 'shipped' && (
                  <Grid item xs={12}>
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Delivery Progress
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={75}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: 'success.light',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: 'success.main',
                          },
                        }}
                      />
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </OrderCard>
        ))}
      </Box>
    </Box>
  );
};

export default OrdersTab; 