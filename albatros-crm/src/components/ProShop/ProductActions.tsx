import React from 'react';
import { Box, TextField, InputAdornment, styled } from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    height: '48px',
    borderRadius: theme.shape.borderRadius * 2,
    backgroundColor: theme.palette.background.paper,
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    '&.Mui-focused': {
      backgroundColor: theme.palette.background.paper,
      boxShadow: theme.shadows[2],
    },
  },
}));

interface ProductActionsProps {
  onSearch: (term: string) => void;
  searchTerm: string;
}

const ProductActions: React.FC<ProductActionsProps> = ({
  onSearch,
  searchTerm,
}) => {
  return (
    <Box>
      <StyledTextField
        fullWidth
        placeholder="Search inventory items..."
        value={searchTerm}
        onChange={(e) => onSearch(e.target.value)}
        variant="outlined"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon color="action" />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};

export default ProductActions; 