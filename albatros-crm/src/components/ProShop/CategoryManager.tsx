import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  IconButton,
  TextField,
  Box,
  Switch,
  Typography,
  styled,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { CategoryManagerProps, ProductCategory } from './types';

const StyledListItem = styled(ListItem)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
}));

const CategoryManager: React.FC<CategoryManagerProps> = ({
  open,
  onClose,
  onSave,
  categories,
}) => {
  const [editedCategories, setEditedCategories] = useState<ProductCategory[]>(categories);
  const [newCategoryName, setNewCategoryName] = useState('');

  const handleToggleActive = (categoryId: string) => {
    setEditedCategories(prevCategories =>
      prevCategories.map(category =>
        category.id === categoryId
          ? { ...category, active: !category.active }
          : category
      )
    );
  };

  const handleDeleteCategory = (categoryId: string) => {
    setEditedCategories(prevCategories =>
      prevCategories.filter(category => category.id !== categoryId)
    );
  };

  const handleAddCategory = () => {
    if (!newCategoryName.trim()) return;

    const newCategory: ProductCategory = {
      id: Date.now().toString(),
      name: newCategoryName.toLowerCase().replace(/\s+/g, '-'),
      displayName: newCategoryName.trim(),
      order: editedCategories.length + 1,
      active: true,
    };

    setEditedCategories(prev => [...prev, newCategory]);
    setNewCategoryName('');
  };

  const handleSave = () => {
    onSave(editedCategories);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Manage Categories</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Add New Category
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              fullWidth
              size="small"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Category name"
            />
            <Button
              variant="contained"
              onClick={handleAddCategory}
              disabled={!newCategoryName.trim()}
              startIcon={<AddIcon />}
            >
              Add
            </Button>
          </Box>
        </Box>

        <Typography variant="subtitle2" gutterBottom>
          Existing Categories
        </Typography>
        <List>
          {editedCategories.map((category) => (
            <StyledListItem key={category.id}>
              <ListItemText primary={category.displayName} />
              <Switch
                checked={category.active}
                onChange={() => handleToggleActive(category.id)}
              />
              <IconButton
                onClick={() => handleDeleteCategory(category.id)}
                color="error"
              >
                <DeleteIcon />
              </IconButton>
            </StyledListItem>
          ))}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained" color="primary">
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CategoryManager; 