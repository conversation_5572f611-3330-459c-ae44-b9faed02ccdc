export type TabValue = 'menu' | 'orders' | 'inventory' | 'reports';

export interface ProductCategory {
  id: string;
  name: string;
  displayName: string;
  order: number;
  active: boolean;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  inStock: boolean;
  quantity: number;
  brand?: string;
  size?: string;
  color?: string;
  mobileApp: boolean;
  ingredients?: string[];
  order: number;
}

export interface ProductEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (item: Product) => void;
  categories: ProductCategory[];
  item?: Product;
  isEditMode?: boolean;
  onCategoriesChange: (categories: ProductCategory[]) => void;
}

export interface ProductListProps {
  items: Product[];
  onMobileAppToggle: (itemId: string, currentValue: boolean) => Promise<void>;
  onEditItem: (item: Product) => void;
  onDeleteItem?: (item: Product) => void;
  isEditMode: boolean;
}

export interface ProductActionsProps {
  onSearch: (term: string) => void;
  searchTerm: string;
}

export interface ProductCategoriesProps {
  categories: ProductCategory[];
  selectedCategory: string | null;
  onCategoryChange: (category: string | null) => void;
  isEditMode?: boolean;
  onReorderCategories?: (categories: ProductCategory[]) => void;
}

export interface CategoryManagerProps {
  open: boolean;
  onClose: () => void;
  onSave: (categories: ProductCategory[]) => void;
  categories: ProductCategory[];
} 