import React from 'react';
import { Shift } from './types';
import { useDraggable } from '@dnd-kit/core';

interface OpenShiftsRowProps {
  shifts: Shift[];
  weekDays: string[];
}

const OpenShiftBlock: React.FC<{ shift: Shift }> = ({ shift }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: shift.id,
  });
  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={{
        transform: transform ? `translate(${transform.x}px, ${transform.y}px)` : undefined,
        opacity: isDragging ? 0.5 : 1,
      }}
      className="bg-yellow-300 text-yellow-900 rounded px-2 py-1 mb-1 text-xs font-medium cursor-move border border-yellow-400"
    >
      {shift.role} ({new Date(shift.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(shift.end).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})
    </div>
  );
};

const OpenShiftsRow: React.FC<OpenShiftsRowProps> = ({ shifts, weekDays }) => (
  <div className="bg-yellow-50 border-b border-yellow-200 p-2 flex">
    <div className="w-56 font-semibold text-yellow-700">Open Shifts</div>
    {weekDays.map(date => (
      <div key={date} className="flex-1 min-w-[120px] px-1">
        {shifts.filter(s => s.start.startsWith(date)).map(s => (
          <OpenShiftBlock key={s.id} shift={s} />
        ))}
      </div>
    ))}
  </div>
);

export default OpenShiftsRow; 