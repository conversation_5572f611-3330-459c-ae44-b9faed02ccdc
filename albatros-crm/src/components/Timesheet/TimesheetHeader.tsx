import React from 'react';

interface TimesheetHeaderProps {
  weekDays: string[];
}

const TimesheetHeader: React.FC<TimesheetHeaderProps> = ({ weekDays }) => (
  <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
    <div className="text-lg font-semibold">Timesheet</div>
    <div className="flex space-x-4">
      {weekDays.map((date, idx) => (
        <div key={date} className="text-sm text-gray-700 font-medium">
          {new Date(date).toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' })}
        </div>
      ))}
    </div>
    {/* TODO: Add date navigation, view switcher, etc. */}
  </div>
);

export default TimesheetHeader; 