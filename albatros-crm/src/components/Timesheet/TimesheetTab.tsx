import React, { useState } from 'react';
import TimesheetHeader from './TimesheetHeader';
import EmployeeList from './EmployeeList';
import ScheduleGrid from './ScheduleGrid';
import OpenShiftsRow from './OpenShiftsRow';
import ShiftFormModal from './ShiftFormModal';
import SwapDropModal from './SwapDropModal';
import { Employee, Shift, TimesheetView } from './types';

// Mock employees
const employees: Employee[] = [
  { id: '1', name: '<PERSON>', role: 'Sous Chef', skills: ['kitchen'], locations: ['Main'], avatarUrl: '' },
  { id: '2', name: '<PERSON>', role: 'General Manager', skills: ['manager'], locations: ['Main'], avatarUrl: '' },
  { id: '3', name: '<PERSON>', role: 'Front of House', skills: ['foh'], locations: ['Main'], avatarUrl: '' },
  { id: '4', name: '<PERSON>', role: 'Pre<PERSON> <PERSON>', skills: ['kitchen'], locations: ['Main'], avatarUrl: '' },
  { id: '5', name: '<PERSON>', role: 'Bart<PERSON>', skills: ['bar'], locations: ['Main'], avatarUrl: '' },
];

// Mock shifts (some assigned, some open)
const initialShifts: Shift[] = [
  { id: 's1', employeeId: '1', start: '2024-07-29T16:00:00', end: '2024-07-30T00:00:00', role: 'Sous Chef', location: 'Main', status: 'scheduled' },
  { id: 's2', employeeId: '2', start: '2024-07-29T09:00:00', end: '2024-07-29T17:00:00', role: 'General Manager', location: 'Main', status: 'scheduled' },
  { id: 's3', employeeId: undefined, start: '2024-07-29T10:00:00', end: '2024-07-29T18:00:00', role: 'Waitstaff', location: 'Main', status: 'open' },
  { id: 's4', employeeId: '3', start: '2024-07-30T09:00:00', end: '2024-07-30T17:00:00', role: 'Front of House', location: 'Main', status: 'scheduled' },
  { id: 's5', employeeId: '4', start: '2024-07-30T04:00:00', end: '2024-07-30T12:00:00', role: 'Prep Cook', location: 'Main', status: 'scheduled' },
  { id: 's6', employeeId: undefined, start: '2024-07-30T16:00:00', end: '2024-07-30T23:00:00', role: 'Bartender', location: 'Main', status: 'open' },
];

// Mock week (Monday to Sunday)
const weekDays = [
  '2024-07-29', // Mon
  '2024-07-30', // Tue
  '2024-07-31', // Wed
  '2024-08-01', // Thu
  '2024-08-02', // Fri
  '2024-08-03', // Sat
  '2024-08-04', // Sun
];

const TimesheetTab: React.FC = () => {
  const [shifts, setShifts] = useState<Shift[]>(initialShifts);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [swapDropModal, setSwapDropModal] = useState<{ open: boolean; shift: Shift | null; type: 'swap' | 'drop' }>({ open: false, shift: null, type: 'swap' });

  // Open modal for new shift
  const handleAddShift = () => {
    setEditingShift(null);
    setModalOpen(true);
  };

  // Open modal for editing
  const handleEditShift = (shift: Shift) => {
    setEditingShift(shift);
    setModalOpen(true);
  };

  // Save shift (create or update)
  const handleSaveShift = (data: any) => {
    // Compose start/end ISO strings from date and time
    const start = `${data.date}T${data.startTime}`;
    const end = `${data.date}T${data.endTime}`;
    if (editingShift) {
      setShifts(shifts.map(s => s.id === editingShift.id ? { ...s, ...data, start, end } : s));
    } else {
      setShifts([
        ...shifts,
        {
          id: `s${shifts.length + 1}`,
          employeeId: data.employeeId || undefined,
          start,
          end,
          role: data.role,
          location: 'Main',
          status: data.employeeId ? 'scheduled' : 'open',
        },
      ]);
    }
    setModalOpen(false);
  };

  // Open swap modal
  const handleSwap = (shift: Shift) => {
    setSwapDropModal({ open: true, shift, type: 'swap' });
  };
  // Open drop modal
  const handleDrop = (shift: Shift) => {
    setSwapDropModal({ open: true, shift, type: 'drop' });
  };
  // Confirm swap/drop
  const handleConfirmSwapDrop = () => {
    if (!swapDropModal.shift) return;
    setShifts(shifts.map(s =>
      s.id === swapDropModal.shift!.id
        ? { ...s, status: swapDropModal.type === 'swap' ? 'swap_requested' : 'dropped' }
        : s
    ));
    setSwapDropModal({ open: false, shift: null, type: 'swap' });
  };

  // Time tracking (mock): update status
  const handleClockInOut = (shift: Shift) => {
    setShifts(shifts.map(s => s.id === shift.id ? { ...s, status: s.status === 'scheduled' ? 'clocked_in' : 'scheduled' } : s));
  };
  const handleBreak = (shift: Shift) => {
    setShifts(shifts.map(s => s.id === shift.id ? { ...s, status: s.status === 'clocked_in' ? 'on_break' : 'clocked_in' } : s));
  };
  // Claim open shift
  const handleClaim = (shift: Shift) => {
    // Assign to first employee for demo (in real app, use logged-in user)
    setShifts(shifts.map(s => s.id === shift.id ? { ...s, employeeId: employees[0].id, status: 'scheduled' } : s));
  };

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-8">
      <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
        {/* Sticky header */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-100 px-8 pt-6 pb-2">
          <h2 className="text-2xl font-bold text-slate-800 mb-2">Weekly Schedule</h2>
          <TimesheetHeader weekDays={weekDays} />
        </div>
        <div className="flex flex-col md:flex-row min-h-[600px]">
          {/* Employee List */}
          <div className="bg-gray-50 border-r border-gray-200 w-full md:w-64 p-6 flex-shrink-0">
            <h3 className="font-semibold text-slate-700 mb-6 text-lg">Employees</h3>
            <EmployeeList employees={employees} />
          </div>
          {/* Schedule Grid */}
          <div className="flex-1 bg-white p-6 overflow-x-auto">
            <OpenShiftsRow shifts={shifts.filter(s => !s.employeeId)} weekDays={weekDays} />
            <ScheduleGrid
              employees={employees}
              shifts={shifts}
              weekDays={weekDays}
              onShiftClick={handleEditShift}
              onSwap={handleSwap}
              onDrop={handleDrop}
              onClockInOut={handleClockInOut}
              onBreak={handleBreak}
              onClaim={handleClaim}
            />
          </div>
        </div>
      </div>
      {/* Floating Add Button */}
      <button
        className="fixed bottom-8 right-8 bg-blue-600 text-white rounded-full w-14 h-14 flex items-center justify-center text-3xl shadow-lg hover:bg-blue-700 z-50"
        onClick={handleAddShift}
        title="Add Shift"
      >
        +
      </button>
      <ShiftFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleSaveShift}
        initialValues={editingShift ? editingShift : undefined}
        employees={employees}
        weekDays={weekDays}
      />
      <SwapDropModal
        open={swapDropModal.open}
        onClose={() => setSwapDropModal({ open: false, shift: null, type: 'swap' })}
        onConfirm={handleConfirmSwapDrop}
        shift={swapDropModal.shift}
        type={swapDropModal.type}
      />
    </div>
  );
};

export default TimesheetTab; 