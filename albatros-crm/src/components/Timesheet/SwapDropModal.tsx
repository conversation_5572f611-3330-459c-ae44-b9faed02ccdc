import React from 'react';
import { Shift } from './types';

interface SwapDropModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  shift: Shift | null;
  type: 'swap' | 'drop';
}

const SwapDropModal: React.FC<SwapDropModalProps> = ({ open, onClose, onConfirm, shift, type }) => {
  if (!open || !shift) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm">
        <h2 className="text-lg font-semibold mb-4">{type === 'swap' ? 'Request Shift Swap' : 'Drop Shift'}</h2>
        <div className="mb-4">
          <div className="mb-2">Role: <b>{shift.role}</b></div>
          <div>Date: <b>{new Date(shift.start).toLocaleDateString()}</b></div>
          <div>Time: <b>{new Date(shift.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(shift.end).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</b></div>
        </div>
        <div className="flex justify-end space-x-2 mt-4">
          <button type="button" onClick={onClose} className="px-4 py-2 rounded bg-gray-200">Cancel</button>
          <button type="button" onClick={onConfirm} className="px-4 py-2 rounded bg-blue-600 text-white">{type === 'swap' ? 'Request Swap' : 'Drop Shift'}</button>
        </div>
      </div>
    </div>
  );
};

export default SwapDropModal; 