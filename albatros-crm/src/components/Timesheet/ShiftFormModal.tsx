import React from 'react';
import { useForm } from 'react-hook-form';
import { Employee, Shift } from './types';

export interface ShiftFormValues {
  employeeId?: string;
  role: string;
  date: string;
  startTime: string;
  endTime: string;
}

interface ShiftFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: ShiftFormValues) => void;
  initialValues?: Partial<Shift>;
  employees: Employee[];
  weekDays: string[];
}

const ShiftFormModal: React.FC<ShiftFormModalProps> = ({ open, onClose, onSubmit, initialValues, employees, weekDays }) => {
  // Derive form defaults from initialValues if present
  const derivedDefaults: ShiftFormValues = initialValues && initialValues.start && initialValues.end ? {
    employeeId: initialValues.employeeId ?? '',
    role: initialValues.role ?? '',
    date: initialValues.start.split('T')[0],
    startTime: initialValues.start.split('T')[1]?.slice(0,5) ?? '',
    endTime: initialValues.end.split('T')[1]?.slice(0,5) ?? '',
  } : {
    employeeId: '',
    role: '',
    date: weekDays[0],
    startTime: '',
    endTime: '',
  };

  const { register, handleSubmit, reset } = useForm<ShiftFormValues>({
    defaultValues: derivedDefaults,
  });

  React.useEffect(() => {
    reset(derivedDefaults);
  }, [initialValues, reset]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">{initialValues ? 'Edit Shift' : 'Add Shift'}</h2>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Employee</label>
            <select {...register('employeeId')} className="w-full border rounded p-2" defaultValue={derivedDefaults.employeeId || ''}>
              <option value="">Open Shift</option>
              {employees.map(emp => (
                <option key={emp.id} value={emp.id}>{emp.name}</option>
              ))}
            </select>
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Role</label>
            <input {...register('role', { required: true })} className="w-full border rounded p-2" defaultValue={derivedDefaults.role || ''} />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Date</label>
            <select {...register('date', { required: true })} className="w-full border rounded p-2" defaultValue={derivedDefaults.date || ''}>
              {weekDays.map(date => (
                <option key={date} value={date}>{new Date(date).toLocaleDateString()}</option>
              ))}
            </select>
          </div>
          <div className="mb-3 flex space-x-2">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Start Time</label>
              <input type="time" {...register('startTime', { required: true })} className="w-full border rounded p-2" defaultValue={derivedDefaults.startTime || ''} />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">End Time</label>
              <input type="time" {...register('endTime', { required: true })} className="w-full border rounded p-2" defaultValue={derivedDefaults.endTime || ''} />
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-4">
            <button type="button" onClick={onClose} className="px-4 py-2 rounded bg-gray-200">Cancel</button>
            <button type="submit" className="px-4 py-2 rounded bg-blue-600 text-white">Save</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ShiftFormModal; 