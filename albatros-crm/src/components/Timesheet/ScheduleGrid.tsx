import React from 'react';
import { Employee, Shift } from './types';
import { DndContext, closestCenter, useDraggable, useDroppable, DragEndEvent } from '@dnd-kit/core';

interface ScheduleGridProps {
  employees: Employee[];
  shifts: Shift[];
  weekDays: string[];
  onShiftClick?: (shift: Shift) => void;
  onSwap?: (shift: Shift) => void;
  onDrop?: (shift: Shift) => void;
  onClockInOut?: (shift: Shift) => void;
  onBreak?: (shift: Shift) => void;
  onClaim?: (shift: Shift) => void;
}

// Helper: check eligibility
function isEligible(employee: Employee, shift: Shift) {
  return shift.role && employee.skills.includes(shift.role.toLowerCase());
}

// Draggable shift block
const ShiftBlock: React.FC<{
  shift: Shift;
  onClick?: () => void;
  onSwap?: () => void;
  onDrop?: () => void;
  onClockInOut?: () => void;
  onBreak?: () => void;
  onClaim?: () => void;
  employee?: Employee;
}> = ({ shift, onClick, onSwap, onDrop, onClockInOut, onBreak, onClaim, employee }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: shift.id,
  });
  // Attendance indicator (mock)
  const attendance = shift.status === 'swap_requested' ? '🟡' : shift.status === 'dropped' ? '🔴' : '🟢';
  // Time tracking state (mock)
  const [clockedIn, setClockedIn] = React.useState(false);
  const [onBreakState, setOnBreakState] = React.useState(false);
  // Open shift claim
  if (!shift.employeeId && onClaim) {
    const eligible = employee && isEligible(employee, shift);
    return (
      <div className="bg-yellow-300 text-yellow-900 rounded px-2 py-1 mb-1 text-xs font-medium flex flex-col items-center">
        {shift.role} ({new Date(shift.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(shift.end).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})
        <button
          className={`mt-1 px-2 py-0.5 rounded text-xs font-bold ${eligible ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
          disabled={!eligible}
          onClick={eligible ? onClaim : undefined}
        >Claim</button>
        {!eligible && <div className="text-[10px] text-red-600 mt-1">Not eligible</div>}
      </div>
    );
  }
  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={{
        transform: transform ? `translate(${transform.x}px, ${transform.y}px)` : undefined,
        opacity: isDragging ? 0.5 : 1,
      }}
      className="bg-blue-500 text-white rounded px-2 py-1 mb-1 text-xs font-medium cursor-move relative group flex flex-col sm:flex-row sm:items-center"
      onClick={onClick}
    >
      <span className="mr-1">{attendance}</span>
      <span className="flex-1">{shift.role} ({new Date(shift.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(shift.end).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})</span>
      <div className="flex space-x-1 mt-1 sm:mt-0">
        <button
          className={`bg-green-400 text-white rounded px-1 text-xs font-bold hover:bg-green-500 ${clockedIn ? 'bg-green-700' : ''}`}
          title={clockedIn ? 'Clock Out' : 'Clock In'}
          onClick={e => { e.stopPropagation(); setClockedIn(v => !v); onClockInOut?.(); }}
        >{clockedIn ? 'Out' : 'In'}</button>
        <button
          className={`bg-yellow-400 text-yellow-900 rounded px-1 text-xs font-bold hover:bg-yellow-500 ${onBreakState ? 'bg-yellow-700 text-white' : ''}`}
          title={onBreakState ? 'End Break' : 'Start Break'}
          onClick={e => { e.stopPropagation(); setOnBreakState(v => !v); onBreak?.(); }}
        >{onBreakState ? 'End' : 'Break'}</button>
      </div>
      <div className="absolute top-1 right-1 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          className="bg-yellow-400 text-yellow-900 rounded px-1 text-xs font-bold hover:bg-yellow-500"
          title="Request Swap"
          onClick={e => { e.stopPropagation(); onSwap?.(); }}
        >⇄</button>
        <button
          className="bg-red-400 text-white rounded px-1 text-xs font-bold hover:bg-red-500"
          title="Drop Shift"
          onClick={e => { e.stopPropagation(); onDrop?.(); }}
        >✕</button>
      </div>
    </div>
  );
};

const DroppableCell = ({ id, children }: { id: string; children: React.ReactNode }) => {
  const { setNodeRef } = useDroppable({ id });
  return <div ref={setNodeRef}>{children}</div>;
};

const ScheduleGrid: React.FC<ScheduleGridProps> = ({ employees, shifts, weekDays, onShiftClick, onSwap, onDrop, onClockInOut, onBreak, onClaim }) => {
  // Handler for when a drag ends
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      // TODO: Update shift assignment in state/API
      alert(`Move shift ${active.id} to cell ${over.id}`);
    }
  };

  return (
    <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      <div className="bg-gray-100 p-4 min-h-[400px] overflow-x-auto">
        <div className="grid" style={{ gridTemplateColumns: `repeat(${weekDays.length + 1}, minmax(120px, 1fr))` }}>
          <div className="font-semibold text-gray-700">&nbsp;</div>
          {weekDays.map(date => (
            <div key={date} className="font-semibold text-center text-gray-700">
              {new Date(date).toLocaleDateString(undefined, { weekday: 'short' })}
            </div>
          ))}
          {employees.map(emp => (
            <React.Fragment key={emp.id}>
              <div className="font-medium text-gray-600 flex items-center min-h-[60px]">{emp.name}</div>
              {weekDays.map(date => {
                // Each cell is a droppable area
                const cellId = `${emp.id}-${date}`;
                return (
                  <DroppableCell id={cellId}>
                    {shifts.filter(s => s.employeeId === emp.id && s.start.startsWith(date)).map(s => (
                      <ShiftBlock
                        key={s.id}
                        shift={s}
                        onClick={() => onShiftClick?.(s)}
                        onSwap={() => onSwap?.(s)}
                        onDrop={() => onDrop?.(s)}
                        onClockInOut={() => onClockInOut?.(s)}
                        onBreak={() => onBreak?.(s)}
                        employee={emp}
                      />
                    ))}
                    {/* Render open shifts for this day as claimable */}
                    {shifts.filter(s => !s.employeeId && s.start.startsWith(date)).map(s => (
                      <ShiftBlock
                        key={s.id}
                        shift={s}
                        onClaim={() => onClaim?.(s)}
                        employee={emp}
                      />
                    ))}
                  </DroppableCell>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    </DndContext>
  );
};

export default ScheduleGrid; 