export interface Employee {
  id: string;
  name: string;
  role: string;
  skills: string[];
  locations: string[];
  avatarUrl?: string;
}

export type ShiftStatus = 'scheduled' | 'open' | 'swap_requested' | 'dropped' | 'claimed' | 'clocked_in' | 'on_break';

export interface Shift {
  id: string;
  employeeId?: string; // undefined for open shift
  start: string; // ISO date
  end: string;   // ISO date
  role: string;
  location: string;
  status: ShiftStatus;
}

export interface TimesheetView {
  type: 'daily' | 'weekly' | 'monthly';
  startDate: Date;
} 