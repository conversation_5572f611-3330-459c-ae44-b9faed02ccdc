import React from 'react';
import { Employee } from './types';

interface EmployeeListProps {
  employees: Employee[];
}

const EmployeeList: React.FC<EmployeeListProps> = ({ employees }) => (
  <div className="w-56 bg-white border-r border-gray-200 p-2 overflow-y-auto">
    <div className="font-medium mb-2">Employees</div>
    {employees.map(emp => (
      <div key={emp.id} className="flex items-center mb-3">
        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2 text-gray-600 font-bold">
          {emp.name[0]}
        </div>
        <div>
          <div className="font-medium text-sm">{emp.name}</div>
          <div className="text-xs text-gray-500">{emp.role}</div>
        </div>
      </div>
    ))}
  </div>
);

export default EmployeeList; 