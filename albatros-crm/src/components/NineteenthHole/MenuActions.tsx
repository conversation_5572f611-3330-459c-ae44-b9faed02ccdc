import {
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  InputAdornment,
  TextField,
  Tooltip,
  styled
} from '@mui/material';
import React from 'react';

interface MenuActionsProps {
  onAdd: () => void;
  onEdit: () => void;
  onSearch: (term: string) => void;
  searchTerm: string;
  isEditMode: boolean;
}

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius * 2,
    backgroundColor: theme.palette.background.paper,
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    '&.Mui-focused': {
      backgroundColor: theme.palette.background.paper,
      boxShadow: theme.shadows[2],
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1, 2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[2],
  },
}));

export const MenuActions: React.FC<MenuActionsProps> = ({
  onAdd,
  onEdit,
  onSearch,
  searchTerm,
  isEditMode,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        mb: 3,
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'stretch', sm: 'center' },
      }}
    >
      <StyledTextField
        fullWidth
        placeholder="Search menu items..."
        value={searchTerm}
        onChange={(e) => onSearch(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon color="action" />
            </InputAdornment>
          ),
        }}
      />
      <Box
        sx={{
          display: 'flex',
          gap: 1,
          flexShrink: 0,
        }}
      >
        <Tooltip title={isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}>
          <StyledButton
            variant={isEditMode ? 'contained' : 'outlined'}
            color={isEditMode ? 'primary' : 'inherit'}
            onClick={onEdit}
            startIcon={isEditMode ? <SaveIcon /> : <EditIcon />}
          >
            {isEditMode ? 'Save Changes' : 'Edit Menu'}
          </StyledButton>
        </Tooltip>
        <Tooltip title="Add New Item">
          <StyledButton
            variant="contained"
            color="primary"
            onClick={onAdd}
            startIcon={<AddIcon />}
          >
            Add Item
          </StyledButton>
        </Tooltip>
      </Box>
    </Box>
  );
}; 