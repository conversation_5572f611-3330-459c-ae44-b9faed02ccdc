import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  AccessTime as AccessTimeIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Info as InfoIcon,
  LocalOffer as LocalOfferIcon,
  Smartphone as SmartphoneIcon,
  Star as StarIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  styled,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import React, { useState } from 'react';
import { MenuItem } from './types';

interface MenuListProps {
  items: MenuItem[];
  onMobileAppToggle: (itemId: string, currentValue: boolean) => Promise<void>;
  onEditItem: (item: MenuItem) => void;
  onDeleteItem?: (item: MenuItem) => void;
  onReorderItems?: (items: MenuItem[]) => void;
  isEditMode: boolean;
}

const StyledCard = styled(Card)<{ isEditMode?: boolean }>(({ theme, isEditMode }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[2],
  },
  cursor: 'pointer',
  position: 'relative',
  ...(isEditMode && {
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      border: '2px solid transparent',
      transition: 'all 0.3s ease',
    },
    '&:hover::after': {
      borderColor: theme.palette.primary.main,
    },
  }),
}));

const StyledCardMedia = styled(CardMedia)(({ theme }) => ({
  height: 100,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.7) 100%)',
  },
}));

const PriceChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  fontWeight: 'bold',
  '& .MuiChip-label': {
    padding: theme.spacing(0.5, 1),
  },
}));

const DiscountChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  left: theme.spacing(1),
  backgroundColor: theme.palette.secondary.main,
  color: theme.palette.secondary.contrastText,
  fontWeight: 'bold',
  '& .MuiChip-label': {
    padding: theme.spacing(0.5, 1),
  },
}));

const TopItemBadge = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor: theme.palette.warning.main,
  color: theme.palette.warning.contrastText,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

const DetailDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    maxWidth: '600px',
    width: '100%',
    borderRadius: theme.shape.borderRadius,
    overflow: 'hidden',
  },
}));

const DetailImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: '250px',
  objectFit: 'contain',
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
}));

const DetailSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '&:last-child': {
    marginBottom: 0,
  },
}));

const DetailTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.main,
  fontWeight: 'bold',
}));

interface SortableItemProps {
  item: MenuItem;
  isEditMode: boolean;
  onItemClick: (item: MenuItem) => void;
  onDelete?: (e: React.MouseEvent, item: MenuItem) => void;
}

const SortableItem = ({ item, isEditMode, onItemClick, onDelete }: SortableItemProps) => {
  const theme = useTheme();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <Grid item xs={12}>
      <Box ref={setNodeRef} style={style}>
        <StyledCard 
          onClick={() => onItemClick(item)}
          isEditMode={isEditMode}
          sx={{ 
            ...(isDragging && {
              background: theme.palette.background.paper,
              boxShadow: theme.shadows[8],
            }),
          }}
        >
          {isEditMode && (
            <Box sx={{ 
              position: 'absolute',
              left: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
            }}
            {...attributes}
            {...listeners}
            >
              <DragIndicatorIcon color="action" />
            </Box>
          )}
          {isEditMode && onDelete && (
            <IconButton
              size="small"
              color="error"
              onClick={(e) => onDelete(e, item)}
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                zIndex: 2,
                bgcolor: 'background.paper',
                '&:hover': {
                  bgcolor: 'error.light',
                  color: 'error.contrastText',
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          )}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            height: '100%',
            pl: isEditMode ? 4 : 0, // Add padding when in edit mode to make room for drag handle
          }}>
            <Box sx={{ 
              position: 'relative',
              width: { xs: '100%', sm: '160px' },
              flexShrink: 0,
            }}>
              <StyledCardMedia
                image={item.image || '/images/menu-items/default.jpg'}
                title={item.name}
                sx={{ height: { xs: 100, sm: '100%' } }}
              />
              <PriceChip label={`$${item.price.toFixed(2)}`} />
              {item.membershipDiscount !== undefined && item.membershipDiscount > 0 && (
                <DiscountChip
                  label={`Save $${item.membershipDiscount.toFixed(2)}`}
                />
              )}
              {item.isTopItem && (
                <TopItemBadge>
                  <StarIcon fontSize="small" />
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                    Top Item
                  </Typography>
                </TopItemBadge>
              )}
            </Box>
            <CardContent sx={{ 
              flexGrow: 1,
              display: 'flex',
              flexDirection: 'column',
              p: 1.5,
            }}>
              <Box
                sx={{
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'flex-start',
                  mb: 0.5,
                }}
              >
                <Typography variant="h6" component="h2" gutterBottom>
                  {item.name}
                </Typography>
              </Box>
                <Typography 
                  variant="body2" 
                  color="text.secondary"
                sx={{ mb: 1 }}
                paragraph
                >
                  {item.description}
                </Typography>
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap',
                gap: 0.5,
                mb: 1,
              }}>
                {item.ingredients && item.ingredients.length > 0 && (
                  <Box>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Ingredients:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {item.ingredients.map((ingredient) => (
                        <Chip
                          key={ingredient}
                          label={ingredient}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Box>
                )}
                {item.allergens && item.allergens.length > 0 && (
                  <Box>
                    <Typography variant="caption" color="error" gutterBottom>
                      Allergens:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {item.allergens.map((allergen) => (
                        <Chip
                          key={allergen}
                          label={allergen}
                      size="small"
                          color="error"
                          variant="outlined"
                    />
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mt: 'auto',
                }}
              >
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {item.preparationTime && (
                    <Typography variant="caption" color="text.secondary">
                      Prep: {item.preparationTime} min
                    </Typography>
                  )}
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Tooltip title={item.mobileApp ? "Available in Mobile App" : "Not Available in Mobile App"}>
                    <SmartphoneIcon
                      fontSize="small"
                      color={item.mobileApp ? 'primary' : 'disabled'}
                    />
                  </Tooltip>
                </Box>
              </Box>
            </CardContent>
          </Box>
        </StyledCard>
      </Box>
    </Grid>
  );
};

export const MenuList: React.FC<MenuListProps> = ({
  items,
  onMobileAppToggle,
  onEditItem,
  onDeleteItem,
  onReorderItems,
  isEditMode,
}) => {
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [deleteConfirmItem, setDeleteConfirmItem] = useState<MenuItem | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);

      if (onReorderItems) {
        onReorderItems(arrayMove(items, oldIndex, newIndex));
      }
    }
  };

  const handleItemClick = (item: MenuItem) => {
    if (isEditMode) {
      onEditItem(item);
    } else {
      setSelectedItem(item);
    }
  };

  const handleDelete = (e: React.MouseEvent, item: MenuItem) => {
    e.stopPropagation();
    setDeleteConfirmItem(item);
  };

  const handleConfirmDelete = () => {
    if (deleteConfirmItem && onDeleteItem) {
      onDeleteItem(deleteConfirmItem);
    }
    setDeleteConfirmItem(null);
  };

  const handleCancelDelete = () => {
    setDeleteConfirmItem(null);
  };

  const handleCloseDetail = () => {
    setSelectedItem(null);
  };

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={items.map(item => item.id)}
          strategy={verticalListSortingStrategy}
        >
          <Grid container spacing={1.5}>
            {items.map((item) => (
              <SortableItem
                key={item.id}
                item={item}
                isEditMode={isEditMode}
                onItemClick={handleItemClick}
                onDelete={onDeleteItem ? handleDelete : undefined}
              />
            ))}
          </Grid>
        </SortableContext>
      </DndContext>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirmItem}
        onClose={handleCancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography id="delete-dialog-description">
            Are you sure you want to delete "{deleteConfirmItem?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <DetailDialog
        open={!!selectedItem}
        onClose={handleCloseDetail}
        maxWidth="md"
        fullWidth
      >
        {selectedItem && (
          <>
            <DialogTitle sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              pb: 1,
            }}>
              <Typography variant="h5" component="div">
                {selectedItem.name}
              </Typography>
              <IconButton onClick={handleCloseDetail} size="small">
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent dividers>
              <DetailImage
                src={selectedItem.image || '/images/menu-items/default.jpg'}
                alt={selectedItem.name}
              />
              
              <DetailSection>
                <DetailTitle variant="h6">Description</DetailTitle>
                <Typography variant="body1" paragraph>
                  {selectedItem.description}
                </Typography>
              </DetailSection>

              <DetailSection>
                <DetailTitle variant="h6">Details</DetailTitle>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <LocalOfferIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Price" 
                      secondary={`$${selectedItem.price.toFixed(2)}`}
                    />
                  </ListItem>
                  {selectedItem.membershipDiscount !== undefined && selectedItem.membershipDiscount > 0 && (
                    <ListItem>
                      <ListItemIcon>
                        <LocalOfferIcon color="secondary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Membership Discount" 
                        secondary={`Save $${selectedItem.membershipDiscount.toFixed(2)}`}
                      />
                    </ListItem>
                  )}
                  {selectedItem.preparationTime && (
                    <ListItem>
                      <ListItemIcon>
                        <AccessTimeIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Preparation Time" 
                        secondary={`${selectedItem.preparationTime} minutes`}
                      />
                    </ListItem>
                  )}
                  <ListItem>
                    <ListItemIcon>
                      <SmartphoneIcon color={selectedItem.mobileApp ? "primary" : "disabled"} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Mobile App Availability" 
                      secondary={selectedItem.mobileApp ? "Available" : "Not Available"}
                    />
                  </ListItem>
                </List>
              </DetailSection>

              {selectedItem.ingredients && selectedItem.ingredients.length > 0 && (
                <DetailSection>
                  <DetailTitle variant="h6">Ingredients</DetailTitle>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedItem.ingredients.map((ingredient) => (
                      <Chip
                        key={ingredient}
                        label={ingredient}
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </DetailSection>
              )}

              {selectedItem.allergens && selectedItem.allergens.length > 0 && (
                <DetailSection>
                  <DetailTitle variant="h6">Allergens</DetailTitle>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedItem.allergens.map((allergen) => (
                      <Chip
                        key={allergen}
                        label={allergen}
                        icon={<WarningIcon />}
                        color="error"
                        variant="outlined"
                      />
      ))}
    </Box>
                </DetailSection>
              )}

              {selectedItem.nutritionalInfo && (
                <DetailSection>
                  <DetailTitle variant="h6">Nutritional Information</DetailTitle>
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <InfoIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Calories" 
                        secondary={`${selectedItem.nutritionalInfo.calories} kcal`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <InfoIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Protein" 
                        secondary={`${selectedItem.nutritionalInfo.protein}g`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <InfoIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Carbohydrates" 
                        secondary={`${selectedItem.nutritionalInfo.carbs}g`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <InfoIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Fat" 
                        secondary={`${selectedItem.nutritionalInfo.fat}g`}
                      />
                    </ListItem>
                  </List>
                </DetailSection>
              )}

              {selectedItem.customizations && selectedItem.customizations.length > 0 && (
                <DetailSection>
                  <DetailTitle variant="h6">Available Customizations</DetailTitle>
                  <List dense>
                    {selectedItem.customizations.map((customization) => (
                      <ListItem key={customization.name}>
                        <ListItemIcon>
                          <InfoIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={customization.name}
                          secondary={`${customization.price > 0 ? `+$${customization.price.toFixed(2)}` : 'No additional charge'}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </DetailSection>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDetail} color="primary">
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </DetailDialog>
    </>
  );
}; 