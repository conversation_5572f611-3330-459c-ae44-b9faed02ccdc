import { MenuItem, MenuCategory } from './types';

export const mockCategories: MenuCategory[] = [
  { id: 'sandwiches', name: 'Sandwiches', hasTopItems: true },
  { id: 'burgers', name: 'Burgers', hasTopItems: true },
  { id: 'appetizers', name: 'Appetizers', hasTopItems: false },
  { id: 'salads', name: 'Salads', hasTopItems: false },
  { id: 'drinks', name: 'Drinks', hasTopItems: true },
  { id: 'desserts', name: 'Desserts', hasTopItems: false },
];

export const mockMenuItems: MenuItem[] = [
  // Golf-Themed Sandwiches
  {
    id: '1',
    name: 'Eagle Club Sandwich',
    description: 'Triple-decker sandwich with turkey, bacon, lettuce, and tomato - perfect for celebrating your eagle',
    price: 12.99,
    category: 'sandwiches',
    ingredients: ['Turkey', 'Bacon', 'Lettuce', 'Tomato', 'Mayonnaise', 'Bread'],
    mobileApp: true,
    image: '/images/menu-items/club-sandwich.jpg',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>LT',
    description: 'Crispy bacon, fresh lettuce, and ripe tomatoes on toasted bread - for when you score under par',
    price: 10.99,
    category: 'sandwiches',
    ingredients: ['Bacon', 'Lettuce', 'Tomato', 'Mayonnaise', 'Bread'],
    mobileApp: true,
    image: '/images/menu-items/blt-sandwich.jpg',
  },
  {
    id: '3',
    name: 'Par 3 Turkey & Avocado',
    description: 'Sliced turkey with fresh avocado and sprouts - quick and satisfying like a par 3',
    price: 11.99,
    category: 'sandwiches',
    ingredients: ['Turkey', 'Avocado', 'Sprouts', 'Bread'],
    mobileApp: true,
    image: '/images/menu-items/turkey-avocado.jpg',
  },

  // Golf-Themed Burgers
  {
    id: '4',
    name: 'Hole-in-One Burger',
    description: 'Juicy beef patty with American cheese and all the fixings - as rare as a hole-in-one',
    price: 13.99,
    category: 'burgers',
    ingredients: ['Beef Patty', 'American Cheese', 'Lettuce', 'Tomato', 'Onion', 'Pickles'],
    mobileApp: true,
    image: '/images/menu-items/cheeseburger.jpg',
  },
  {
    id: '5',
    name: 'Fairway Bacon BBQ Burger',
    description: 'Beef patty with crispy bacon and BBQ sauce - straight down the fairway',
    price: 14.99,
    category: 'burgers',
    ingredients: ['Beef Patty', 'Bacon', 'BBQ Sauce', 'Cheddar Cheese', 'Onion Rings'],
    mobileApp: true,
    image: '/images/menu-items/bbq-burger.jpg',
  },
  {
    id: '6',
    name: 'Rough Mushroom Swiss Burger',
    description: 'Beef patty topped with sautéed mushrooms and Swiss cheese - for when you find yourself in the rough',
    price: 14.99,
    category: 'burgers',
    ingredients: ['Beef Patty', 'Mushrooms', 'Swiss Cheese', 'Caramelized Onions'],
    mobileApp: true,
    image: '/images/menu-items/mushroom-burger.jpg',
  },

  // Golf-Themed Appetizers
  {
    id: '7',
    name: 'Chip Shot Nachos',
    description: 'Crispy tortilla chips with cheese, jalapeños, and all the fixings - perfect for practicing your chip shots',
    price: 10.99,
    category: 'appetizers',
    ingredients: ['Tortilla Chips', 'Cheese', 'Jalapeños', 'Sour Cream', 'Guacamole', 'Salsa'],
    mobileApp: true,
    image: '/images/menu-items/loaded-nachos.jpg',
  },
  {
    id: '8',
    name: 'Tee Time Wings',
    description: 'Crispy chicken wings tossed in spicy buffalo sauce - ready for your tee time',
    price: 12.99,
    category: 'appetizers',
    ingredients: ['Chicken Wings', 'Buffalo Sauce', 'Celery', 'Blue Cheese Dressing'],
    mobileApp: true,
    image: '/images/menu-items/wings.jpg',
  },
  {
    id: '9',
    name: 'Chips and Queso',
    description: 'Crispy tortilla chips served with warm, creamy queso dip - perfect for the 19th hole',
    price: 8.99,
    category: 'appetizers',
    ingredients: ['Tortilla Chips', 'Queso Cheese', 'Jalapeños', 'Pico de Gallo'],
    mobileApp: true,
    image: '/images/menu-items/chips-queso.jpg',
  },

  // Golf-Themed Drinks
  {
    id: '13',
    name: 'Arnold Palmer',
    description: 'Classic mix of iced tea and lemonade - named after the golf legend',
    price: 4.99,
    category: 'drinks',
    ingredients: ['Iced Tea', 'Lemonade'],
    mobileApp: true,
    image: '/images/menu-items/iced-tea.jpg',
  },
  {
    id: '14',
    name: 'Golfer\'s Beer',
    description: 'Local craft beer selection - perfect for post-round celebration',
    price: 6.99,
    category: 'drinks',
    ingredients: ['Beer'],
    mobileApp: true,
    image: '/images/menu-items/craft-beer.jpg',
  },
  {
    id: '15',
    name: 'Clubhouse Lemonade',
    description: 'Freshly squeezed lemonade - refreshing after 18 holes',
    price: 4.99,
    category: 'drinks',
    ingredients: ['Lemons', 'Sugar', 'Water'],
    mobileApp: true,
    image: '/images/menu-items/lemonade.jpg',
  },

  // Golf-Themed Salads
  {
    id: '16',
    name: 'Greenskeeper Salad',
    description: 'Crisp romaine lettuce with Caesar dressing and parmesan - as fresh as our greens',
    price: 10.99,
    category: 'salads',
    ingredients: ['Romaine Lettuce', 'Caesar Dressing', 'Parmesan', 'Croutons'],
    mobileApp: true,
    image: '/images/menu-items/caesar-salad.jpg',
  },
  {
    id: '17',
    name: 'Fairway Cobb Salad',
    description: 'Mixed greens with chicken, bacon, egg, avocado, and blue cheese - straight down the fairway',
    price: 12.99,
    category: 'salads',
    ingredients: ['Mixed Greens', 'Chicken', 'Bacon', 'Egg', 'Avocado', 'Blue Cheese'],
    mobileApp: true,
    image: '/images/menu-items/cobb-salad.jpg',
  },
  {
    id: '18',
    name: 'Rough Chicken Salad',
    description: 'Fresh greens with grilled chicken and balsamic vinaigrette - perfect for when you find yourself in the rough',
    price: 11.99,
    category: 'salads',
    ingredients: ['Mixed Greens', 'Grilled Chicken', 'Cherry Tomatoes', 'Cucumber', 'Balsamic Vinaigrette'],
    mobileApp: true,
    image: '/images/menu-items/chicken-salad.jpg',
  },

  // Golf-Themed Desserts
  {
    id: '39',
    name: 'Hole-in-One Chocolate Cake',
    description: 'Rich chocolate cake with chocolate ganache - as sweet as a hole-in-one',
    price: 7.99,
    category: 'desserts',
    ingredients: ['Chocolate', 'Flour', 'Eggs', 'Sugar', 'Butter'],
    mobileApp: true,
    image: '/images/menu-items/chocolate-cake.jpg',
  },
  {
    id: '40',
    name: 'Eagle Cheesecake',
    description: 'Classic New York style cheesecake with berry compote - perfect for celebrating your eagle',
    price: 8.99,
    category: 'desserts',
    ingredients: ['Cream Cheese', 'Graham Cracker Crust', 'Berries', 'Sugar'],
    mobileApp: true,
    image: '/images/menu-items/cheesecake.jpg',
  },
  {
    id: '41',
    name: 'Birdie Apple Pie',
    description: 'Warm apple pie with vanilla ice cream - as satisfying as a birdie',
    price: 7.99,
    category: 'desserts',
    ingredients: ['Apples', 'Cinnamon', 'Sugar', 'Pie Crust', 'Vanilla Ice Cream'],
    mobileApp: true,
    image: '/images/menu-items/apple-pie.jpg',
  },
];

export const mockOrders = [
  // Add mock orders here when implementing the Orders tab
];

export const mockInventory = [
  // Add mock inventory here when implementing the Inventory tab
];

export const mockReports = [
  // Add mock reports here when implementing the Reports tab
]; 