import React from 'react';
import { Card, CardContent, Skeleton, Box } from '@mui/material';

export const MenuItemSkeleton: React.FC = () => {
  return (
    <Card sx={{ display: 'flex', mb: 2 }}>
      <Skeleton
        variant="rectangular"
        width={160}
        height={160}
        animation="wave"
      />
      <CardContent sx={{ flex: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Skeleton variant="text" width={150} height={32} animation="wave" />
            <Skeleton variant="text" width={200} height={24} animation="wave" />
            <Skeleton variant="text" width={80} height={32} animation="wave" />
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Skeleton variant="circular" width={40} height={40} animation="wave" />
            <Skeleton variant="circular" width={40} height={40} animation="wave" />
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {[...Array(3)].map((_, index) => (
            <Skeleton
              key={index}
              variant="rectangular"
              width={80}
              height={24}
              animation="wave"
            />
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}; 