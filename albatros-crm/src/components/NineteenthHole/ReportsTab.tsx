import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Avatar,
  styled,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Restaurant as RestaurantIcon,
  LocalDining as DiningIcon,
  Group as GroupIcon,
} from '@mui/icons-material';

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

export const ReportsTab: React.FC = () => {
  return (
    <Box>
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.light', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6">Total Revenue</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$45,670</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +22.3% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.light', mr: 2 }}>
                  <RestaurantIcon />
                </Avatar>
                <Typography variant="h6">Total Orders</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>2,345</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +15.8% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.light', mr: 2 }}>
                  <GroupIcon />
                </Avatar>
                <Typography variant="h6">Customers</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>1,890</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +10.5% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.light', mr: 2 }}>
                  <DiningIcon />
                </Avatar>
                <Typography variant="h6">Avg. Order Value</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$19.50</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5 }} />
                +5.2% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Top Menu Items */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Top Selling Menu Items</Typography>
          {[
            { name: 'Classic Burger', sales: 456, progress: 90 },
            { name: 'Caesar Salad', sales: 342, progress: 75 },
            { name: 'Club Sandwich', sales: 289, progress: 65 },
            { name: 'Fish & Chips', sales: 234, progress: 55 },
          ].map((item, index) => (
            <Box key={index} sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">{item.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {item.sales} orders
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={item.progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'primary.light',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: 'primary.main',
                  },
                }}
              />
            </Box>
          ))}
        </CardContent>
      </Card>

      {/* Daily Revenue */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Daily Revenue (Last 7 Days)</Typography>
          <Box sx={{ height: 300, display: 'flex', alignItems: 'flex-end', gap: 2 }}>
            {[75, 85, 92, 88, 95, 82, 89].map((value, index) => (
              <Box
                key={index}
                sx={{
                  height: `${value}%`,
                  flex: 1,
                  backgroundColor: 'primary.main',
                  borderRadius: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                    transform: 'scaleY(1.02)',
                  },
                }}
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
              <Typography key={day} variant="body2" color="text.secondary">
                {day}
              </Typography>
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Peak Hours */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Peak Hours</Typography>
          <Box sx={{ height: 200, display: 'flex', alignItems: 'flex-end', gap: 1 }}>
            {[30, 25, 20, 35, 55, 80, 90, 85, 70, 60, 45, 40].map((value, index) => (
              <Box
                key={index}
                sx={{
                  height: `${value}%`,
                  flex: 1,
                  backgroundColor: 'secondary.main',
                  borderRadius: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'secondary.dark',
                    transform: 'scaleY(1.02)',
                  },
                }}
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            {['8AM', '9AM', '10AM', '11AM', '12PM', '1PM', '2PM', '3PM', '4PM', '5PM', '6PM', '7PM'].map((hour) => (
              <Typography key={hour} variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                {hour}
              </Typography>
            ))}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}; 