import {
  AddCircle as AddCircleIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { CategoryManager } from './CategoryManager';
import { MenuCategory, MenuItemCustomization, MenuItem as MenuItemType, NutritionalInfo } from './types';

interface MenuItemEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (item: MenuItemType) => void;
  categories: MenuCategory[];
  item?: MenuItemType;
  onCategoriesChange?: (categories: MenuCategory[]) => void;
}

const defaultNutritionalInfo: NutritionalInfo = {
  calories: 0,
  protein: 0,
  carbs: 0,
  fat: 0,
};

const defaultCustomization: MenuItemCustomization = {
  name: '',
  price: 0,
  options: [{ label: '', price: 0 }],
};

export const MenuItemEditor: React.FC<MenuItemEditorProps> = ({
  open,
  onClose,
  onSave,
  categories,
  item,
  onCategoriesChange,
}) => {
  const [formData, setFormData] = useState<MenuItemType>(() => ({
    id: '',
    name: '',
    description: '',
    category: categories[0]?.id || '',
    price: 0,
    image: '',
    membershipDiscount: 0,
    preparationTime: 0,
    customizations: [],
    ingredients: [],
    allergens: [],
    nutritionalInfo: { ...defaultNutritionalInfo },
    mobileApp: true,
    isTopItem: false,
  }));

  const [newIngredient, setNewIngredient] = useState('');
  const [newAllergen, setNewAllergen] = useState('');
  const [newCustomization, setNewCustomization] = useState<MenuItemCustomization>(defaultCustomization);

  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [newCategories, setNewCategories] = useState<MenuCategory[]>(categories);

  // Reset form data when dialog opens/closes or item changes
  useEffect(() => {
    if (open) {
      if (item) {
        // Editing existing item
        setFormData({
          id: item.id,
          name: item.name,
          description: item.description,
          category: item.category,
          price: item.price,
          image: item.image || '',
          membershipDiscount: item.membershipDiscount || 0,
          preparationTime: item.preparationTime || 0,
          customizations: item.customizations || [],
          ingredients: item.ingredients || [],
          allergens: item.allergens || [],
          nutritionalInfo: item.nutritionalInfo || { ...defaultNutritionalInfo },
          mobileApp: item.mobileApp ?? true,
          isTopItem: item.isTopItem ?? false,
        });
      } else {
        // Creating new item
        setFormData({
          id: '',
          name: '',
          description: '',
          category: categories[0]?.id || '',
          price: 0,
          image: '',
          membershipDiscount: 0,
          preparationTime: 0,
          customizations: [],
          ingredients: [],
          allergens: [],
          nutritionalInfo: { ...defaultNutritionalInfo },
          mobileApp: true,
          isTopItem: false,
        });
      }
    }
  }, [open, item, categories]);

  const handleChange = (field: keyof MenuItemType, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNutritionalInfoChange = (field: keyof NutritionalInfo, value: number) => {
    setFormData(prev => ({
      ...prev,
      nutritionalInfo: {
        calories: prev.nutritionalInfo?.calories || 0,
        protein: prev.nutritionalInfo?.protein || 0,
        carbs: prev.nutritionalInfo?.carbs || 0,
        fat: prev.nutritionalInfo?.fat || 0,
        [field]: value,
      },
    }));
  };

  const handleAddIngredient = () => {
    if (newIngredient.trim()) {
      setFormData(prev => ({
        ...prev,
        ingredients: [...(prev.ingredients || []), newIngredient.trim()],
      }));
      setNewIngredient('');
    }
  };

  const handleRemoveIngredient = (ingredient: string) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients?.filter(i => i !== ingredient),
    }));
  };

  const handleAddAllergen = () => {
    if (newAllergen.trim()) {
      setFormData(prev => ({
        ...prev,
        allergens: [...(prev.allergens || []), newAllergen.trim()],
      }));
      setNewAllergen('');
    }
  };

  const handleRemoveAllergen = (allergen: string) => {
    setFormData(prev => ({
      ...prev,
      allergens: prev.allergens?.filter(a => a !== allergen),
    }));
  };

  const handleAddCustomization = () => {
    if (newCustomization.name.trim() && newCustomization.options[0].label.trim()) {
      setFormData(prev => ({
        ...prev,
        customizations: [...(prev.customizations || []), {
          name: newCustomization.name.trim(),
          price: newCustomization.price,
          options: newCustomization.options.map(opt => ({
            label: opt.label.trim(),
            price: opt.price
          }))
        }],
      }));
      setNewCustomization(defaultCustomization);
    }
  };

  const handleRemoveCustomization = (index: number) => {
    setFormData(prev => ({
      ...prev,
      customizations: prev.customizations?.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async () => {
    try {
      // If we have new categories that aren't in the original categories list
      const newCategoriesToSave = newCategories.filter(
        newCat => !categories.find(cat => cat.id === newCat.id)
      );

      // Save any new categories first
      if (newCategoriesToSave.length > 0) {
        for (const category of newCategoriesToSave) {
          await fetch('/api/menu/categories', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(category),
          });
        }
        
        // Notify parent component of the updated categories
        onCategoriesChange?.(newCategories);
      }

      // Then save the menu item
      onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving menu item:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleCategoryManagerClose = (updatedCategories?: MenuCategory[]) => {
    if (updatedCategories) {
      setNewCategories(updatedCategories);
      
      // Find the newly added category (it will be the last one in the array)
      const newCategory = updatedCategories[updatedCategories.length - 1];
      
      // If this is a new category (not in the original categories list)
      if (!categories.find(cat => cat.id === newCategory.id)) {
        // Update the form data to use the new category
        setFormData(prev => ({
          ...prev,
          category: newCategory.id
        }));
      }
    }
    setShowCategoryManager(false);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>{item ? 'Edit Menu Item' : 'Add New Menu Item'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  onChange={(e) => handleChange('category', e.target.value)}
                  label="Category"
                >
                  {newCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                  <Divider />
                  <MenuItem 
                    onClick={() => setShowCategoryManager(true)}
                    sx={{ 
                      color: 'primary.main',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                        color: 'primary.contrastText',
                      },
                    }}
                  >
                    <ListItemIcon>
                      <AddCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary="Add New Category" />
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                multiline
                rows={3}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Price"
                type="number"
                value={formData.price}
                onChange={(e) => handleChange('price', parseFloat(e.target.value))}
                required
                InputProps={{
                  startAdornment: '$',
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Membership Discount"
                type="number"
                value={formData.membershipDiscount}
                onChange={(e) => handleChange('membershipDiscount', parseFloat(e.target.value))}
                InputProps={{
                  startAdornment: '$',
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Preparation Time (minutes)"
                type="number"
                value={formData.preparationTime}
                onChange={(e) => handleChange('preparationTime', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Image URL"
                value={formData.image}
                onChange={(e) => handleChange('image', e.target.value)}
              />
            </Grid>

            {/* Ingredients */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Ingredients
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  fullWidth
                  label="Add Ingredient"
                  value={newIngredient}
                  onChange={(e) => setNewIngredient(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddIngredient()}
                />
                <IconButton onClick={handleAddIngredient} color="primary">
                  <AddIcon />
                </IconButton>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.ingredients?.map((ingredient) => (
                  <Chip
                    key={ingredient}
                    label={ingredient}
                    onDelete={() => handleRemoveIngredient(ingredient)}
                  />
                ))}
              </Box>
            </Grid>

            {/* Allergens */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Allergens
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  fullWidth
                  label="Add Allergen"
                  value={newAllergen}
                  onChange={(e) => setNewAllergen(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddAllergen()}
                />
                <IconButton onClick={handleAddAllergen} color="primary">
                  <AddIcon />
                </IconButton>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.allergens?.map((allergen) => (
                  <Chip
                    key={allergen}
                    label={allergen}
                    onDelete={() => handleRemoveAllergen(allergen)}
                  />
                ))}
              </Box>
            </Grid>

            {/* Customizations */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Customizations
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Customization Name"
                      value={newCustomization.name}
                      onChange={(e) =>
                        setNewCustomization((prev) => ({ ...prev, name: e.target.value }))
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        fullWidth
                        label="Option Label"
                        value={newCustomization.options[0].label}
                        onChange={(e) =>
                          setNewCustomization((prev) => ({
                            ...prev,
                            options: [{ ...prev.options[0], label: e.target.value }],
                          }))
                        }
                      />
                      <TextField
                        label="Price"
                        type="number"
                        value={newCustomization.options[0].price}
                        onChange={(e) =>
                          setNewCustomization((prev) => ({
                            ...prev,
                            options: [
                              { ...prev.options[0], price: parseFloat(e.target.value) },
                            ],
                          }))
                        }
                        InputProps={{
                          startAdornment: '$',
                        }}
                      />
                      <IconButton onClick={handleAddCustomization} color="primary">
                        <AddIcon />
                      </IconButton>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
              {formData.customizations?.map((customization, index) => (
                <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">{customization.name}</Typography>
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveCustomization(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                  {customization.options.map((option, optIndex) => (
                    <Chip
                      key={optIndex}
                      label={`${option.label} ($${option.price})`}
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              ))}
            </Grid>

            {/* Nutritional Info */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Nutritional Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <TextField
                    fullWidth
                    label="Calories"
                    type="number"
                    value={formData.nutritionalInfo?.calories}
                    onChange={(e) =>
                      handleNutritionalInfoChange('calories', parseInt(e.target.value))
                    }
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <TextField
                    fullWidth
                    label="Protein (g)"
                    type="number"
                    value={formData.nutritionalInfo?.protein}
                    onChange={(e) =>
                      handleNutritionalInfoChange('protein', parseInt(e.target.value))
                    }
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <TextField
                    fullWidth
                    label="Carbs (g)"
                    type="number"
                    value={formData.nutritionalInfo?.carbs}
                    onChange={(e) =>
                      handleNutritionalInfoChange('carbs', parseInt(e.target.value))
                    }
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <TextField
                    fullWidth
                    label="Fat (g)"
                    type="number"
                    value={formData.nutritionalInfo?.fat}
                    onChange={(e) =>
                      handleNutritionalInfoChange('fat', parseInt(e.target.value))
                    }
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Toggles */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.mobileApp}
                    onChange={(e) => handleChange('mobileApp', e.target.checked)}
                  />
                }
                label="Available in Mobile App"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isTopItem}
                    onChange={(e) => handleChange('isTopItem', e.target.checked)}
                  />
                }
                label="Featured Item"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {item ? 'Save Changes' : 'Add Item'}
          </Button>
        </DialogActions>
      </Dialog>

      <CategoryManager
        open={showCategoryManager}
        onClose={handleCategoryManagerClose}
        onSave={handleCategoryManagerClose}
        categories={newCategories}
      />
    </>
  );
}; 