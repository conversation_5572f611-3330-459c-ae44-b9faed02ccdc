export type TabValue = 'menu' | 'orders' | 'inventory' | 'reports' | 'layout';

export interface MenuItemCustomization {
  name: string;
  price: number;
  options: {
    label: string;
    price?: number;
  }[];
}

export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  image?: string;
  membershipDiscount?: number;
  preparationTime?: number;
  customizations?: MenuItemCustomization[];
  ingredients?: string[];
  allergens?: string[];
  nutritionalInfo?: NutritionalInfo;
  mobileApp: boolean;
  isTopItem?: boolean;
}

export interface MenuCategory {
  id: string;
  name: string;
  hasTopItems: boolean;
}

export interface MenuListProps {
  items: MenuItem[];
  onMobileAppToggle: (itemId: string, currentValue: boolean) => Promise<void>;
  onEditItem: (item: MenuItem) => void;
}

export interface MenuCategoriesProps {
  categories: MenuCategory[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  onReorderCategories?: (categories: MenuCategory[]) => void;
  isEditMode: boolean;
}

export interface MenuActionsProps {
  onAdd: () => void;
  onEdit: () => void;
  onSearch: (term: string) => void;
  searchTerm: string;
}

export interface MenuItemCardProps {
  item: MenuItem;
  onMobileAppToggle: (itemId: string, currentValue: boolean) => Promise<void>;
  onEditItem: (item: MenuItem) => void;
  onViewMetrics?: (item: MenuItem) => void;
} 