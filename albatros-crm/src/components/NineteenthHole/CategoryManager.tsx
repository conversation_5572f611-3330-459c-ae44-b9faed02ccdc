import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Switch,
  FormControlLabel,
  Typography,
  Box,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { MenuCategory } from './types';

interface CategoryManagerProps {
  open: boolean;
  onClose: () => void;
  onSave: (categories: MenuCategory[]) => void;
  categories: MenuCategory[];
}

export const CategoryManager: React.FC<CategoryManagerProps> = ({
  open,
  onClose,
  onSave,
  categories,
}) => {
  const [editedCategories, setEditedCategories] = useState<MenuCategory[]>(categories);
  const [newCategory, setNewCategory] = useState<MenuCategory>({
    id: '',
    name: '',
    hasTopItems: false,
  });
  const [editingCategory, setEditingCategory] = useState<MenuCategory | null>(null);

  const handleAddCategory = () => {
    if (newCategory.name.trim()) {
      const categoryId = newCategory.name.toLowerCase().replace(/\s+/g, '-');
      setEditedCategories([
        ...editedCategories,
        { ...newCategory, id: categoryId },
      ]);
      setNewCategory({ id: '', name: '', hasTopItems: false });
    }
  };

  const handleEditCategory = (category: MenuCategory) => {
    setEditingCategory(category);
  };

  const handleUpdateCategory = () => {
    if (editingCategory) {
      setEditedCategories(
        editedCategories.map((cat) =>
          cat.id === editingCategory.id ? editingCategory : cat
        )
      );
      setEditingCategory(null);
    }
  };

  const handleDeleteCategory = (categoryId: string) => {
    setEditedCategories(editedCategories.filter((cat) => cat.id !== categoryId));
  };

  const handleSubmit = () => {
    onSave(editedCategories);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Manage Categories</DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Add New Category */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Add New Category
            </Typography>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 2,
              mb: 2,
              flexWrap: 'wrap',
            }}>
              <TextField
                fullWidth
                label="Category Name"
                value={newCategory.name}
                onChange={(e) =>
                  setNewCategory((prev) => ({ ...prev, name: e.target.value }))
                }
                onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
                sx={{ flex: 1, minWidth: 200 }}
              />
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1,
                minWidth: 200,
              }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newCategory.hasTopItems}
                      onChange={(e) =>
                        setNewCategory((prev) => ({
                          ...prev,
                          hasTopItems: e.target.checked,
                        }))
                      }
                    />
                  }
                  label="Has Top Items"
                  sx={{ mr: 0 }}
                />
                <Button
                  variant="contained"
                  onClick={handleAddCategory}
                  disabled={!newCategory.name.trim()}
                  sx={{ ml: 'auto' }}
                >
                  Add
                </Button>
              </Box>
            </Box>
          </Grid>

          {/* Edit Category Dialog */}
          {editingCategory && (
            <Dialog
              open={!!editingCategory}
              onClose={() => setEditingCategory(null)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Edit Category</DialogTitle>
              <DialogContent>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Category Name"
                      value={editingCategory.name}
                      onChange={(e) =>
                        setEditingCategory((prev) =>
                          prev ? { ...prev, name: e.target.value } : null
                        )
                      }
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={editingCategory.hasTopItems}
                          onChange={(e) =>
                            setEditingCategory((prev) =>
                              prev
                                ? { ...prev, hasTopItems: e.target.checked }
                                : null
                            )
                          }
                        />
                      }
                      label="Has Top Items"
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setEditingCategory(null)}>Cancel</Button>
                <Button onClick={handleUpdateCategory} variant="contained">
                  Save Changes
                </Button>
              </DialogActions>
            </Dialog>
          )}

          {/* Categories List */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Existing Categories
            </Typography>
            <List>
              {editedCategories.map((category) => (
                <ListItem key={category.id}>
                  <ListItemText
                    primary={category.name}
                    secondary={`ID: ${category.id} | ${
                      category.hasTopItems ? 'Has Top Items' : 'No Top Items'
                    }`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleEditCategory(category)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteCategory(category.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 