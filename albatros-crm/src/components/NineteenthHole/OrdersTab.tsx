import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Avatar,
  Chip,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  styled,
} from '@mui/material';
import {
  Search as SearchIcon,
  Restaurant as RestaurantIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingIcon,
  Pending as PendingIcon,
  LocalDining as DiningIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const OrderCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[3],
  },
}));

const StatusChip = styled(Chip)(({ theme }) => ({
  fontWeight: 600,
  '&.pending': {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.dark,
  },
  '&.preparing': {
    backgroundColor: theme.palette.info.light,
    color: theme.palette.info.dark,
  },
  '&.ready': {
    backgroundColor: theme.palette.success.light,
    color: theme.palette.success.dark,
  },
  '&.cancelled': {
    backgroundColor: theme.palette.error.light,
    color: theme.palette.error.dark,
  },
}));

export const OrdersTab: React.FC = () => {
  return (
    <Box>
      {/* Statistics Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.light', mr: 2 }}>
                  <RestaurantIcon />
                </Avatar>
                <Typography variant="h6">Total Orders</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>234</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingIcon sx={{ mr: 0.5 }} />
                +18.5% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.light', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6">Revenue</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>$3,450</Typography>
              <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingIcon sx={{ mr: 0.5 }} />
                +12.2% from last month
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.light', mr: 2 }}>
                  <PendingIcon />
                </Avatar>
                <Typography variant="h6">Pending</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>8</Typography>
              <Typography variant="body2" color="text.secondary">
                Orders in queue
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.light', mr: 2 }}>
                  <DiningIcon />
                </Avatar>
                <Typography variant="h6">Ready</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>12</Typography>
              <Typography variant="body2" color="text.secondary">
                Ready for pickup
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Box sx={{ 
        display: 'flex', 
        gap: 2, 
        mb: 4,
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'stretch', sm: 'center' },
      }}>
        <TextField
          placeholder="Search orders..."
          variant="outlined"
          size="small"
          sx={{ flex: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Status</InputLabel>
          <Select label="Status" defaultValue="all">
            <MenuItem value="all">All Orders</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="preparing">Preparing</MenuItem>
            <MenuItem value="ready">Ready</MenuItem>
            <MenuItem value="cancelled">Cancelled</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Time</InputLabel>
          <Select label="Time" defaultValue="today">
            <MenuItem value="today">Today</MenuItem>
            <MenuItem value="week">This Week</MenuItem>
            <MenuItem value="month">This Month</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Orders List */}
      <Box>
        {[
          { id: '1', customer: 'John Smith', items: 2, total: 24.99, status: 'pending', time: '10:30 AM' },
          { id: '2', customer: 'Sarah Johnson', items: 1, total: 12.99, status: 'preparing', time: '10:25 AM' },
          { id: '3', customer: 'Mike Brown', items: 3, total: 35.99, status: 'ready', time: '10:15 AM' },
          { id: '4', customer: 'Emily Davis', items: 2, total: 28.99, status: 'cancelled', time: '10:00 AM' },
        ].map((order) => (
          <OrderCard key={order.id}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={4}>
                  <Typography variant="subtitle1" fontWeight={600}>
                    Order #{order.id}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {order.customer}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Items
                  </Typography>
                  <Typography variant="subtitle2">
                    {order.items} items
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Total
                  </Typography>
                  <Typography variant="subtitle2" fontWeight={600}>
                    ${order.total}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2}>
                  <Typography variant="body2" color="text.secondary">
                    Time
                  </Typography>
                  <Typography variant="subtitle2">
                    {order.time}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={2} sx={{ textAlign: 'right' }}>
                  <StatusChip
                    label={order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    className={order.status}
                    icon={
                      order.status === 'pending' ? <PendingIcon /> :
                      order.status === 'preparing' ? <RestaurantIcon /> :
                      order.status === 'ready' ? <DiningIcon /> :
                      <CancelIcon />
                    }
                  />
                </Grid>
                {order.status === 'preparing' && (
                  <Grid item xs={12}>
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Preparation Progress
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={65}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: 'info.light',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: 'info.main',
                          },
                        }}
                      />
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </OrderCard>
        ))}
      </Box>
    </Box>
  );
}; 