import React from 'react';
import { Box, Chip, useTheme, useMediaQuery } from '@mui/material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DragIndicator as DragIndicatorIcon } from '@mui/icons-material';
import { MenuCategoriesProps, MenuCategory } from './types';

interface SortableCategoryProps {
  category: MenuCategory;
  isSelected: boolean;
  isEditMode: boolean;
  isMobile: boolean;
  onClick: () => void;
}

const SortableCategory = ({
  category,
  isSelected,
  isEditMode,
  isMobile,
  onClick,
}: SortableCategoryProps) => {
  const theme = useTheme();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: category.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <Box
      ref={setNodeRef}
      style={style}
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
      }}
    >
      {isEditMode && (
        <Box
          {...attributes}
          {...listeners}
          sx={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'grab',
            color: theme.palette.text.secondary,
          }}
        >
          <DragIndicatorIcon fontSize="small" />
        </Box>
      )}
      <Chip
        label={category.name}
        onClick={onClick}
        sx={{
          px: 2,
          py: 1,
          fontSize: isMobile ? '0.875rem' : '1rem',
          fontWeight: isSelected ? 600 : 400,
          backgroundColor: isSelected
            ? theme.palette.primary.main
            : theme.palette.grey[100],
          color: isSelected
            ? theme.palette.primary.contrastText
            : theme.palette.text.primary,
          '&:hover': {
            backgroundColor: isSelected
              ? theme.palette.primary.dark
              : theme.palette.grey[200],
          },
          transition: 'all 0.2s ease-in-out',
          transform: isSelected ? 'scale(1.05)' : 'scale(1)',
          boxShadow: isSelected
            ? `0 2px 4px ${theme.palette.primary.main}40`
            : 'none',
          ...(isDragging && {
            boxShadow: theme.shadows[4],
          }),
        }}
      />
    </Box>
  );
};

export const MenuCategories: React.FC<MenuCategoriesProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  onReorderCategories,
  isEditMode,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = categories.findIndex((cat) => cat.id === active.id);
      const newIndex = categories.findIndex((cat) => cat.id === over.id);

      if (onReorderCategories) {
        onReorderCategories(arrayMove(categories, oldIndex, newIndex));
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 1,
        mb: 3,
        overflowX: 'auto',
        pb: 1,
        '&::-webkit-scrollbar': {
          height: '6px',
        },
        '&::-webkit-scrollbar-track': {
          background: theme.palette.grey[100],
          borderRadius: '3px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: theme.palette.grey[300],
          borderRadius: '3px',
          '&:hover': {
            background: theme.palette.grey[400],
          },
        },
      }}
    >
        <SortableContext
          items={categories.map(cat => cat.id)}
          strategy={horizontalListSortingStrategy}
        >
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
      {categories.map((category) => (
              <SortableCategory
          key={category.id}
                category={category}
                isSelected={selectedCategory === category.id}
                isEditMode={isEditMode}
                isMobile={isMobile}
          onClick={() => onCategoryChange(category.id)}
        />
      ))}
    </Box>
        </SortableContext>
      </Box>
    </DndContext>
  );
}; 