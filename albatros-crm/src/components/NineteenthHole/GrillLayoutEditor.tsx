import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Typography, IconButton, Paper, TextField, MenuItem, Select, Dialog, DialogTitle, DialogContent, DialogActions, InputLabel, FormControl, Slider, ToggleButton, ToggleButtonGroup } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import UploadIcon from '@mui/icons-material/Upload';
import EditIcon from '@mui/icons-material/Edit';
import RotateRightIcon from '@mui/icons-material/RotateRight';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import RectangleIcon from '@mui/icons-material/ViewAgenda';
import HexagonIcon from '@mui/icons-material/Hexagon';

const TABLE_SIZES = [2, 4, 6, 8];
const BARSTOOL_SIZE = 1;
const CANVAS_WIDTH = 1200;
const CANVAS_HEIGHT = 700;

const GRAY = '#d1d5db';
const GRAY_DARK = '#6b7280';
const GRAY_LIGHT = '#f3f4f6';
const SHADOW = '0 2px 8px #0002';

const TABLE_COLORS = ['#60a5fa', '#fbbf24', '#34d399', '#f87171'];
const BARSTOOL_COLOR = '#a3a3a3';
const BARTOP_COLOR = '#f59e42';
const DOOR_COLOR = '#64748b';
const WALL_COLOR = '#22223b';
const KITCHEN_COLOR = '#b91c1c';

const SERVER_LIST = [
  { id: 's1', name: 'Alice' },
  { id: 's2', name: 'Bob' },
  { id: 's3', name: 'Carlos' },
  { id: 's4', name: 'Dana' },
];

const SHAPES = [
  { value: 'square', icon: <CropSquareIcon />, label: 'Square' },
  { value: 'circle', icon: <RadioButtonUncheckedIcon />, label: 'Circle' },
  { value: 'rectangle', icon: <RectangleIcon />, label: 'Rectangle' },
  { value: 'hex', icon: <HexagonIcon />, label: 'Hexagon' },
];

function getRandomPosition() {
  return {
    x: Math.floor(Math.random() * (CANVAS_WIDTH - 100)) + 20,
    y: Math.floor(Math.random() * (CANVAS_HEIGHT - 100)) + 20,
  };
}

export type TableShape = 'circle' | 'square' | 'rectangle' | 'hex';
export type GrillItemType = 'table' | 'barstool' | 'bartop' | 'door' | 'wall' | 'kitchen';

export type GrillItem = {
  id: string;
  type: GrillItemType;
  size?: number;
  x: number;
  y: number;
  width?: number;
  height?: number;
  name?: string;
  selected?: boolean;
  serverId?: string;
  editingName?: boolean;
  // For wall: x2, y2
  x2?: number;
  y2?: number;
  angle?: number; // for rotation
  shape?: TableShape; // for tables
};

const LOCAL_STORAGE_KEY = 'grill_layout_v1';

const DEFAULT_BARTOP = { width: 180, height: 36, angle: 0 };

function getTableDims(size: number, shape: TableShape) {
  // Base size for 2 seats, scale up
  const base = 48;
  const scale = 1 + ((size ?? 2) - 2) * 0.25;
  if (shape === 'rectangle') return { width: base * scale * 1.6, height: base * scale };
  if (shape === 'hex') return { width: base * scale * 1.2, height: base * scale * 1.1 };
  return { width: base * scale, height: base * scale };
}

const renderWall = (item: GrillItem) => {
  const x1 = item.x;
  const y1 = item.y;
  const x2 = item.x2 ?? (item.x + 120);
  const y2 = item.y2 ?? item.y;
  return (
    <>
      <svg
        key={item.id}
        style={{ position: 'absolute', left: 0, top: 0, pointerEvents: 'none', zIndex: 0 }}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
      >
        <line x1={x1} y1={y1} x2={x2} y2={y2} stroke={GRAY_DARK} strokeWidth={8} />
      </svg>
      {item.selected && (
        <>
          {/* Start endpoint handle */}
          <Box
            sx={{ position: 'absolute', left: x1 - 8, top: y1 - 8, width: 16, height: 16, bgcolor: '#fff', border: '2px solid #14b8a6', borderRadius: '50%', cursor: 'pointer', zIndex: 10 }}
            onMouseDown={e => handleWallEndpointMouseDown(e, item.id, 'start')}
          />
          {/* End endpoint handle */}
          <Box
            sx={{ position: 'absolute', left: x2 - 8, top: y2 - 8, width: 16, height: 16, bgcolor: '#fff', border: '2px solid #14b8a6', borderRadius: '50%', cursor: 'pointer', zIndex: 10 }}
            onMouseDown={e => handleWallEndpointMouseDown(e, item.id, 'end')}
          />
        </>
      )}
    </>
  );
};

const renderRectElement = (item: GrillItem, label: string) => {
  const width = item.width ?? 60;
  const height = item.height ?? 60;
  const angle = item.angle || 0;
  return (
    <Box
      key={item.id}
      sx={{
        position: 'absolute',
        left: item.x,
        top: item.y,
        width,
        height,
        borderRadius: 2,
        background: GRAY,
        border: item.selected ? '3px solid #14b8a6' : '2px solid #64748b',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: GRAY_DARK,
        fontWeight: 700,
        fontSize: 18,
        boxShadow: SHADOW,
        cursor: 'grab',
        zIndex: item.selected ? 2 : 1,
        transition: 'box-shadow 0.2s',
        userSelect: 'none',
        transform: `rotate(${angle}deg)`
      }}
      onMouseDown={e => handleMouseDown(e, item.id)}
      onClick={e => { e.stopPropagation(); handleSelect(item.id); }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ color: GRAY_DARK, fontWeight: 700, mr: 1 }}>
            {item.name || label}
          </Typography>
          <IconButton size="small" sx={{ color: GRAY_DARK, p: 0.5 }} onClick={e => { e.stopPropagation(); handleEditName(item.id, item.name || ''); }}>
            <EditIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};

const GrillLayoutEditor: React.FC = () => {
  const [items, setItems] = useState<GrillItem[]>([]);
  const [draggedId, setDraggedId] = useState<string | null>(null);
  const [offset, setOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [toolbarType, setToolbarType] = useState<GrillItemType>('table');
  const [tableSize, setTableSize] = useState<number>(4);
  const [tableShape, setTableShape] = useState<TableShape>('square');
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [nameEditValue, setNameEditValue] = useState('');
  const [nameEditId, setNameEditId] = useState<string | null>(null);
  const [showServerDialog, setShowServerDialog] = useState(false);
  const [serverEditId, setServerEditId] = useState<string | null>(null);
  const [serverEditValue, setServerEditValue] = useState('');
  const [resizeInfo, setResizeInfo] = useState<{ id: string; anchor: 'right' | 'bottom' | 'corner' } | null>(null);
  const [wallDragInfo, setWallDragInfo] = useState<{ id: string; endpoint: 'start' | 'end' } | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Load from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (saved) {
      setItems(JSON.parse(saved));
    }
  }, []);

  // Save to localStorage
  const handleSaveLayout = () => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(items));
  };

  const handleLoadLayout = () => {
    const saved = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (saved) {
      setItems(JSON.parse(saved));
    }
  };

  // Add item
  const handleAddItem = () => {
    const { x, y } = getRandomPosition();
    let newItem: GrillItem;
    if (toolbarType === 'table') {
      const { width, height } = getTableDims(tableSize, tableShape);
      newItem = {
        id: `T-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'table',
        size: tableSize,
        x,
        y,
        width,
        height,
        name: '',
        shape: tableShape,
      };
    } else if (toolbarType === 'barstool') {
      newItem = {
        id: `B-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'barstool',
        size: BARSTOOL_SIZE,
        x,
        y,
        name: '',
      };
    } else if (toolbarType === 'bartop') {
      newItem = {
        id: `BT-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'bartop',
        x,
        y,
        width: DEFAULT_BARTOP.width,
        height: DEFAULT_BARTOP.height,
        angle: 0,
        name: 'Bartop',
      };
    } else if (toolbarType === 'door') {
      newItem = {
        id: `D-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'door',
        x,
        y,
        width: 32,
        height: 16,
        name: 'Door',
      };
    } else if (toolbarType === 'kitchen') {
      newItem = {
        id: `K-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'kitchen',
        x,
        y,
        width: 80,
        height: 60,
        name: 'Kitchen',
      };
    } else if (toolbarType === 'wall') {
      newItem = {
        id: `W-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type: 'wall',
        x,
        y,
        x2: x + 120,
        y2: y,
        name: 'Wall',
      };
    } else {
      return;
    }
    setItems(prev => [...prev, newItem]);
  };

  // Select item
  const handleSelect = (id: string) => {
    setItems(prev => prev.map(item => ({ ...item, selected: item.id === id })));
  };

  // Delete item
  const handleDelete = () => {
    setItems(prev => prev.filter(item => !item.selected));
  };

  // Duplicate item
  const handleDuplicate = () => {
    const selected = items.find(i => i.selected);
    if (!selected) return;
    const copy = { ...selected, id: `${selected.id}-copy-${Date.now()}`, x: selected.x + 30, y: selected.y + 30, selected: false };
    setItems(prev => [...prev, copy]);
  };

  // Drag logic
  const handleMouseDown = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    const item = items.find(i => i.id === id);
    if (!item) return;
    setDraggedId(id);
    setOffset({
      x: e.clientX - item.x,
      y: e.clientY - item.y,
    });
    handleSelect(id);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (resizeInfo) {
      setItems(prev => prev.map(item => {
        if (item.id !== resizeInfo.id) return item;
        if (item.type !== 'bartop') return item;
        let width = item.width || DEFAULT_BARTOP.width;
        let height = item.height || DEFAULT_BARTOP.height;
        if (resizeInfo.anchor === 'right') {
          width = Math.max(60, e.clientX - item.x);
        } else if (resizeInfo.anchor === 'bottom') {
          height = Math.max(24, e.clientY - item.y);
        } else if (resizeInfo.anchor === 'corner') {
          width = Math.max(60, e.clientX - item.x);
          height = Math.max(24, e.clientY - item.y);
        }
        return { ...item, width, height };
      }));
      return;
    }
    if (!draggedId) return;
    setItems(prev =>
      prev.map(item =>
        item.id === draggedId
          ? { ...item, x: e.clientX - offset.x, y: e.clientY - offset.y }
          : item
      )
    );
  };

  const handleMouseUp = () => {
    setDraggedId(null);
    setResizeInfo(null);
    setWallDragInfo(null);
  };

  // Deselect on canvas click
  const handleCanvasClick = () => {
    setItems(prev => prev.map(item => ({ ...item, selected: false })));
  };

  // Table naming
  const handleEditName = (id: string, current: string) => {
    setNameEditId(id);
    setNameEditValue(current);
    setShowNameDialog(true);
  };
  const handleNameDialogSave = () => {
    setItems(prev => prev.map(item => item.id === nameEditId ? { ...item, name: nameEditValue } : item));
    setShowNameDialog(false);
    setNameEditId(null);
    setNameEditValue('');
  };

  // Server assignment
  const handleAssignServer = (id: string, current: string | undefined) => {
    setServerEditId(id);
    setServerEditValue(current || '');
    setShowServerDialog(true);
  };
  const handleServerDialogSave = () => {
    setItems(prev => prev.map(item => item.id === serverEditId ? { ...item, serverId: serverEditValue } : item));
    setShowServerDialog(false);
    setServerEditId(null);
    setServerEditValue('');
  };

  // Bartop rotation
  const handleRotate = (id: string, angle: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, angle } : item));
  };

  // Table shape/size change
  const handleTableShapeChange = (id: string, shape: TableShape) => {
    setItems(prev => prev.map(item => {
      if (item.id !== id) return item;
      const { width, height } = getTableDims(item.size ?? 4, shape);
      return { ...item, shape, width, height };
    }));
  };
  const handleTableSizeChange = (id: string, size: number) => {
    setItems(prev => prev.map(item => {
      if (item.id !== id) return item;
      const { width, height } = getTableDims(size, item.shape ?? 'square');
      return { ...item, size, width, height };
    }));
  };
  const handleTableWidthChange = (id: string, width: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, width } : item));
  };
  const handleTableHeightChange = (id: string, height: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, height } : item));
  };

  // Wall endpoint drag logic
  const handleWallEndpointMouseDown = (e: React.MouseEvent, id: string, endpoint: 'start' | 'end') => {
    e.stopPropagation();
    setWallDragInfo({ id, endpoint });
  };

  const handleWallMouseMove = (e: React.MouseEvent) => {
    if (!wallDragInfo) return;
    setItems(prev => prev.map(item => {
      if (item.id !== wallDragInfo.id || item.type !== 'wall') return item;
      if (wallDragInfo.endpoint === 'start') {
        return { ...item, x: e.nativeEvent.offsetX, y: e.nativeEvent.offsetY };
      } else {
        return { ...item, x2: e.nativeEvent.offsetX, y2: e.nativeEvent.offsetY };
      }
    }));
  };

  // Door/Kitchen rotation
  const handleElementRotate = (id: string, angle: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, angle } : item));
  };

  // Door/Kitchen width/height
  const handleElementWidthChange = (id: string, width: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, width } : item));
  };
  const handleElementHeightChange = (id: string, height: number) => {
    setItems(prev => prev.map(item => item.id === id ? { ...item, height } : item));
  };

  // Render item
  const renderItem = (item: GrillItem) => {
    if (item.type === 'wall') return renderWall(item);
    if (item.type === 'door') return renderRectElement(item, 'Door');
    if (item.type === 'kitchen') return renderRectElement(item, 'Kitchen');
    if (item.type === 'bartop') {
      const width = item.width || DEFAULT_BARTOP.width;
      const height = item.height || DEFAULT_BARTOP.height;
      const angle = item.angle || 0;
      return (
        <Box
          key={item.id}
          sx={{
            position: 'absolute',
            left: item.x,
            top: item.y,
            width,
            height,
            borderRadius: 2,
            background: GRAY,
            border: item.selected ? '3px solid #14b8a6' : '2px solid #64748b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: GRAY_DARK,
            fontWeight: 700,
            fontSize: 18,
            boxShadow: SHADOW,
            cursor: 'grab',
            zIndex: item.selected ? 2 : 1,
            transition: 'box-shadow 0.2s',
            userSelect: 'none',
            transform: `rotate(${angle}deg)`
          }}
          onMouseDown={e => handleMouseDown(e, item.id)}
          onClick={e => { e.stopPropagation(); handleSelect(item.id); }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ color: GRAY_DARK, fontWeight: 700, mr: 1 }}>
                {item.name || 'Bartop'}
              </Typography>
              <IconButton size="small" sx={{ color: GRAY_DARK, p: 0.5 }} onClick={e => { e.stopPropagation(); handleEditName(item.id, item.name || ''); }}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
          {/* Resize handles */}
          {item.selected && (
            <>
              {/* Right handle */}
              <Box
                sx={{ position: 'absolute', right: -8, top: '50%', width: 16, height: 16, bgcolor: '#fff', border: '2px solid #14b8a6', borderRadius: '50%', cursor: 'ew-resize', zIndex: 10, transform: 'translateY(-50%)' }}
                onMouseDown={e => { e.stopPropagation(); setResizeInfo({ id: item.id, anchor: 'right' }); }}
              />
              {/* Bottom handle */}
              <Box
                sx={{ position: 'absolute', left: '50%', bottom: -8, width: 16, height: 16, bgcolor: '#fff', border: '2px solid #14b8a6', borderRadius: '50%', cursor: 'ns-resize', zIndex: 10, transform: 'translateX(-50%)' }}
                onMouseDown={e => { e.stopPropagation(); setResizeInfo({ id: item.id, anchor: 'bottom' }); }}
              />
              {/* Corner handle */}
              <Box
                sx={{ position: 'absolute', right: -8, bottom: -8, width: 16, height: 16, bgcolor: '#fff', border: '2px solid #14b8a6', borderRadius: '50%', cursor: 'nwse-resize', zIndex: 10 }}
                onMouseDown={e => { e.stopPropagation(); setResizeInfo({ id: item.id, anchor: 'corner' }); }}
              />
            </>
          )}
        </Box>
      );
    }
    if (item.type === 'table') {
      const width = item.width ?? getTableDims(item.size ?? 4, item.shape ?? 'square').width;
      const height = item.height ?? getTableDims(item.size ?? 4, item.shape ?? 'square').height;
      let shape = item.shape ?? 'square';
      let borderRadius = shape === 'circle' ? '50%' : shape === 'square' ? 4 : 2;
      let extra = {};
      if (shape === 'hex') {
        extra = { clipPath: 'polygon(25% 5%, 75% 5%, 100% 50%, 75% 95%, 25% 95%, 0% 50%)' };
      }
      if (shape === 'rectangle') {
        borderRadius = 6;
      }
      return (
        <Box
          key={item.id}
          sx={{
            position: 'absolute',
            left: item.x,
            top: item.y,
            width,
            height,
            borderRadius,
            background: GRAY,
            border: item.selected ? '3px solid #14b8a6' : '2px solid #64748b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: GRAY_DARK,
            fontWeight: 700,
            fontSize: 18,
            boxShadow: SHADOW,
            cursor: 'grab',
            zIndex: item.selected ? 2 : 1,
            transition: 'box-shadow 0.2s',
            userSelect: 'none',
            ...extra,
          }}
          onMouseDown={e => handleMouseDown(e, item.id)}
          onClick={e => { e.stopPropagation(); handleSelect(item.id); }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ color: GRAY_DARK, fontWeight: 700, mr: 1 }}>
                {item.name || `T-${item.size}`}
              </Typography>
              <IconButton size="small" sx={{ color: GRAY_DARK, p: 0.5 }} onClick={e => { e.stopPropagation(); handleEditName(item.id, item.name || ''); }}>
                <EditIcon fontSize="small" />
              </IconButton>
              <ToggleButtonGroup
                value={shape}
                exclusive
                onChange={(_, v) => v && handleTableShapeChange(item.id, v)}
                size="small"
                sx={{ ml: 1 }}
              >
                {SHAPES.map(s => (
                  <ToggleButton key={s.value} value={s.value} sx={{ p: 0.5, minWidth: 28, minHeight: 28 }}>{s.icon}</ToggleButton>
                ))}
              </ToggleButtonGroup>
            </Box>
            <Slider
              min={2}
              max={8}
              step={2}
              value={item.size ?? 4}
              onChange={(_, v) => handleTableSizeChange(item.id, Number(v))}
              sx={{ width: 80, mt: 1 }}
              marks={[{ value: 2, label: '2' }, { value: 4, label: '4' }, { value: 6, label: '6' }, { value: 8, label: '8' }]}
            />
          </Box>
        </Box>
      );
    }
    return (
      <Box
        key={item.id}
        sx={{
          position: 'absolute',
          left: item.x,
          top: item.y,
          width: item.type === 'barstool' ? 36 : item.width ?? 60,
          height: item.type === 'barstool' ? 36 : item.height ?? 60,
          borderRadius: item.type === 'barstool' ? '50%' : 2,
          background: GRAY,
          border: item.selected ? '3px solid #14b8a6' : '2px solid #64748b',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: GRAY_DARK,
          fontWeight: 700,
          fontSize: 18,
          boxShadow: SHADOW,
          cursor: 'grab',
          zIndex: item.selected ? 2 : 1,
          transition: 'box-shadow 0.2s',
          userSelect: 'none',
        }}
        onMouseDown={e => handleMouseDown(e, item.id)}
        onClick={e => { e.stopPropagation(); handleSelect(item.id); }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ color: GRAY_DARK, fontWeight: 700, mr: 1 }}>
              {item.type === 'barstool' ? (item.name || 'Bar') :
                item.type === 'door' ? (item.name || 'Door') :
                item.type === 'kitchen' ? (item.name || 'Kitchen') : ''}
            </Typography>
            {(item.type === 'barstool') && (
              <IconButton size="small" sx={{ color: GRAY_DARK, p: 0.5 }} onClick={e => { e.stopPropagation(); handleEditName(item.id, item.name || ''); }}>
                <EditIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  // Right panel: properties of selected item
  const selected = items.find(i => i.selected);

  return (
    <Box sx={{ display: 'flex', height: CANVAS_HEIGHT + 60 }}>
      {/* Left: Elements */}
      <Box sx={{ width: 120, bgcolor: GRAY_LIGHT, p: 2, borderRight: '1px solid #e5e7eb', display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Elements</Typography>
        <ToggleButtonGroup
          orientation="vertical"
          value={toolbarType}
          exclusive
          onChange={(_, v) => v && setToolbarType(v)}
          sx={{ mb: 2 }}
        >
          <ToggleButton value="table" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 48 }}>{<CropSquareIcon />}</ToggleButton>
          <ToggleButton value="barstool" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 48 }}>{<RadioButtonUncheckedIcon />}</ToggleButton>
          <ToggleButton value="bartop" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 24 }}>Bar</ToggleButton>
          <ToggleButton value="door" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 24 }}>Door</ToggleButton>
          <ToggleButton value="wall" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 12 }}>Wall</ToggleButton>
          <ToggleButton value="kitchen" sx={{ p: 0.5, mb: 1, minWidth: 48, minHeight: 32 }}>K</ToggleButton>
        </ToggleButtonGroup>
        {toolbarType === 'table' && (
          <>
            <ToggleButtonGroup
              value={tableShape}
              exclusive
              onChange={(_, v) => v && setTableShape(v)}
              size="small"
              sx={{ mb: 1 }}
            >
              {SHAPES.map(s => (
                <ToggleButton key={s.value} value={s.value} sx={{ p: 0.5, minWidth: 28, minHeight: 28 }}>{s.icon}</ToggleButton>
              ))}
            </ToggleButtonGroup>
            <FormControl size="small" sx={{ mb: 1 }}>
              <InputLabel>Seats</InputLabel>
              <Select
                value={tableSize}
                label="Seats"
                onChange={e => setTableSize(Number(e.target.value))}
                sx={{ minWidth: 80 }}
              >
                {TABLE_SIZES.map(size => (
                  <MenuItem key={size} value={size}>{size}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </>
        )}
        <Button variant="contained" sx={{ mt: 2 }} onClick={handleAddItem}>Add</Button>
      </Box>
      {/* Center: Canvas */}
      <Box sx={{ flex: 1, bgcolor: '#fff', position: 'relative', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Paper
          ref={canvasRef}
          sx={{
            width: CANVAS_WIDTH,
            height: CANVAS_HEIGHT,
            position: 'relative',
            background: `repeating-linear-gradient(0deg, #e5e7eb, #e5e7eb 1px, transparent 1px, transparent 40px), repeating-linear-gradient(90deg, #e5e7eb, #e5e7eb 1px, transparent 1px, transparent 40px)`,
            border: '2px solid #e5e7eb',
            overflow: 'hidden',
            cursor: draggedId ? 'grabbing' : 'default',
            userSelect: 'none',
            boxShadow: SHADOW,
          }}
          onMouseMove={e => { handleMouseMove(e); handleWallMouseMove(e); }}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onClick={handleCanvasClick}
        >
          {/* Render walls first */}
          {items.filter(i => i.type === 'wall').map(renderItem)}
          {/* Render other items */}
          {items.filter(i => i.type !== 'wall').map(renderItem)}
        </Paper>
      </Box>
      {/* Right: Properties */}
      <Box sx={{ width: 260, bgcolor: GRAY_LIGHT, p: 3, borderLeft: '1px solid #e5e7eb', display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Properties</Typography>
        {selected ? (
          <>
            <TextField
              label="Name"
              value={selected.name || ''}
              onChange={e => setItems(prev => prev.map(item => item.id === selected.id ? { ...item, name: e.target.value } : item))}
              size="small"
              sx={{ mb: 2 }}
            />
            <Typography variant="body2" sx={{ mb: 1 }}>Type: {selected.type.charAt(0).toUpperCase() + selected.type.slice(1)}</Typography>
            {selected.type === 'table' && (
              <>
                <ToggleButtonGroup
                  value={selected.shape ?? 'square'}
                  exclusive
                  onChange={(_, v) => v && handleTableShapeChange(selected.id, v)}
                  size="small"
                  sx={{ mb: 1 }}
                >
                  {SHAPES.map(s => (
                    <ToggleButton key={s.value} value={s.value} sx={{ p: 0.5, minWidth: 28, minHeight: 28 }}>{s.icon}</ToggleButton>
                  ))}
                </ToggleButtonGroup>
                <Slider
                  min={2}
                  max={8}
                  step={2}
                  value={selected.size ?? 4}
                  onChange={(_, v) => handleTableSizeChange(selected.id, Number(v))}
                  sx={{ width: 120, mb: 1 }}
                  marks={[{ value: 2, label: '2' }, { value: 4, label: '4' }, { value: 6, label: '6' }, { value: 8, label: '8' }]}
                />
                <Typography variant="body2" sx={{ mb: 1 }}>Fine-tune Size</Typography>
                <Slider
                  min={32}
                  max={200}
                  value={selected.width ?? getTableDims(selected.size ?? 4, selected.shape ?? 'square').width}
                  onChange={(_, v) => handleTableWidthChange(selected.id, Number(v))}
                  sx={{ width: 120, mb: 1 }}
                  marks={[{ value: 32, label: 'S' }, { value: 200, label: 'L' }]}
                />
                <Slider
                  min={32}
                  max={200}
                  value={selected.height ?? getTableDims(selected.size ?? 4, selected.shape ?? 'square').height}
                  onChange={(_, v) => handleTableHeightChange(selected.id, Number(v))}
                  sx={{ width: 120, mb: 2 }}
                  marks={[{ value: 32, label: 'S' }, { value: 200, label: 'L' }]}
                />
                <FormControl size="small" sx={{ mb: 2 }}>
                  <InputLabel>Server</InputLabel>
                  <Select
                    value={selected.serverId || ''}
                    label="Server"
                    onChange={e => setItems(prev => prev.map(item => item.id === selected.id ? { ...item, serverId: e.target.value } : item))}
                  >
                    <MenuItem value="">Unassigned</MenuItem>
                    {SERVER_LIST.map(server => (
                      <MenuItem key={server.id} value={server.id}>{server.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </>
            )}
            {selected.type === 'bartop' && (
              <>
                <Typography variant="body2" sx={{ mb: 1 }}>Width</Typography>
                <Slider
                  min={60}
                  max={400}
                  value={selected.width || DEFAULT_BARTOP.width}
                  onChange={(_, v) => setItems(prev => prev.map(item => item.id === selected.id ? { ...item, width: Number(v) } : item))}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" sx={{ mb: 1 }}>Height</Typography>
                <Slider
                  min={24}
                  max={120}
                  value={selected.height || DEFAULT_BARTOP.height}
                  onChange={(_, v) => setItems(prev => prev.map(item => item.id === selected.id ? { ...item, height: Number(v) } : item))}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" sx={{ mb: 1 }}>Rotation</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Slider
                    min={0}
                    max={359}
                    value={selected.angle || 0}
                    onChange={(_, v) => handleRotate(selected.id, Number(v))}
                    sx={{ flex: 1 }}
                  />
                  <IconButton onClick={() => handleRotate(selected.id, ((selected.angle || 0) + 15) % 360)}>
                    <RotateRightIcon />
                  </IconButton>
                </Box>
              </>
            )}
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button variant="outlined" startIcon={<ContentCopyIcon />} onClick={handleDuplicate}>Duplicate</Button>
              <Button variant="outlined" color="error" startIcon={<DeleteIcon />} onClick={handleDelete}>Delete</Button>
            </Box>
          </>
        ) : (
          <Typography variant="body2" color="text.secondary">Select an item to edit its properties.</Typography>
        )}
        <Box sx={{ flex: 1 }} />
        <Button variant="contained" startIcon={<SaveIcon />} onClick={handleSaveLayout} sx={{ mt: 2 }}>Save Layout</Button>
        <Button variant="outlined" startIcon={<UploadIcon />} onClick={handleLoadLayout}>Load Layout</Button>
      </Box>
      {/* Table Name Dialog */}
      <Dialog open={showNameDialog} onClose={() => setShowNameDialog(false)}>
        <DialogTitle>Edit Name</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            value={nameEditValue}
            onChange={e => setNameEditValue(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNameDialog(false)}>Cancel</Button>
          <Button onClick={handleNameDialogSave} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
      {/* Server Assignment Dialog */}
      <Dialog open={showServerDialog} onClose={() => setShowServerDialog(false)}>
        <DialogTitle>Assign Server</DialogTitle>
        <DialogContent>
          <FormControl fullWidth>
            <InputLabel>Server</InputLabel>
            <Select
              value={serverEditValue}
              label="Server"
              onChange={e => setServerEditValue(e.target.value)}
            >
              <MenuItem value="">Unassigned</MenuItem>
              {SERVER_LIST.map(server => (
                <MenuItem key={server.id} value={server.id}>{server.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowServerDialog(false)}>Cancel</Button>
          <Button onClick={handleServerDialogSave} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GrillLayoutEditor; 