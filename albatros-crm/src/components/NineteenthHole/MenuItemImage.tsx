import React, { useState, useEffect } from 'react';
import { Box, Skeleton } from '@mui/material';
import styled from '@emotion/styled';

const ImageContainer = styled(Box)`
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
`;

const StyledImage = styled('img')<{ loaded: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
  opacity: ${props => props.loaded ? 1 : 0};
`;

const BlurImage = styled('img')`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(10px);
  transform: scale(1.1);
`;

interface MenuItemImageProps {
  src: string;
  alt: string;
}

export const MenuItemImage: React.FC<MenuItemImageProps> = ({ src, alt }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Create a low-quality placeholder version of the image
  const getLowQualitySrc = (src: string) => {
    // If the image is from a CDN that supports quality parameters, use them
    if (src.includes('cloudinary.com')) {
      return src.replace('/upload/', '/upload/q_10,f_auto/');
    }
    // Otherwise, return the original src
    return src;
  };

  useEffect(() => {
    setIsLoading(true);
    setIsLoaded(false);
    setIsError(false);

    const img = new Image();
    const lowQualitySrc = getLowQualitySrc(src);
    
    // First load the low quality image
    img.src = lowQualitySrc;
    img.onload = () => {
      setIsLoading(false);
      
      // Then load the full quality image
      const fullQualityImg = new Image();
      fullQualityImg.src = src;
      fullQualityImg.onload = () => {
        // Small delay for smooth transition
        setTimeout(() => setIsLoaded(true), 100);
      };
      fullQualityImg.onerror = () => setIsError(true);
    };
    img.onerror = () => setIsError(true);

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  return (
    <ImageContainer>
      {!isError ? (
        <>
          {isLoading ? (
            <Skeleton
              variant="rectangular"
              width="100%"
              height="100%"
              animation="wave"
            />
          ) : (
            <>
              <BlurImage src={getLowQualitySrc(src)} alt={alt} />
              <StyledImage
                src={src}
                alt={alt}
                loaded={isLoaded}
              />
            </>
          )}
        </>
      ) : (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          animation="wave"
        />
      )}
    </ImageContainer>
  );
}; 