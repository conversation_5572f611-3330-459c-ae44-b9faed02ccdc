import React, { useState } from 'react';
import styles from './BackOffice.module.css';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { FaHeading, FaParagraph, FaImage, FaRegDotCircle, FaMinus, FaShareAlt, FaUtensils, FaClock, FaStar, FaCode, FaGlobe, FaFacebook, FaGoogle, FaTrashAlt } from 'react-icons/fa';

// Type definitions
interface Block {
  id: string;
  type: string;
  content: string | any[];
}

interface TemplateSettings {
  bgColor: string;
  contentBgColor: string;
  status: string;
}

interface DeleteDialog {
  open: boolean;
  blockId: string | null;
}

interface SocialLink {
  type: 'website' | 'facebook' | 'google';
  url: string;
}

const BLOCKS = [
  { type: 'headline', label: 'Headline' },
  { type: 'paragraph', label: 'Paragraph' },
  { type: 'image', label: 'Image' },
  { type: 'button', label: 'Button' },
  { type: 'separator', label: 'Separator' },
  { type: 'social', label: 'Social', pro: true },
  { type: 'menu', label: 'Menu', pro: true },
  { type: 'timer', label: 'Timer', pro: true },
  { type: 'review', label: 'Review', pro: true },
  { type: 'customHtml', label: 'Custom HTML', pro: true },
];

const DEFAULT_BLOCKS: Block[] = [
  { id: '1', type: 'headline', content: 'WE TRULY VALUE YOUR OPINION!' },
  { id: '2', type: 'paragraph', content: "Hi {$firstname}, We're so glad you've chosen to purchase from us." },
  { id: '3', type: 'paragraph', content: "To help us keep providing top-notch [Product/Service], we'd be grateful if you could rate your experience with us on a scale of 1 to 10." },
  { id: '4', type: 'rating', content: '' },
  { id: '5', type: 'button', content: 'Share Your Experience' },
];

const BLOCK_ICONS: Record<string, React.ReactElement> = {
  headline: React.createElement(FaHeading, { className: styles.blockPaletteIcon }),
  paragraph: React.createElement(FaParagraph, { className: styles.blockPaletteIcon }),
  image: React.createElement(FaImage, { className: styles.blockPaletteIcon }),
  button: React.createElement(FaRegDotCircle, { className: styles.blockPaletteIcon }),
  separator: React.createElement(FaMinus, { className: styles.blockPaletteIcon }),
  social: React.createElement(FaShareAlt, { className: styles.blockPaletteIcon }),
  menu: React.createElement(FaUtensils, { className: styles.blockPaletteIcon }),
  timer: React.createElement(FaClock, { className: styles.blockPaletteIcon }),
  review: React.createElement(FaStar, { className: styles.blockPaletteIcon }),
  customHtml: React.createElement(FaCode, { className: styles.blockPaletteIcon }),
};

// Mock function to get course profile data (replace with real hook/service as needed)
function getCourseProfile() {
  return {
    courseWebsite: 'https://www.twincreeksgc.com',
    facebookUrl: 'https://facebook.com/twincreeksgc',
    googleUrl: 'https://g.page/twincreeksgc',
  };
}

interface BlockPaletteProps {
  onDragStart: (type: string) => void;
  templateType: string;
  onImageUpload: (file: File) => void;
}

function BlockPalette({ onDragStart, templateType, onImageUpload }: BlockPaletteProps) {
  const getAvailableBlocks = () => {
    switch (templateType) {
      case 'SMS':
        return BLOCKS.filter(block => ['headline', 'paragraph', 'button'].includes(block.type));
      case 'In-App':
        return BLOCKS.filter(block => ['headline', 'paragraph', 'button', 'image', 'separator'].includes(block.type));
      case 'Email':
      default:
        return BLOCKS;
    }
  };

  const availableBlocks = getAvailableBlocks();

  return (
    <div className={styles.blockPalettePanel}>
      <h4>CONTENT BLOCKS</h4>
      {availableBlocks.map(block => (
        <div
          key={block.type}
          className={block.pro ? styles.blockPalettePro : styles.blockPaletteItem}
          draggable={true}
          onDragStart={e => {
            onDragStart(block.type);
          }}
          onDragEnd={e => e.preventDefault()}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
            {BLOCK_ICONS[block.type]}
            <span style={{ marginTop: 8, fontWeight: 600, fontSize: '1.08rem', color: '#22223b', letterSpacing: 0.2 }}>{block.label}</span>
            {block.type === 'image' && (
              <>
                <input
                  type="file"
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="upload-image-input"
                  onChange={e => {
                    if (e.target.files && e.target.files[0]) {
                      onImageUpload(e.target.files[0]);
                      e.target.value = '';
                    }
                  }}
                />
                <label htmlFor="upload-image-input" style={{ marginTop: 6, cursor: 'pointer', color: '#6366f1', fontWeight: 500, fontSize: '0.95rem' }}>
                  Upload Image
                </label>
              </>
            )}
            {block.pro && <span className={styles.proBadge}>PRO</span>}
          </div>
        </div>
      ))}
    </div>
  );
}

interface SortableBlockProps {
  id: string;
  type: string;
  content: string | any[];
  onEdit: (id: string, content: string | any[]) => void;
  templateType: string;
  preview?: boolean;
  onDelete: (blockId: string) => void;
}

function SortableBlock({ id, type, content, onEdit, templateType, preview = false, onDelete }: SortableBlockProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  
  // SMS-specific styling and restrictions
  const isSMS = templateType === 'SMS';
  const smsStyles = {
    fontSize: '14px',
    lineHeight: '1.4',
    maxWidth: '160px',
    wordWrap: 'break-word' as const,
  };

  // Social block logic
  if (type === 'social' && !isSMS) {
    const courseProfile = getCourseProfile();
    // content: array of { type: 'website'|'facebook'|'google', url: string }
    let selectedLinks = Array.isArray(content) ? content : [];
    const allLinks = [
      { type: 'website', label: 'Website', url: courseProfile.courseWebsite, icon: React.createElement(FaGlobe, { style: { fontSize: 22, color: '#0ea5e9' } }) },
      { type: 'facebook', label: 'Facebook', url: courseProfile.facebookUrl, icon: React.createElement(FaFacebook, { style: { fontSize: 22, color: '#1877f3' } }) },
      { type: 'google', label: 'Google', url: courseProfile.googleUrl, icon: React.createElement(FaGoogle, { style: { fontSize: 22, color: '#ea4335' } }) },
    ];
    if (!preview) {
      return (
        <div className={styles.previewBlock} ref={setNodeRef} {...attributes} {...listeners} style={{ background: 'transparent' }}>
          <div style={{ display: 'flex', gap: 18, marginBottom: 10 }}>
            {allLinks.map(link => (
              <label key={link.type} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer', fontSize: 13 }}>
                <input
                  type="checkbox"
                  checked={!!selectedLinks.find((l: SocialLink) => l.type === link.type)}
                  onChange={e => {
                    let newLinks = selectedLinks.filter((l: SocialLink) => l.type !== link.type);
                    if (e.target.checked) newLinks = [...newLinks, { type: link.type, url: link.url }];
                    onEdit(id, newLinks);
                  }}
                  style={{ marginBottom: 4 }}
                />
                {link.icon}
                <span style={{ marginTop: 2 }}>{link.label}</span>
              </label>
            ))}
          </div>
          <div style={{ display: 'flex', gap: 16, marginTop: 8 }}>
            {selectedLinks.map((link: SocialLink) => {
              const found = allLinks.find(l => l.type === link.type);
              return found ? (
                <a key={link.type} href={found.url} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                  {found.icon}
                </a>
              ) : null;
            })}
          </div>
        </div>
      );
    } else {
      // Preview mode: just show the selected icons as links
      return (
        <div className={styles.previewBlock} ref={setNodeRef} style={{ background: 'transparent', justifyContent: 'center', display: 'flex', gap: 18 }}>
          {selectedLinks.map((link: SocialLink) => {
            const found = allLinks.find(l => l.type === link.type);
            return found ? (
              <a key={link.type} href={found.url} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                {found.icon}
              </a>
            ) : null;
          })}
        </div>
      );
    }
  }

  return (
    <div
      ref={setNodeRef}
      className={styles.previewBlock}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        cursor: 'grab',
        position: 'relative',
        ...(isSMS && { maxWidth: '160px', margin: '0 auto' }),
      }}
      {...attributes}
      {...listeners}
    >
      {!preview && (
        <button
          onClick={() => onDelete(id)}
          className={styles.deleteBlockButton}
          title="Delete block"
        >
          {React.createElement(FaTrashAlt)}
        </button>
      )}
      {type === 'headline' && (
        <div style={{ padding: '16px 24px', ...(isSMS && smsStyles) }}>
          {preview ? (
            <h1 style={{ margin: 0, fontSize: isSMS ? '16px' : '24px', fontWeight: 'bold', color: '#1f2937' }}>
              {content}
            </h1>
          ) : (
            <input
              type="text"
              value={content as string}
              onChange={e => onEdit(id, e.target.value)}
              placeholder="Enter headline..."
              style={{
                width: '100%',
                fontSize: isSMS ? '16px' : '24px',
                fontWeight: 'bold',
                border: 'none',
                outline: 'none',
                background: 'transparent',
                color: '#1f2937',
              }}
            />
          )}
        </div>
      )}

      {type === 'paragraph' && (
        <div style={{ padding: '16px 24px', ...(isSMS && smsStyles) }}>
          {preview ? (
            <p style={{ margin: 0, fontSize: isSMS ? '14px' : '16px', lineHeight: '1.5', color: '#374151' }}>
              {content}
            </p>
          ) : (
            <textarea
              value={content as string}
              onChange={e => onEdit(id, e.target.value)}
              placeholder="Enter paragraph text..."
              style={{
                width: '100%',
                minHeight: '80px',
                fontSize: isSMS ? '14px' : '16px',
                lineHeight: '1.5',
                border: 'none',
                outline: 'none',
                background: 'transparent',
                color: '#374151',
                resize: 'vertical',
              }}
            />
          )}
        </div>
      )}

      {type === 'image' && (
        <div style={{ padding: '16px 24px' }}>
          {preview ? (
            <img
              src={content as string}
              alt="Template image"
              style={{
                width: '100%',
                maxWidth: '400px',
                height: 'auto',
                borderRadius: '8px',
                display: 'block',
                margin: '0 auto',
              }}
            />
          ) : (
            <div>
              <input
                type="text"
                value={content as string}
                onChange={e => onEdit(id, e.target.value)}
                placeholder="Enter image URL..."
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                }}
              />
              {content && (
                <img
                  src={content as string}
                  alt="Preview"
                  style={{
                    width: '100%',
                    maxWidth: '200px',
                    height: 'auto',
                    borderRadius: '4px',
                    marginTop: '8px',
                  }}
                />
              )}
            </div>
          )}
        </div>
      )}

      {type === 'button' && (
        <div style={{ padding: '16px 24px', textAlign: 'center' }}>
          {preview ? (
            <button
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                padding: isSMS ? '8px 16px' : '12px 24px',
                borderRadius: '6px',
                fontSize: isSMS ? '14px' : '16px',
                fontWeight: '600',
                cursor: 'pointer',
              }}
            >
              {content}
            </button>
          ) : (
            <input
              type="text"
              value={content as string}
              onChange={e => onEdit(id, e.target.value)}
              placeholder="Enter button text..."
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                textAlign: 'center',
              }}
            />
          )}
        </div>
      )}

      {type === 'separator' && (
        <div style={{ padding: '16px 24px' }}>
          <hr style={{ border: 'none', borderTop: '1px solid #e5e7eb', margin: 0 }} />
        </div>
      )}
    </div>
  );
}

interface TemplateSettingsProps {
  settings: TemplateSettings;
  onChange: (settings: TemplateSettings) => void;
}

function TemplateSettings({ settings, onChange }: TemplateSettingsProps) {
  return (
    <div className={styles.settingsPanel}>
      <h4>TEMPLATE SETTINGS</h4>
      <div className={styles.formGroup}>
        <label>Background Color</label>
        <input
          type="color"
          value={settings.bgColor}
          onChange={e => onChange({ ...settings, bgColor: e.target.value })}
          className={styles.colorInput}
        />
      </div>
      <div className={styles.formGroup}>
        <label>Content Background</label>
        <input
          type="color"
          value={settings.contentBgColor}
          onChange={e => onChange({ ...settings, contentBgColor: e.target.value })}
          className={styles.colorInput}
        />
      </div>
      <div className={styles.formGroup}>
        <label>Status</label>
        <select
          value={settings.status}
          onChange={e => onChange({ ...settings, status: e.target.value })}
          className={styles.formSelect}
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="draft">Draft</option>
        </select>
      </div>
    </div>
  );
}

interface TemplateEditorProps {
  onClose: () => void;
  template?: any;
}

const TemplateEditor = ({ onClose, template }: TemplateEditorProps) => {
  const [blocks, setBlocks] = useState<Block[]>(template?.blocks || DEFAULT_BLOCKS);
  const [templateType, setTemplateType] = useState(template?.type || 'Email');
  const [templateName, setTemplateName] = useState(template?.name || '');
  const [settings, setSettings] = useState<TemplateSettings>({
    bgColor: template?.settings?.bgColor || '#ffffff',
    contentBgColor: template?.settings?.contentBgColor || '#ffffff',
    status: template?.settings?.status || 'draft',
  });
  const [dragType, setDragType] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialog>({ open: false, blockId: null });

  const getAvailableBlocks = () => {
    switch (templateType) {
      case 'SMS':
        return BLOCKS.filter(block => ['headline', 'paragraph', 'button'].includes(block.type));
      case 'In-App':
        return BLOCKS.filter(block => ['headline', 'paragraph', 'button', 'image', 'separator'].includes(block.type));
      case 'Email':
      default:
        return BLOCKS;
    }
  };

  const getDefaultBlocksForType = () => {
    switch (templateType) {
      case 'SMS':
        return [
          { id: '1', type: 'headline', content: 'SMS HEADLINE' },
          { id: '2', type: 'paragraph', content: 'SMS message content here...' },
          { id: '3', type: 'button', content: 'Action' },
        ];
      case 'In-App':
        return [
          { id: '1', type: 'headline', content: 'In-App Notification' },
          { id: '2', type: 'paragraph', content: 'In-app message content...' },
          { id: '3', type: 'button', content: 'View Details' },
        ];
      case 'Email':
      default:
        return DEFAULT_BLOCKS;
    }
  };

  const handleTemplateTypeChange = (newType: string) => {
    setTemplateType(newType);
    setBlocks(getDefaultBlocksForType());
  };

  const handlePaletteDragStart = (type: string) => {
    setDragType(type);
  };

  const handleDrop = (event: React.DragEvent) => {
    if (dragType) {
      setBlocks((prev: Block[]) => [
        ...prev,
        { id: Math.random().toString(36).substr(2, 9), type: dragType, content: '' },
      ]);
      setDragType(null);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setBlocks((items: Block[]) => {
        const oldIndex = items.findIndex((i: Block) => i.id === active.id);
        const newIndex = items.findIndex((i: Block) => i.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const handleEditBlock = (id: string, content: string | any[]) => {
    setBlocks((blocks: Block[]) => blocks.map((b: Block) => b.id === id ? { ...b, content } : b));
  };

  const handleSave = () => {
    const templateData = {
      id: template?.id || Date.now().toString(),
      name: templateName,
      type: templateType,
      blocks,
      settings,
      createdAt: template?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    console.log('Saving template:', templateData);
    onClose();
  };

  const handleCancelRequest = () => {
    onClose();
  };

  const handleCancelConfirm = () => {
    onClose();
  };

  const handleCancelBack = () => {
    // Stay in editor
  };

  const handleImageUpload = (file: File) => {
    const url = URL.createObjectURL(file);
    setBlocks((prev: Block[]) => [
      ...prev,
      { id: Math.random().toString(36).substr(2, 9), type: 'image', content: url },
    ]);
  };

  const handleDeleteBlock = (blockId: string) => {
    setDeleteDialog({ open: true, blockId });
  };

  const confirmDeleteBlock = () => {
    setBlocks((blocks: Block[]) => blocks.filter((b: Block) => b.id !== deleteDialog.blockId));
    setDeleteDialog({ open: false, blockId: null });
  };
  
  const cancelDeleteBlock = () => {
    setDeleteDialog({ open: false, blockId: null });
  };

  return (
    <div className={styles.templateEditorLayout} style={{ position: 'relative' }}>
      {/* Left: Block Palette */}
      <BlockPalette onDragStart={handlePaletteDragStart} templateType={templateType} onImageUpload={handleImageUpload} />

      {/* Center: Live Preview */}
      <div className={styles.previewPanel}>
        <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={blocks.map((b: Block) => b.id)} strategy={verticalListSortingStrategy}>
            <div className={styles.previewCard} style={{ background: settings.bgColor }} onDragOver={e => e.preventDefault()} onDrop={handleDrop}>
              {/* Template Name and Type Input */}
              <div style={{ padding: '0 24px 18px 24px' }}>
                <input
                  type="text"
                  value={templateName}
                  onChange={e => setTemplateName(e.target.value)}
                  placeholder={template ? "Template name..." : "Enter template name..."}
                  style={{
                    width: '100%',
                    marginBottom: 12,
                    padding: '10px 14px',
                    borderRadius: 8,
                    border: '1.5px solid #e5e7eb',
                    fontSize: '1.08rem',
                    fontWeight: 500,
                    background: '#f8fafc',
                    outline: 'none',
                  }}
                />
                <select
                  value={templateType}
                  onChange={e => handleTemplateTypeChange(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '10px 14px',
                    borderRadius: 8,
                    border: '1.5px solid #e5e7eb',
                    fontSize: '1rem',
                    fontWeight: 500,
                    background: '#f8fafc',
                    outline: 'none',
                    cursor: 'pointer',
                    marginBottom: 12,
                  }}
                >
                  <option value="Email">Email Template</option>
                  <option value="SMS">SMS Template</option>
                  <option value="In-App">In-App Template</option>
                </select>
              </div>
              <div style={{ background: settings.contentBgColor, borderRadius: 10, padding: 0 }}>
                {blocks.map((block: Block) => (
                  <SortableBlock
                    key={block.id}
                    {...block}
                    onEdit={handleEditBlock}
                    templateType={templateType}
                    preview={false}
                    onDelete={handleDeleteBlock}
                  />
                ))}
              </div>
            </div>
          </SortableContext>
        </DndContext>
      </div>

      {/* Right: Template Settings and Status */}
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 12 }}>
        <div style={{ display: 'flex', flexDirection: 'row', gap: 10, width: '100%', justifyContent: 'flex-end', marginBottom: 8 }}>
          <button
            onClick={handleCancelRequest}
            style={{
              background: '#f3f4f6',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: 8,
              padding: '10px 18px',
              fontWeight: 600,
              fontSize: '1rem',
              cursor: 'pointer',
              transition: 'background 0.2s',
            }}
            onMouseOver={(e) => e.currentTarget.style.background = '#e5e7eb'}
            onMouseOut={(e) => e.currentTarget.style.background = '#f3f4f6'}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className={styles.saveButton}
            style={{
              background: '#6366f1',
              color: '#fff',
              border: 'none',
              borderRadius: 8,
              padding: '10px 24px',
              fontWeight: 700,
              fontSize: '1rem',
              boxShadow: '0 2px 8px rgba(99,102,241,0.12)',
              cursor: 'pointer',
              transition: 'background 0.2s',
            }}
            disabled={!templateName.trim()}
            title={!templateName.trim() ? 'Enter a template name to save' : template ? 'Update template' : 'Save template'}
          >
            {template ? 'Update Template' : 'Save Template'}
          </button>
        </div>
        <button
          style={{
            marginBottom: 10,
            background: '#6366f1',
            color: '#fff',
            border: 'none',
            borderRadius: 8,
            padding: '10px 24px',
            fontWeight: 600,
            fontSize: '1rem',
            cursor: 'pointer',
            boxShadow: '0 2px 8px rgba(99,102,241,0.10)',
            alignSelf: 'stretch',
          }}
          onClick={() => setShowPreview(true)}
        >
          Preview
        </button>
        <div style={{ width: '100%', marginBottom: 8, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
          <label style={{ fontWeight: 500, color: '#374151', marginBottom: 4 }}>Status</label>
          <select
            value={settings.status}
            onChange={e => setSettings({ ...settings, status: e.target.value })}
            style={{
              width: 140,
              padding: '8px 12px',
              borderRadius: 6,
              border: '1.5px solid #e5e7eb',
              fontSize: '1rem',
              fontWeight: 500,
              background: '#f8fafc',
              outline: 'none',
              cursor: 'pointer',
            }}
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="draft">Draft</option>
          </select>
        </div>
        <TemplateSettings settings={settings} onChange={setSettings} />
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0,0,0,0.45)',
            zIndex: 2000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => setShowPreview(false)}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: 18,
              boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
              padding: 0,
              maxWidth: 800,
              width: '90vw',
              minHeight: 400,
              overflow: 'auto',
              position: 'relative',
            }}
            onClick={e => e.stopPropagation()}
          >
            <div style={{ padding: 32, background: settings.bgColor, borderRadius: '18px 18px 0 0' }}>
              <div style={{ background: settings.contentBgColor, borderRadius: 10, padding: 0 }}>
                {blocks.map((block: Block) => (
                  <SortableBlock key={block.id} {...block} onEdit={() => {}} templateType={templateType} preview={true} onDelete={() => {}} />
                ))}
              </div>
            </div>
            <button
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: '#f3f4f6',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: 8,
                padding: '8px 18px',
                fontWeight: 600,
                fontSize: '1rem',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onClick={() => setShowPreview(false)}
            >
              Close Preview
            </button>
          </div>
        </div>
      )}

      {/* Delete Block Confirmation Dialog */}
      {deleteDialog.open && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 2001,
          }}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: 12,
              padding: '32px',
              maxWidth: '340px',
              width: '90%',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
              textAlign: 'center',
            }}
          >
            <h3 style={{ margin: '0 0 16px 0', fontSize: '1.15rem', fontWeight: 600, color: '#111827' }}>
              Remove Content Block?
            </h3>
            <p style={{ margin: '0 0 24px 0', color: '#6b7280', lineHeight: 1.5 }}>
              Are you sure you want to delete this content block?
            </p>
            <div style={{ display: 'flex', gap: 12, justifyContent: 'center' }}>
              <button
                onClick={cancelDeleteBlock}
                style={{
                  background: '#f3f4f6',
                  color: '#374151',
                  border: '1px solid #d1d5db',
                  borderRadius: 6,
                  padding: '10px 20px',
                  fontWeight: 500,
                  fontSize: '0.95rem',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                }}
                onMouseOver={(e) => e.currentTarget.style.background = '#e5e7eb'}
                onMouseOut={(e) => e.currentTarget.style.background = '#f3f4f6'}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteBlock}
                style={{
                  background: '#dc2626',
                  color: '#fff',
                  border: 'none',
                  borderRadius: 6,
                  padding: '10px 20px',
                  fontWeight: 500,
                  fontSize: '0.95rem',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                }}
                onMouseOver={(e) => e.currentTarget.style.background = '#b91c1c'}
                onMouseOut={(e) => e.currentTarget.style.background = '#dc2626'}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateEditor; 