import React, { useState, useEffect } from 'react';
import {
  Box,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Chip,
  styled
} from '@mui/material';
import {
  Add as AddIcon,
  Sort as SortIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { Golfer, GolferFormData, SortOption, Filters, TabPanelProps } from './types';
import GolferListItem from './GolferListItem';
import GolferFormModal from './GolferFormModal';
import GolferMetricsCard from './GolferMetricsCard';
import { mockDataService } from './mockDataService';

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} role="tabpanel">
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

export const Roster: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [golfers, setGolfers] = useState<Golfer[]>([]);
  const [selectedGolfer, setSelectedGolfer] = useState<Golfer | null>(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [sortAnchorEl, setSortAnchorEl] = useState<null | HTMLElement>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [currentSort, setCurrentSort] = useState<SortOption | null>(null);
  const [filters, setFilters] = useState<Filters>({
    stars: [],
    membership: [],
    playStatus: []
  });
  const [selectedGolfers, setSelectedGolfers] = useState<Set<string>>(new Set());

  useEffect(() => {
    const loadedGolfers = mockDataService.getGolfers();
    setGolfers(loadedGolfers);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDateToYYYYMMDD = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
      // Try to parse the date in various formats
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        // If the date is in MM-DD-YYYY format, try to parse it
        const parts = dateStr.split('-');
        if (parts.length === 3) {
          const [month, day, year] = parts;
          const newDate = new Date(`${year}-${month}-${day}`);
          if (!isNaN(newDate.getTime())) {
            return newDate.toISOString().split('T')[0];
          }
        }
        return '';
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleEditGolfer = (golfer: Golfer) => {
    // Create a clean copy of the golfer data
    const cleanGolfer = {
      ...golfer,
      name: golfer.name || '',
      phone: golfer.phone || '',
      email: golfer.email || '',
      address: golfer.address || '',
      albatrossStarScore: golfer.albatrossStarScore || '',
      nps: golfer.nps || '',
      events: golfer.events || '',
      foodDrink: golfer.foodDrink || '',
      student: golfer.student || '',
      lastPlayDate: formatDateToYYYYMMDD(golfer.lastPlayDate) || '',
      upcomingPlayDate: formatDateToYYYYMMDD(golfer.upcomingPlayDate) || '',
    };

    const nameParts = cleanGolfer.name.split(' ');
    const formData: GolferFormData = {
      id: cleanGolfer.id,
      firstName: nameParts[0] || '',
      lastName: nameParts.slice(1).join(' ') || '',
      phone: cleanGolfer.phone,
      email: cleanGolfer.email,
      address: cleanGolfer.address,
      stars: cleanGolfer.stars,
      isMember: cleanGolfer.isMember,
      upcomingPlay: cleanGolfer.upcomingPlayDate,
      lastSeen: cleanGolfer.lastPlayDate,
      albatrossStarScore: cleanGolfer.albatrossStarScore,
      nps: cleanGolfer.nps,
      events: cleanGolfer.events,
      foodDrink: cleanGolfer.foodDrink,
      student: cleanGolfer.student,
      avatar: cleanGolfer.avatar,
      avatarColor: cleanGolfer.avatarColor
    };

    setSelectedGolfer(cleanGolfer);
    setFormModalOpen(true);
  };

  const handleAddGolfer = () => {
    setSelectedGolfer(null);
    setFormModalOpen(true);
  };

  const handleFormSubmit = (data: GolferFormData) => {
    try {
      console.log('Form Data Received:', data);
      console.log('Selected Golfer:', selectedGolfer);

      if (selectedGolfer) {
        // Update existing golfer - preserve ALL existing data
        const updatedGolfer: Golfer = {
          ...selectedGolfer, // Keep ALL existing fields
          // Name fields
          name: `${data.firstName} ${data.lastName}`.trim(),
          // Contact information
          phone: data.phone,
          email: data.email,
          // Status
          stars: data.stars ?? selectedGolfer.stars,
          isMember: data.isMember ?? selectedGolfer.isMember,
          // Dates
          lastPlayDate: data.lastSeen,
          upcomingPlayDate: data.upcomingPlay,
          // Text fields
          address: data.address,
          albatrossStarScore: data.albatrossStarScore,
          nps: data.nps,
          events: data.events,
          foodDrink: data.foodDrink,
          student: data.student,
          // Avatar
          avatar: data.avatar ?? selectedGolfer.avatar,
          avatarColor: data.avatarColor ?? selectedGolfer.avatarColor
        };

        console.log('Updating Golfer - Updated Data:', updatedGolfer);

        // Update mock service first
        mockDataService.updateGolfer(updatedGolfer);
        
        // Then update local state
        setGolfers(prevGolfers => {
          const newGolfers = prevGolfers.map(g => 
            g.id === updatedGolfer.id ? updatedGolfer : g
          );
          console.log('New Golfers State:', newGolfers);
          return newGolfers;
        });

        // Log the current state after update
        console.log('Current Mock Service Data:', mockDataService.getGolfers());
      } else {
        // Add new golfer
        const newGolfer: Golfer = {
          id: Math.random().toString(),
          // Name fields
          name: `${data.firstName} ${data.lastName}`.trim(),
          // Contact information
          phone: data.phone,
          email: data.email,
          // Status
          stars: data.stars,
          isMember: data.isMember,
          // Dates
          lastPlayDate: data.lastSeen,
          upcomingPlayDate: data.upcomingPlay,
          // Text fields
          address: data.address,
          albatrossStarScore: data.albatrossStarScore,
          nps: data.nps,
          events: data.events,
          foodDrink: data.foodDrink,
          student: data.student,
          // Avatar
          avatar: data.avatar,
          avatarColor: data.avatarColor
        };

        console.log('Adding New Golfer:', newGolfer);

        // Add to mock service first
        mockDataService.addGolfer(newGolfer);
        
        // Then update local state
        setGolfers(prevGolfers => {
          const newGolfers = [...prevGolfers, newGolfer];
          console.log('New Golfers State After Add:', newGolfers);
          return newGolfers;
        });

        // Log the current state after add
        console.log('Current Mock Service Data After Add:', mockDataService.getGolfers());
      }

      // Close the modal and reset selection
      setFormModalOpen(false);
      setSelectedGolfer(null);
    } catch (error) {
      console.error('Error saving golfer data:', error);
    }
  };

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterChange = (category: keyof Filters, value: Filters[keyof Filters][number]) => {
    setFilters(prev => {
      const currentFilters = prev[category] as Array<Filters[typeof category][number]>;
      const hasValue = currentFilters.includes(value);
      return {
        ...prev,
        [category]: hasValue 
          ? currentFilters.filter(v => v !== value)
          : [...currentFilters, value]
      };
    });
  };

  const handleSelectGolfer = (golferId: string) => {
    setSelectedGolfers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(golferId)) {
        newSelected.delete(golferId);
      } else {
        newSelected.add(golferId);
      }
      return newSelected;
    });
  };

  const filteredGolfers = golfers.filter(golfer => {
    if (filters.stars.length && !filters.stars.includes(golfer.stars)) return false;
    if (filters.membership.length) {
      if (filters.membership.includes('member') && !golfer.isMember) return false;
      if (filters.membership.includes('non-member') && golfer.isMember) return false;
    }
    if (filters.playStatus.length) {
      const hasUpcoming = new Date(golfer.upcomingPlayDate) > new Date();
      const hasRecent = new Date(golfer.lastPlayDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      if (filters.playStatus.includes('upcoming') && !hasUpcoming) return false;
      if (filters.playStatus.includes('recent') && !hasRecent) return false;
    }
    return true;
  });

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="All Golfers" />
          <Tab label="Members" />
          <Tab label="Non-Members" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">All Golfers</Typography>
          <Box>
            <IconButton onClick={handleFilterClick}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={(e) => setSortAnchorEl(e.currentTarget)}>
              <SortIcon />
            </IconButton>
            <IconButton onClick={handleAddGolfer}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Active Filters Display */}
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
          {filters.stars.length > 0 && (
            <Chip
              label={`${filters.stars.join(', ')} Stars`}
              onDelete={() => filters.stars.forEach(star => handleFilterChange('stars', star))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.membership.length > 0 && (
            <Chip
              label={filters.membership.join(', ')}
              onDelete={() => filters.membership.forEach(member => handleFilterChange('membership', member))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.playStatus.length > 0 && (
            <Chip
              label={filters.playStatus.join(', ')}
              onDelete={() => filters.playStatus.forEach(status => handleFilterChange('playStatus', status))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {currentSort && (
            <Chip
              label={`Sorted by: ${currentSort}`}
              onDelete={() => setCurrentSort(null)}
              color="secondary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.stars.length === 0 && filters.membership.length === 0 && 
           filters.playStatus.length === 0 && !currentSort && (
            <Typography variant="body2" color="text.secondary">
              No filters applied
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 3, mb: 3 }}>
          <GolferMetricsCard
            title="Total Golfers"
            value={golfers.length}
            change={5}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Members"
            value={golfers.filter(g => g.isMember).length}
            change={3}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Non-Members"
            value={golfers.filter(g => !g.isMember).length}
            change={2}
            period="vs last month"
          />
        </Box>

        <Box sx={{ bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          {filteredGolfers.map(golfer => (
            <GolferListItem
              key={golfer.id}
              golfer={golfer}
              onEdit={() => handleEditGolfer(golfer)}
              selected={selectedGolfers.has(golfer.id.toString())}
              onSelect={() => handleSelectGolfer(golfer.id.toString())}
            />
          ))}
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6">Members</Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6">Non-Members</Typography>
      </TabPanel>

      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={() => handleFilterChange('stars', 5)}>5 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 4)}>4 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 3)}>3 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'member')}>Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'non-member')}>Non-Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'upcoming')}>Upcoming</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'recent')}>Recent</MenuItem>
      </Menu>

      <GolferFormModal
        open={formModalOpen}
        onClose={() => {
          setFormModalOpen(false);
          setSelectedGolfer(null);
        }}
        onSubmit={handleFormSubmit}
        golfer={selectedGolfer ? {
          id: selectedGolfer.id,
          firstName: selectedGolfer.name.split(' ')[0] || '',
          lastName: selectedGolfer.name.split(' ').slice(1).join(' ') || '',
          phone: selectedGolfer.phone || '',
          email: selectedGolfer.email || '',
          address: selectedGolfer.address || '',
          stars: selectedGolfer.stars,
          isMember: selectedGolfer.isMember,
          upcomingPlay: selectedGolfer.upcomingPlayDate || '',
          lastSeen: selectedGolfer.lastPlayDate || '',
          albatrossStarScore: selectedGolfer.albatrossStarScore || '',
          nps: selectedGolfer.nps || '',
          events: selectedGolfer.events || '',
          foodDrink: selectedGolfer.foodDrink || '',
          student: selectedGolfer.student || '',
          avatar: selectedGolfer.avatar,
          avatarColor: selectedGolfer.avatarColor
        } : null}
      />
    </Box>
  );
}; 