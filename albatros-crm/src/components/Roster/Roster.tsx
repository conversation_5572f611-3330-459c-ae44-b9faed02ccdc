import React, { useState, useEffect } from 'react';
import {
  Box,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Chip,
  Alert,
  CircularProgress,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Sort as SortIcon,
  FilterList as FilterIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Golfer, GolferFormData, SortOption, Filters, TabPanelProps } from './types';
import GolferListItem from './GolferListItem';
import GolferFormModal from './GolferFormModal';
import GolferMetricsCard from './GolferMetricsCard';
import { useGolfers, useCreateGolfer, useUpdateGolfer, useDeleteGolfer } from '../../api/hooks/useGolfers';
import { useXanoAuth } from '../../api/hooks/useXanoAuth';
import {
  convertXanoToComponentGolfer,
  convertComponentToXanoCreate,
  convertComponentToXanoUpdate,
  calculateGolferMetrics
} from '../../utils/golferDataConverter';
import config from '../../config';

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} role="tabpanel">
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

export const Roster: React.FC = () => {
  const { isAuthenticated } = useXanoAuth();
  const golf_course_id = parseInt(config.courseId);

  const [tabValue, setTabValue] = useState(0);
  const [selectedGolfer, setSelectedGolfer] = useState<Golfer | null>(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [sortAnchorEl, setSortAnchorEl] = useState<null | HTMLElement>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [currentSort, setCurrentSort] = useState<SortOption | null>(null);
  const [filters, setFilters] = useState<Filters>({
    stars: [],
    membership: [],
    playStatus: []
  });
  const [selectedGolfers, setSelectedGolfers] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  // XANO API hooks
  const {
    data: xanoGolfers = [],
    isLoading,
    error,
    refetch
  } = useGolfers({ golf_course_id });

  const createGolferMutation = useCreateGolfer();
  const updateGolferMutation = useUpdateGolfer();
  const deleteGolferMutation = useDeleteGolfer();

  // Convert XANO data to component format
  const golfers: Golfer[] = xanoGolfers.map(convertXanoToComponentGolfer);

  // Calculate metrics from XANO data
  const metrics = calculateGolferMetrics(xanoGolfers);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDateToYYYYMMDD = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
      // Try to parse the date in various formats
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        // If the date is in MM-DD-YYYY format, try to parse it
        const parts = dateStr.split('-');
        if (parts.length === 3) {
          const [month, day, year] = parts;
          const newDate = new Date(`${year}-${month}-${day}`);
          if (!isNaN(newDate.getTime())) {
            return newDate.toISOString().split('T')[0];
          }
        }
        return '';
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleEditGolfer = (golfer: Golfer) => {
    // Create a clean copy of the golfer data
    const cleanGolfer = {
      ...golfer,
      name: golfer.name || '',
      phone: golfer.phone || '',
      email: golfer.email || '',
      address: golfer.address || '',
      albatrossStarScore: golfer.albatrossStarScore || '',
      nps: golfer.nps || '',
      events: golfer.events || '',
      foodDrink: golfer.foodDrink || '',
      student: golfer.student || '',
      lastPlayDate: formatDateToYYYYMMDD(golfer.lastPlayDate) || '',
      upcomingPlayDate: formatDateToYYYYMMDD(golfer.upcomingPlayDate) || '',
    };

    const nameParts = cleanGolfer.name.split(' ');
    const formData: GolferFormData = {
      id: cleanGolfer.id,
      firstName: nameParts[0] || '',
      lastName: nameParts.slice(1).join(' ') || '',
      phone: cleanGolfer.phone,
      email: cleanGolfer.email,
      address: cleanGolfer.address,
      stars: cleanGolfer.stars,
      isMember: cleanGolfer.isMember,
      upcomingPlay: cleanGolfer.upcomingPlayDate,
      lastSeen: cleanGolfer.lastPlayDate,
      albatrossStarScore: cleanGolfer.albatrossStarScore,
      nps: cleanGolfer.nps,
      events: cleanGolfer.events,
      foodDrink: cleanGolfer.foodDrink,
      student: cleanGolfer.student,
      avatar: cleanGolfer.avatar,
      avatarColor: cleanGolfer.avatarColor
    };

    setSelectedGolfer(cleanGolfer);
    setFormModalOpen(true);
  };

  const handleAddGolfer = () => {
    setSelectedGolfer(null);
    setFormModalOpen(true);
  };

  const handleFormSubmit = async (data: GolferFormData) => {
    try {
      console.log('Form Data Received:', data);
      console.log('Selected Golfer:', selectedGolfer);

      if (selectedGolfer) {
        // Update existing golfer
        const updateData = convertComponentToXanoUpdate(data);
        await updateGolferMutation.mutateAsync({
          id: parseInt(selectedGolfer.id),
          data: updateData
        });
      } else {
        // Create new golfer
        const createData = convertComponentToXanoCreate(data, golf_course_id);
        await createGolferMutation.mutateAsync(createData);
      }

      // Close the modal and reset selection
      setFormModalOpen(false);
      setSelectedGolfer(null);
    } catch (error) {
      console.error('Error saving golfer data:', error);
    }
  };

  const handleDeleteGolfer = async (golfer: Golfer) => {
    if (window.confirm(`Are you sure you want to delete ${golfer.name}?`)) {
      try {
        await deleteGolferMutation.mutateAsync(parseInt(golfer.id));
      } catch (error) {
        console.error('Failed to delete golfer:', error);
      }
    }
  };

  // Show loading indicator for mutations
  const isSaving = createGolferMutation.isPending || updateGolferMutation.isPending;
  const isDeleting = deleteGolferMutation.isPending;

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterChange = (category: keyof Filters, value: Filters[keyof Filters][number]) => {
    setFilters(prev => {
      const currentFilters = prev[category] as Array<Filters[typeof category][number]>;
      const hasValue = currentFilters.includes(value);
      return {
        ...prev,
        [category]: hasValue 
          ? currentFilters.filter(v => v !== value)
          : [...currentFilters, value]
      };
    });
  };

  const handleSelectGolfer = (golferId: string) => {
    setSelectedGolfers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(golferId)) {
        newSelected.delete(golferId);
      } else {
        newSelected.add(golferId);
      }
      return newSelected;
    });
  };

  // Show authentication required state
  if (!isAuthenticated) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please log in to access the roster.
        </Alert>
      </Box>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading golfers...
        </Typography>
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to load golfers: {error.message}
        </Alert>
        <Typography variant="body2">
          <button onClick={() => refetch()}>Try Again</button>
        </Typography>
      </Box>
    );
  }

  const filteredGolfers = golfers.filter(golfer => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesName = golfer.name.toLowerCase().includes(searchLower);
      const matchesEmail = golfer.email.toLowerCase().includes(searchLower);
      const matchesPhone = golfer.phone.includes(searchTerm);
      if (!matchesName && !matchesEmail && !matchesPhone) return false;
    }

    // Star filter
    if (filters.stars.length && !filters.stars.includes(golfer.stars)) return false;

    // Membership filter
    if (filters.membership.length) {
      if (filters.membership.includes('member') && !golfer.isMember) return false;
      if (filters.membership.includes('non-member') && golfer.isMember) return false;
    }

    // Play status filter
    if (filters.playStatus.length) {
      const hasUpcoming = new Date(golfer.upcomingPlayDate) > new Date();
      const hasRecent = new Date(golfer.lastPlayDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      if (filters.playStatus.includes('upcoming') && !hasUpcoming) return false;
      if (filters.playStatus.includes('recent') && !hasRecent) return false;
    }

    return true;
  });

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="All Golfers" />
          <Tab label="Members" />
          <Tab label="Non-Members" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">All Golfers ({filteredGolfers.length})</Typography>
          <Box>
            <IconButton onClick={handleFilterClick}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={(e) => setSortAnchorEl(e.currentTarget)}>
              <SortIcon />
            </IconButton>
            <IconButton onClick={handleAddGolfer} disabled={isSaving}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Search Field */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Search golfers by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 400 }}
          />
        </Box>

        {/* Active Filters Display */}
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
          {filters.stars.length > 0 && (
            <Chip
              label={`${filters.stars.join(', ')} Stars`}
              onDelete={() => filters.stars.forEach(star => handleFilterChange('stars', star))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.membership.length > 0 && (
            <Chip
              label={filters.membership.join(', ')}
              onDelete={() => filters.membership.forEach(member => handleFilterChange('membership', member))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.playStatus.length > 0 && (
            <Chip
              label={filters.playStatus.join(', ')}
              onDelete={() => filters.playStatus.forEach(status => handleFilterChange('playStatus', status))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {currentSort && (
            <Chip
              label={`Sorted by: ${currentSort}`}
              onDelete={() => setCurrentSort(null)}
              color="secondary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.stars.length === 0 && filters.membership.length === 0 && 
           filters.playStatus.length === 0 && !currentSort && (
            <Typography variant="body2" color="text.secondary">
              No filters applied
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 3, mb: 3 }}>
          <GolferMetricsCard
            title="Total Golfers"
            value={metrics.total}
            change={5}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Members"
            value={metrics.members}
            change={3}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Non-Members"
            value={metrics.nonMembers}
            change={2}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Recently Active"
            value={metrics.recentlyActive}
            change={8}
            period="last 30 days"
          />
          <GolferMetricsCard
            title="Upcoming Activity"
            value={metrics.upcomingActivity}
            change={12}
            period="next 30 days"
          />
          <GolferMetricsCard
            title="Average Rating"
            value={`${metrics.averageStars} ⭐`}
            change={0.2}
            period="vs last month"
          />
        </Box>

        <Box sx={{ bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1, position: 'relative' }}>
          {isDeleting && (
            <Box sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255,255,255,0.7)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1
            }}>
              <CircularProgress />
            </Box>
          )}
          {filteredGolfers.length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                {searchTerm ? 'No golfers found matching your search.' : 'No golfers found.'}
              </Typography>
            </Box>
          ) : (
            filteredGolfers.map(golfer => (
              <GolferListItem
                key={golfer.id}
                golfer={golfer}
                onEdit={() => handleEditGolfer(golfer)}
                onDelete={handleDeleteGolfer}
                selected={selectedGolfers.has(golfer.id.toString())}
                onSelect={() => handleSelectGolfer(golfer.id.toString())}
              />
            ))
          )}
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Members ({golfers.filter(g => g.isMember).length})</Typography>
          <Box>
            <IconButton onClick={handleFilterClick}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={(e) => setSortAnchorEl(e.currentTarget)}>
              <SortIcon />
            </IconButton>
            <IconButton onClick={handleAddGolfer} disabled={isSaving}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          {golfers.filter(g => g.isMember).map(golfer => (
            <GolferListItem
              key={golfer.id}
              golfer={golfer}
              onEdit={() => handleEditGolfer(golfer)}
              onDelete={handleDeleteGolfer}
              selected={selectedGolfers.has(golfer.id.toString())}
              onSelect={() => handleSelectGolfer(golfer.id.toString())}
            />
          ))}
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Non-Members ({golfers.filter(g => !g.isMember).length})</Typography>
          <Box>
            <IconButton onClick={handleFilterClick}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={(e) => setSortAnchorEl(e.currentTarget)}>
              <SortIcon />
            </IconButton>
            <IconButton onClick={handleAddGolfer} disabled={isSaving}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          {golfers.filter(g => !g.isMember).map(golfer => (
            <GolferListItem
              key={golfer.id}
              golfer={golfer}
              onEdit={() => handleEditGolfer(golfer)}
              onDelete={handleDeleteGolfer}
              selected={selectedGolfers.has(golfer.id.toString())}
              onSelect={() => handleSelectGolfer(golfer.id.toString())}
            />
          ))}
        </Box>
      </TabPanel>

      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={() => handleFilterChange('stars', 5)}>5 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 4)}>4 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 3)}>3 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'member')}>Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'non-member')}>Non-Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'upcoming')}>Upcoming</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'recent')}>Recent</MenuItem>
      </Menu>

      <GolferFormModal
        open={formModalOpen}
        onClose={() => {
          if (!isSaving) {
            setFormModalOpen(false);
            setSelectedGolfer(null);
          }
        }}
        onSubmit={handleFormSubmit}
        golfer={selectedGolfer ? {
          id: selectedGolfer.id,
          firstName: selectedGolfer.name.split(' ')[0] || '',
          lastName: selectedGolfer.name.split(' ').slice(1).join(' ') || '',
          phone: selectedGolfer.phone || '',
          email: selectedGolfer.email || '',
          address: selectedGolfer.address || '',
          stars: selectedGolfer.stars,
          isMember: selectedGolfer.isMember,
          upcomingPlay: selectedGolfer.upcomingPlayDate || '',
          lastSeen: selectedGolfer.lastPlayDate || '',
          albatrossStarScore: selectedGolfer.albatrossStarScore || '',
          nps: selectedGolfer.nps || '',
          events: selectedGolfer.events || '',
          foodDrink: selectedGolfer.foodDrink || '',
          student: selectedGolfer.student || '',
          avatar: selectedGolfer.avatar,
          avatarColor: selectedGolfer.avatarColor
        } : null}
      />
    </Box>
  );
}; 