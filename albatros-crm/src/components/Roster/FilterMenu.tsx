import React from 'react';
import {
  Menu,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
  Divider,
  Box,
} from '@mui/material';
import { FilterMenuProps, Filters } from './types';

const FilterMenu: React.FC<FilterMenuProps> = ({ 
  anchorEl, 
  open, 
  onClose, 
  filters, 
  onFilterChange 
}) => {
  const handleFilterChange = (category: keyof Filters, value: Filters[keyof Filters][number]): void => {
    onFilterChange(category, value);
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <Box sx={{ p: 2, minWidth: 250 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Stars</Typography>
        <FormGroup>
          {[1, 2, 3, 4, 5].map((star) => (
            <FormControlLabel
              key={star}
              control={
                <Checkbox
                  checked={filters.stars.includes(star)}
                  onChange={() => handleFilterChange('stars', star)}
                  size="small"
                />
              }
              label={`${star} Star${star === 1 ? '' : 's'}`}
            />
          ))}
        </FormGroup>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>Membership</Typography>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.membership.includes('member')}
                onChange={() => handleFilterChange('membership', 'member')}
                size="small"
              />
            }
            label="Members"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.membership.includes('non-member')}
                onChange={() => handleFilterChange('membership', 'non-member')}
                size="small"
              />
            }
            label="Non-Members"
          />
        </FormGroup>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>Play Status</Typography>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.playStatus.includes('upcoming')}
                onChange={() => handleFilterChange('playStatus', 'upcoming')}
                size="small"
              />
            }
            label="Has Upcoming Game"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.playStatus.includes('recent')}
                onChange={() => handleFilterChange('playStatus', 'recent')}
                size="small"
              />
            }
            label="Played Recently"
          />
        </FormGroup>
      </Box>
    </Menu>
  );
};

export default FilterMenu; 