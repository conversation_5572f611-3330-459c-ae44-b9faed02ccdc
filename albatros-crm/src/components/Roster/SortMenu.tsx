import React from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  ArrowUpward as AscIcon,
  ArrowDownward as DescIcon,
} from '@mui/icons-material';
import { SortMenuProps, SortOption } from './types';

const sortOptions: SortOption[] = [
  { id: 'name-asc', label: 'Name (A-Z)', value: 'name', direction: 'asc' },
  { id: 'name-desc', label: 'Name (Z-A)', value: 'name', direction: 'desc' },
  { id: 'stars-desc', label: 'Stars (High to Low)', value: 'stars', direction: 'desc' },
  { id: 'stars-asc', label: 'Stars (Low to High)', value: 'stars', direction: 'asc' },
  { id: 'lastPlayDate-desc', label: 'Last Play Date (Recent First)', value: 'lastPlayDate', direction: 'desc' },
  { id: 'lastPlayDate-asc', label: 'Last Play Date (Oldest First)', value: 'lastPlayDate', direction: 'asc' },
];

const SortMenu: React.FC<SortMenuProps> = ({ 
  anchorEl, 
  open, 
  onClose, 
  currentSort, 
  onSortChange 
}) => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      {sortOptions.map((option) => (
        <MenuItem
          key={option.id}
          onClick={() => {
            onSortChange(option);
            onClose();
          }}
          selected={currentSort?.id === option.id}
        >
          <ListItemIcon>
            {option.direction === 'asc' ? <AscIcon fontSize="small" /> : <DescIcon fontSize="small" />}
          </ListItemIcon>
          <ListItemText primary={option.label} />
        </MenuItem>
      ))}
    </Menu>
  );
};

export default SortMenu; 