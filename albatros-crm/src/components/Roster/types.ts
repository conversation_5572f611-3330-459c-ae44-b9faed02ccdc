import { ReactNode } from 'react';

export interface Golfer {
  id: string;
  name: string;
  phone: string;
  email: string;
  stars: number;
  isMember: boolean;
  lastPlayDate: string;
  upcomingPlayDate: string;
  avatar?: string;
  avatarColor?: string;
  initials?: string;
  address?: string;
  albatrossStarScore?: string;
  nps?: string;
  events?: string;
  foodDrink?: string;
  student?: string;
}

export interface GolferListItemProps {
  golfer: Golfer;
  onEdit: (golfer: <PERSON><PERSON>) => void;
  onDelete?: (golfer: Golfer) => void;
  selected?: boolean;
  onSelect?: () => void;
}

export interface GolferFormData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  stars: number;
  isMember: boolean;
  upcomingPlay: string;
  lastSeen: string;
  albatrossStarScore: string;
  nps: string;
  events: string;
  foodDrink: string;
  student: string;
  avatar?: string;
  avatarColor?: string;
}

export interface GolferFormModalProps {
  open: boolean;
  onClose: () => void;
  golfer: GolferFormData | null;
  onSubmit: (data: GolferFormData) => void;
  mode?: 'add' | 'edit';
}

export interface GolferMetricsCardProps {
  icon: ReactNode;
  label: string;
  value: string | number;
  change?: number;
  bgColor?: string;
}

export interface SortOption {
  id: string;
  label: string;
  value: 'name' | 'stars' | 'lastPlayDate';
  direction: 'asc' | 'desc';
}

export interface SortMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  currentSort: SortOption | null;
  onSortChange: (option: SortOption) => void;
}

export type FilterValue = number | 'member' | 'non-member' | 'upcoming' | 'recent';

export interface Filters {
  stars: number[];
  membership: Array<'member' | 'non-member'>;
  playStatus: Array<'upcoming' | 'recent'>;
}

export interface FilterMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  filters: Filters;
  onFilterChange: (category: keyof Filters, value: Filters[keyof Filters][number]) => void;
}

export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
} 