import React from 'react';
import { Box, Typography, styled, Checkbox, Avatar } from '@mui/material';
import { Star as StarIcon } from '@mui/icons-material';

const ListItemContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: '40px 80px 1fr 1fr 1fr 1fr 1fr',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  cursor: 'pointer',
  alignItems: 'center',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const AvatarContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  justifyContent: 'center',
}));

const CrownIcon = styled('div')({
  position: 'absolute',
  top: -8,
  left: 0,
  fontSize: '20px',
});

const StarsContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '2px',
});

const StarIconStyled = styled(StarIcon)(({ theme }) => ({
  color: theme.palette.success.main,
  fontSize: '20px',
}));

const CenteredTypography = styled(Typography)({
  textAlign: 'center',
});

const NameTypography = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
}));

interface GolferListItemProps {
  golfer: {
    id: string;
    name: string;
    phone: string;
    stars: number;
    lastPlayDate: string;
    upcomingPlayDate: string;
    avatar?: string;
    avatarColor?: string;
    initials?: string;
    isMember?: boolean;
  };
  onEdit: () => void;
  selected: boolean;
  onSelect: () => void;
}

const GolferListItem: React.FC<GolferListItemProps> = ({ 
  golfer, 
  onEdit, 
  selected,
  onSelect 
}) => {
  const renderStars = () => {
    return (
      <StarsContainer>
        <StarIconStyled />
        <Typography variant="body2" color="text.secondary">
          x{golfer.stars}
        </Typography>
      </StarsContainer>
    );
  };

  const handleClick = (e: React.MouseEvent) => {
    // If clicking the checkbox, don't trigger edit
    if ((e.target as HTMLElement).closest('.MuiCheckbox-root')) {
      e.stopPropagation();
      onSelect();
      return;
    }
    onEdit();
  };

  return (
    <ListItemContainer onClick={handleClick}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Checkbox
          checked={selected}
          onChange={(e) => {
            e.stopPropagation();
            onSelect();
          }}
          sx={{ p: 0 }}
        />
      </Box>
      <AvatarContainer>
        {golfer.isMember && <CrownIcon>👑</CrownIcon>}
        <Avatar
          src={golfer.avatar}
          sx={{
            width: 40,
            height: 40,
            bgcolor: golfer.avatarColor || 'primary.main',
            fontSize: '0.875rem',
          }}
        >
          {!golfer.avatar && (golfer.initials || golfer.name.split(' ').map(n => n[0]).join(''))}
        </Avatar>
      </AvatarContainer>
      <NameTypography>{golfer.name}</NameTypography>
      <CenteredTypography variant="body2" color="text.secondary">
        {golfer.phone}
      </CenteredTypography>
      {renderStars()}
      <CenteredTypography variant="body2" color="text.secondary">
        {golfer.lastPlayDate}
      </CenteredTypography>
      <CenteredTypography variant="body2" color="text.secondary">
        {golfer.upcomingPlayDate === 'NONE' ? 'NONE' : golfer.upcomingPlayDate}
      </CenteredTypography>
    </ListItemContainer>
  );
};

export default GolferListItem; 