import { styled } from '@mui/material/styles';
import { Paper, Box, Typography, Chip } from '@mui/material';

export const GolferListItemContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(1),
  '&:hover': { 
    backgroundColor: theme.palette.action.hover 
  },
  cursor: 'pointer',
}));

export const GolferInfoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

export const GolferNameContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
}));

export const MemberChip = styled(Chip)(({ theme }) => ({
  backgroundColor: '#E1F9F5',
  color: '#1ABC9C',
  fontSize: '0.75rem',
}));

export const MetricsCardContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
  display: 'flex',
  alignItems: 'center',
}));

export const IconContainer = styled(Box)<{ bgcolor: string }>(({ theme, bgcolor }) => ({
  width: 48,
  height: 48,
  borderRadius: '50%',
  backgroundColor: bgcolor,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: theme.spacing(2),
}));

export const MetricsContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
}));

export const MetricsLabel = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
}));

export const MetricsValue = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'baseline',
  gap: theme.spacing(1),
})); 