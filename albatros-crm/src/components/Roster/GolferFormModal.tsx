import {
  AddAPhoto as AddPhotoIcon,
  CalendarMonth as CalendarIcon,
  Close as CloseIcon,
  EmojiEvents as EventsIcon,
  Restaurant as FoodIcon,
  Star as StarIcon,
  School as StudentIcon
} from '@mui/icons-material';
import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Rating,
  Stack,
  Typography
} from '@mui/material';
import { styled } from '@mui/material/styles';
import React, { useEffect, useRef, useState } from 'react';
import {
  StyledChip,
  StyledDialog,
  StyledTextField
} from '../../styles/styledComponents';
import { PhoneInput } from '../shared/PhoneInput';
import { GolferFormData } from './types';

const REQUIRED_FIELDS = ['firstName', 'lastName', 'phone', 'email'] as const;
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

interface GolferFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: GolferFormData) => void;
  golfer: GolferFormData | null;
  mode?: 'add' | 'edit';
}

const ImageUploadBox = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: 120,
  height: 120,
  borderRadius: '50%',
  overflow: 'hidden',
  backgroundColor: theme.palette.grey[100],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

const GolferFormModal: React.FC<GolferFormModalProps> = ({ 
  open, 
  onClose, 
  golfer, 
  onSubmit, 
  mode = 'add' 
}) => {
  const [formData, setFormData] = useState<GolferFormData>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    address: '',
    stars: 0,
    isMember: false,
    upcomingPlay: '',
    lastSeen: '',
    albatrossStarScore: '',
    nps: '',
    events: '',
    foodDrink: '',
    student: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imagePreview, setImagePreview] = useState<string | undefined>(undefined);
  const [starsAnchorEl, setStarsAnchorEl] = useState<null | HTMLElement>(null);
  const [membershipAnchorEl, setMembershipAnchorEl] = useState<null | HTMLElement>(null);

  // Determine if we're editing based on whether we have a golfer
  const isEditing = Boolean(golfer);

  useEffect(() => {
    if (golfer) {
      setFormData({
        firstName: golfer.firstName || '',
        lastName: golfer.lastName || '',
        phone: golfer.phone || '',
        email: golfer.email || '',
        address: golfer.address || '',
        stars: golfer.stars || 0,
        isMember: Boolean(golfer.isMember),
        upcomingPlay: golfer.upcomingPlay || '',
        lastSeen: golfer.lastSeen || '',
        albatrossStarScore: golfer.albatrossStarScore || '',
        nps: golfer.nps || '',
        events: golfer.events || '',
        foodDrink: golfer.foodDrink || '',
        student: golfer.student || '',
        avatar: golfer.avatar,
        avatarColor: golfer.avatarColor,
      });
      setImagePreview(golfer.avatar);
    } else {
      setFormData({
        firstName: '',
        lastName: '',
        phone: '',
        email: '',
        address: '',
        stars: 0,
        isMember: false,
        upcomingPlay: '',
        lastSeen: '',
        albatrossStarScore: '',
        nps: '',
        events: '',
        foodDrink: '',
        student: '',
      });
      setImagePreview(undefined);
    }
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [golfer, open]);

  const validateField = (name: keyof GolferFormData, value: string | null): string => {
    // Only validate required fields and fields with values
    if (!REQUIRED_FIELDS.includes(name as typeof REQUIRED_FIELDS[number]) && !value) {
      return ''; // Don't validate optional empty fields
    }

    if (REQUIRED_FIELDS.includes(name as typeof REQUIRED_FIELDS[number]) && (!value || value.trim() === '')) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} is required`;
    }

    if (name === 'email' && value && !EMAIL_REGEX.test(value)) {
      return 'Invalid email address';
    }

    if (name === 'phone' && value) {
      // For international numbers, we'll be more lenient with length
      const digitsOnly = value.replace(/\D/g, '');
      if (digitsOnly.length < 10) {
        return 'Please enter a valid phone number';
      }
      if (digitsOnly.length > 15) {
        return 'Phone number cannot exceed 15 digits';
      }
    }

    // Date validation
    if (name === 'lastSeen' && value) {
      const lastSeenDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (lastSeenDate > today) {
        return 'Last seen date cannot be in the future';
      }
    }

    if (name === 'upcomingPlay' && value) {
      const upcomingPlayDate = new Date(value);
      const lastSeenDate = new Date(formData.lastSeen || '');
      
      if (lastSeenDate && upcomingPlayDate < lastSeenDate) {
        return 'Upcoming play date cannot be before last seen date';
      }
    }

    return '';
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    if (!name) return;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Always validate required fields on change
    if (REQUIRED_FIELDS.includes(name as typeof REQUIRED_FIELDS[number])) {
      setErrors(prev => ({
        ...prev,
        [name]: validateField(name as keyof GolferFormData, value)
      }));
      setTouched(prev => ({
        ...prev,
        [name]: true
      }));
    }
  };

  const handleStarsClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setStarsAnchorEl(event.currentTarget);
  };

  const handleStarsClose = () => {
    setStarsAnchorEl(null);
  };

  const handleStarsSelect = (value: number) => {
    setFormData(prev => ({
      ...prev,
      stars: value
    }));
    handleStarsClose();
  };

  const handleMembershipClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setMembershipAnchorEl(event.currentTarget);
  };

  const handleMembershipClose = () => {
    setMembershipAnchorEl(null);
  };

  const handleMembershipSelect = (isMember: boolean) => {
    setFormData(prev => ({
      ...prev,
      isMember
    }));
    handleMembershipClose();
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    if (!name) return;

    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    setErrors(prev => ({
      ...prev,
      [name]: validateField(name as keyof GolferFormData, value)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const newTouched: Record<string, boolean> = {};
    
    // Only validate required fields
    REQUIRED_FIELDS.forEach(field => {
      newErrors[field] = validateField(field, formData[field]);
      newTouched[field] = true;
    });

    setErrors(newErrors);
    setTouched(newTouched);

    // Check if any required field has an error
    return !Object.values(newErrors).some(error => error !== '');
  };

  const isFormValid = (): boolean => {
    // Check if all required fields are filled and valid
    return REQUIRED_FIELDS.every(field => {
      const value = formData[field];
      
      if (!value || value.trim() === '') {
        return false;
      }

      // Check for field-specific validation errors
      if (errors[field]) {
        return false;
      }

      if (field === 'email') {
        return EMAIL_REGEX.test(value);
      }

      return true;
    });
  };

  const handleSubmit = (): void => {
    setIsSubmitting(true);
    
    if (validateForm()) {
      // Start with the original golfer data if editing
      const submissionData: GolferFormData = {
        // Preserve the ID if we're editing
        id: golfer?.id,
        // Name fields
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        // Contact information
        phone: formData.phone,
        email: formData.email.trim(),
        address: formData.address,
        // Status
        stars: formData.stars,
        isMember: formData.isMember,
        // Dates
        upcomingPlay: formData.upcomingPlay,
        lastSeen: formData.lastSeen,
        // Additional information
        albatrossStarScore: formData.albatrossStarScore,
        nps: formData.nps,
        events: formData.events,
        foodDrink: formData.foodDrink,
        student: formData.student,
        // Avatar
        avatar: formData.avatar,
        avatarColor: formData.avatarColor
      };

      console.log('Submitting form data:', submissionData);
      onSubmit(submissionData);
      onClose();
    }
    setIsSubmitting(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setImagePreview(base64String);
        setFormData(prev => ({
          ...prev,
          avatar: base64String
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handlePhoneChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      phone: value
    }));

    setTouched(prev => ({
      ...prev,
      phone: true
    }));

    // Remove country code and get just the digits
    const numberWithoutCountryCode = value.replace(/^\+1\s?/, '');
    const digitsOnly = numberWithoutCountryCode.replace(/\D/g, '');
    
    setErrors(prev => ({
      ...prev,
      phone: !value ? 'Phone number is required' : 
             digitsOnly.length !== 10 ? 'Please enter a valid 10-digit phone number' : ''
    }));
  };

  return (
    <StyledDialog
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle sx={{ 
        p: 3,
        display: 'flex', 
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          {isEditing ? 'Edit Golfer' : 'Add New Golfer'}
        </Typography>
        <IconButton onClick={onClose} size="small" sx={{ color: 'text.secondary' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Profile Section */}
          <Box sx={{ mb: 4, display: 'flex', alignItems: 'flex-start', gap: 3 }}>
            <Box>
              <ImageUploadBox onClick={triggerImageUpload}>
                {imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="Golfer"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <AddPhotoIcon sx={{ fontSize: 40, color: 'grey.400' }} />
                )}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  accept="image/*"
                  style={{ display: 'none' }}
                />
              </ImageUploadBox>
              <Typography
                variant="caption"
                sx={{
                  display: 'block',
                  textAlign: 'center',
                  mt: 1,
                  color: 'text.secondary'
                }}
              >
                Click to {imagePreview ? 'change' : 'add'} photo
              </Typography>
            </Box>

            <Stack spacing={2} sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <StyledTextField
                fullWidth
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleTextChange}
                onBlur={handleBlur}
                error={touched.firstName && !!errors.firstName}
                helperText={touched.firstName && errors.firstName}
                required
                  size="small"
              />
                <StyledTextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleTextChange}
                onBlur={handleBlur}
                error={touched.lastName && !!errors.lastName}
                helperText={touched.lastName && errors.lastName}
                required
                  size="small"
                />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <StyledChip
                  icon={<StarIcon />}
                  label={`${formData.stars} Star${formData.stars !== 1 ? 's' : ''}`}
                  color="primary"
                  variant="outlined"
                  onClick={handleStarsClick}
                  sx={{ cursor: 'pointer' }}
                />
                <Menu
                  anchorEl={starsAnchorEl}
                  open={Boolean(starsAnchorEl)}
                  onClose={handleStarsClose}
                >
                  {[0, 1, 2, 3, 4, 5].map((stars) => (
                    <MenuItem
                      key={stars}
                      onClick={() => handleStarsSelect(stars)}
                      selected={formData.stars === stars}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Rating value={stars} readOnly size="small" />
                        <Typography>{stars} Star{stars !== 1 ? 's' : ''}</Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Menu>

                <StyledChip
                  label={formData.isMember ? 'Member' : 'Non-Member'}
                  color={formData.isMember ? 'success' : 'default'}
                  variant="outlined"
                  onClick={handleMembershipClick}
                  sx={{ cursor: 'pointer' }}
                />
                <Menu
                  anchorEl={membershipAnchorEl}
                  open={Boolean(membershipAnchorEl)}
                  onClose={handleMembershipClose}
                >
                  <MenuItem
                    onClick={() => handleMembershipSelect(true)}
                    selected={formData.isMember}
                  >
                    Member
                  </MenuItem>
                  <MenuItem
                    onClick={() => handleMembershipSelect(false)}
                    selected={!formData.isMember}
                  >
                    Non-Member
                  </MenuItem>
                </Menu>
              </Box>
            </Stack>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Contact Information */}
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Contact Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3, mb: 4 }}>
            <Box>
              <PhoneInput
                value={formData.phone}
                onChange={handlePhoneChange}
                error={touched.phone && Boolean(errors.phone)}
                helperText={touched.phone ? errors.phone : undefined}
                required
              />
            </Box>
            <Box>
              <StyledTextField
                fullWidth
                label="Email"
                name="email"
                value={formData.email}
                onChange={handleTextChange}
                onBlur={handleBlur}
                error={touched.email && !!errors.email}
                helperText={touched.email && errors.email}
                required
                size="small"
              />
            </Box>
            <Box sx={{ gridColumn: '1 / -1' }}>
              <StyledTextField
                fullWidth
                label="Physical Address"
                name="address"
                value={formData.address}
                onChange={handleTextChange}
                multiline
                rows={2}
                size="small"
                InputLabelProps={{
                  shrink: true
                }}
              />
            </Box>
          </Box>

          {/* Activity Information */}
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Activity Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3 }}>
            <StyledTextField
                fullWidth
                label="Last Seen"
                name="lastSeen"
                type="date"
                value={formData.lastSeen || ''}
                onChange={handleTextChange}
                onBlur={handleBlur}
                error={touched.lastSeen && !!errors.lastSeen}
                helperText={touched.lastSeen && errors.lastSeen}
                size="small"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  startAdornment: <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  inputProps: {
                    max: new Date().toISOString().split('T')[0] // Only allow dates up to today
                  }
                }}
            />
            <StyledTextField
                fullWidth
                label="Upcoming Play"
                name="upcomingPlay"
                type="date"
                value={formData.upcomingPlay || ''}
                onChange={handleTextChange}
                onBlur={handleBlur}
                error={touched.upcomingPlay && !!errors.upcomingPlay}
                helperText={touched.upcomingPlay && errors.upcomingPlay}
                size="small"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  startAdornment: <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  inputProps: {
                    min: new Date().toISOString().split('T')[0] // Only allow dates from today onwards
                  }
                }}
            />
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* History Information */}
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            History & Preferences
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3 }}>
            <StyledTextField
                fullWidth
                label="Albatross Star Score"
                name="albatrossStarScore"
                value={formData.albatrossStarScore}
                onChange={handleTextChange}
              size="small"
              InputProps={{
                startAdornment: <StarIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              />
            <StyledTextField
                fullWidth
                label="NPS"
                name="nps"
                value={formData.nps}
                onChange={handleTextChange}
              size="small"
              />
            <StyledTextField
                fullWidth
                label="Events"
                name="events"
                value={formData.events}
                onChange={handleTextChange}
              size="small"
              InputProps={{
                startAdornment: <EventsIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              />
            <StyledTextField
                fullWidth
              label="Food/Drink Preferences"
                name="foodDrink"
                value={formData.foodDrink}
                onChange={handleTextChange}
              size="small"
              InputProps={{
                startAdornment: <FoodIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              />
            <StyledTextField
                fullWidth
              label="Student Details"
                name="student"
                value={formData.student}
                onChange={handleTextChange}
              size="small"
              InputProps={{
                startAdornment: <StudentIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              />
          </Box>
        </Box>
      </DialogContent>

      <DialogActions 
        sx={{ 
          p: 3,
          pt: 2,
          borderTop: 1,
          borderColor: 'divider',
          gap: 1
        }}
      >
        <Button 
          onClick={onClose}
          variant="outlined"
          sx={{ 
            textTransform: 'none',
            fontWeight: 500,
            minWidth: 100
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || !isFormValid()}
          sx={{ 
            textTransform: 'none',
            fontWeight: 500,
            minWidth: 100
          }}
        >
          {isEditing ? 'Save Changes' : 'Add Golfer'}
        </Button>
      </DialogActions>
    </StyledDialog>
  );
};

export default GolferFormModal; 