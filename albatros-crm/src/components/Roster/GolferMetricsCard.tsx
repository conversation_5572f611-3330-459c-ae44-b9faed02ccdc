import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

interface GolferMetricsCardProps {
  title: string;
  value: number | string;
  change: number;
  period: string;
  icon?: string;
  bgColor?: string;
}

const Card = styled(Box)<{ bgColor?: string }>(({ theme, bgColor }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: bgColor || theme.palette.background.paper,
  boxShadow: theme.shadows[1],
}));

const Title = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(1),
}));

const Value = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  marginBottom: theme.spacing(1),
}));

const Change = styled(Typography)<{ isPositive: boolean }>(({ theme, isPositive }) => ({
  fontSize: '0.875rem',
  color: isPositive ? theme.palette.success.main : theme.palette.error.main,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

const Period = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  marginLeft: theme.spacing(0.5),
}));

const GolferMetricsCard: React.FC<GolferMetricsCardProps> = ({
  title,
  value,
  change,
  period,
  icon,
  bgColor,
}) => {
  return (
    <Card bgColor={bgColor}>
      {icon && <Typography variant="h4">{icon}</Typography>}
      <Title>{title}</Title>
      <Value>{typeof value === 'number' ? value.toLocaleString() : value}</Value>
      <Change isPositive={change >= 0}>
        {change >= 0 ? '+' : ''}{change}%
        <Period>{period}</Period>
      </Change>
    </Card>
  );
};

export default GolferMetricsCard; 