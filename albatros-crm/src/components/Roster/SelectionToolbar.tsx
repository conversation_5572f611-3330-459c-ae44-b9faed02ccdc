import React, { useState } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Typography,
  Checkbox,
  styled,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  NotificationsActive as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';

const ToolbarContainer = styled(Box)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  zIndex: 1100,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(2),
}));

interface SelectionToolbarProps {
  selectedCount: number;
  onSelectAll: () => void;
  allSelected: boolean;
  onSendReminder: (type: string, message: string) => void;
}

const SelectionToolbar: React.FC<SelectionToolbarProps> = ({
  selectedCount,
  onSelectAll,
  allSelected,
  onSendReminder,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [reminderDialogOpen, setReminderDialogOpen] = useState(false);
  const [reminderType, setReminderType] = useState<string>('');
  const [reminderMessage, setReminderMessage] = useState('');

  const handleReminderClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleReminderClose = () => {
    setAnchorEl(null);
  };

  const handleReminderTypeSelect = (type: string) => {
    setReminderType(type);
    setAnchorEl(null);
    setReminderDialogOpen(true);
  };

  const handleSendReminder = () => {
    onSendReminder(reminderType, reminderMessage);
    setReminderDialogOpen(false);
    setReminderMessage('');
  };

  return (
    <>
      <ToolbarContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Checkbox
            checked={allSelected}
            onChange={onSelectAll}
            sx={{ color: 'primary.contrastText', '&.Mui-checked': { color: 'primary.contrastText' } }}
          />
          <Typography>{selectedCount} selected</Typography>
        </Box>
        <Button
          variant="contained"
          color="inherit"
          startIcon={<NotificationsIcon />}
          endIcon={<ArrowDownIcon />}
          onClick={handleReminderClick}
          sx={{ color: 'primary.main', bgcolor: 'primary.contrastText' }}
        >
          Send Reminder
        </Button>
      </ToolbarContainer>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleReminderClose}
      >
        <MenuItem onClick={() => handleReminderTypeSelect('email')}>
          <EmailIcon sx={{ mr: 1 }} /> Email Reminder
        </MenuItem>
        <MenuItem onClick={() => handleReminderTypeSelect('sms')}>
          <SmsIcon sx={{ mr: 1 }} /> SMS Reminder
        </MenuItem>
      </Menu>

      <Dialog
        open={reminderDialogOpen}
        onClose={() => setReminderDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Send {reminderType === 'email' ? 'Email' : 'SMS'} Reminder
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Message"
            fullWidth
            multiline
            rows={4}
            value={reminderMessage}
            onChange={(e) => setReminderMessage(e.target.value)}
            placeholder={`Type your ${reminderType} message here...`}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReminderDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSendReminder} variant="contained" color="primary">
            Send
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SelectionToolbar; 