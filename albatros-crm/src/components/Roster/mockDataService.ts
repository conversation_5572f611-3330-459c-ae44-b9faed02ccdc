import { Golfer } from './types';

const STORAGE_KEY = 'roster_golfers_data';

const initialMockGolfers: <PERSON><PERSON>[] = [
  {
    id: '1',
    name: '<PERSON>',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 5,
    isMember: true,
    lastPlayDate: '2024-03-15',
    upcomingPlayDate: '2024-03-22',
    address: '123 Golf Lane',
    albatrossStarScore: '95',
    nps: '9',
    events: 'Club Championship',
    foodDrink: 'Prefers vegetarian',
    student: 'Advanced',
  },
  {
    id: '2',
    name: '<PERSON>',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 4,
    isMember: true,
    lastPlayDate: '2024-03-10',
    upcomingPlayDate: '2024-03-25',
    address: '456 Fairway Dr',
    albatrossStarScore: '88',
    nps: '8',
    events: 'Ladies League',
    foodDrink: 'Allergic to nuts',
    student: 'Intermediate',
  }
];

class MockDataService {
  private data: Golfer[];

  constructor() {
    try {
      const storedData = localStorage.getItem(STORAGE_KEY);
      this.data = storedData ? JSON.parse(storedData) : initialMockGolfers;
      this.saveToStorage(); // Ensure initial data is stored
    } catch (error) {
      console.error('Error initializing mock data service:', error);
      this.data = initialMockGolfers;
      this.saveToStorage();
    }
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  private validateGolfer(golfer: Golfer): Golfer {
    // Ensure all required fields have at least empty string values
    return {
      ...golfer,
      name: golfer.name || '',
      phone: golfer.phone || '',
      email: golfer.email || '',
      address: golfer.address || '',
      albatrossStarScore: golfer.albatrossStarScore || '',
      nps: golfer.nps || '',
      events: golfer.events || '',
      foodDrink: golfer.foodDrink || '',
      student: golfer.student || '',
      stars: golfer.stars || 0,
      isMember: typeof golfer.isMember === 'boolean' ? golfer.isMember : false,
      lastPlayDate: golfer.lastPlayDate || '',
      upcomingPlayDate: golfer.upcomingPlayDate || '',
    };
  }

  getGolfers(): Golfer[] {
    return this.data.map(golfer => this.validateGolfer(golfer));
  }

  updateGolfer(updatedGolfer: Golfer): void {
    const validatedGolfer = this.validateGolfer(updatedGolfer);
    this.data = this.data.map(golfer => 
      golfer.id === validatedGolfer.id ? validatedGolfer : golfer
    );
    this.saveToStorage();
  }

  addGolfer(newGolfer: Golfer): void {
    const validatedGolfer = this.validateGolfer(newGolfer);
    this.data = [...this.data, validatedGolfer];
    this.saveToStorage();
  }

  updateGolfers(golfers: Golfer[]): void {
    this.data = [...golfers];
    this.saveToStorage();
  }

  resetToInitial(): void {
    this.data = [...initialMockGolfers];
    this.saveToStorage();
  }
}

export const mockDataService = new MockDataService(); 