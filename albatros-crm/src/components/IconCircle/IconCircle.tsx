import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

interface IconCircleProps {
  icon: React.ReactNode;
  bgcolor?: string;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  onClick?: () => void;
  className?: string;
  ariaLabel?: string;
}

const sizeMap = {
  small: 32,
  medium: 48,
  large: 64
};

const IconContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'bgcolor' && prop !== 'size',
})<{ bgcolor: string; size: 'small' | 'medium' | 'large' }>(({ theme, bgcolor, size }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: sizeMap[size],
  height: sizeMap[size],
  borderRadius: '50%',
  backgroundColor: bgcolor,
  color: theme.palette.common.white,
  transition: theme.transitions.create(['transform', 'box-shadow']),
  cursor: 'pointer',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: theme.shadows[2],
  },
  '& svg': {
    fontSize: size === 'small' ? 16 : size === 'medium' ? 24 : 32,
  },
}));

export const IconCircle: React.FC<IconCircleProps> = ({
  icon,
  bgcolor = '#1ABC9C',
  size = 'medium',
  color,
  onClick,
  className,
  ariaLabel
}) => {
  return (
    <IconContainer
      bgcolor={bgcolor}
      size={size}
      onClick={onClick}
      className={className}
      aria-label={ariaLabel}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      sx={{ color }}
    >
      {icon}
    </IconContainer>
  );
}; 