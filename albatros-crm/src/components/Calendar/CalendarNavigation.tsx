import React from 'react';
import { Box, Typography, ToggleButtonGroup, ToggleButton, Button, styled } from '@mui/material';

const NavigationContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3)
}));

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  backgroundColor: theme.palette.primary.light + '14', // 14 is 8% opacity in hex
  '& .MuiToggleButton-root': {
    border: 'none',
    margin: theme.spacing(0, 0.5),
    padding: theme.spacing(0.75, 2),
    color: theme.palette.text.secondary,
    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.primary.main,
      },
    },
    '&:hover': {
      backgroundColor: theme.palette.primary.light + '14', // 14 is 8% opacity in hex
    },
  },
}));

const DateText = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(1)
}));

type ViewMode = 'daily' | 'weekly' | 'monthly';

interface CalendarNavigationProps {
  selectedDate: Date;
  viewMode: ViewMode;
  onViewModeChange: (view: ViewMode) => void;
}

const CalendarNavigation: React.FC<CalendarNavigationProps> = ({ 
  selectedDate, 
  viewMode, 
  onViewModeChange 
}) => {
  const handleViewChange = (
    event: React.MouseEvent<HTMLElement>,
    newView: ViewMode | null
  ): void => {
    if (newView !== null) {
      onViewModeChange(newView);
    }
  };

  const handleTeeSheetClick = () => {
    window.open('/tee-sheet', '_blank');
  };

  return (
    <NavigationContainer>
      <DateText variant="subtitle1">
        {selectedDate.toLocaleDateString('en-US', { 
          month: 'long',
          day: 'numeric',
          year: 'numeric'
        })}
      </DateText>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Button
          variant="outlined"
          color="primary"
          onClick={handleTeeSheetClick}
          sx={{
            borderColor: 'primary.main',
            color: 'primary.main',
            '&:hover': {
              borderColor: 'primary.dark',
              backgroundColor: 'primary.light + 14',
            },
          }}
        >
          Tee Sheet
        </Button>
        <StyledToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewChange}
          aria-label="view mode"
        >
          <ToggleButton value="daily" aria-label="daily view">
            Daily
          </ToggleButton>
          <ToggleButton value="weekly" aria-label="weekly view">
            Weekly
          </ToggleButton>
          <ToggleButton value="monthly" aria-label="monthly view">
            Monthly
          </ToggleButton>
        </StyledToggleButtonGroup>
      </Box>
    </NavigationContainer>
  );
};

export default CalendarNavigation; 