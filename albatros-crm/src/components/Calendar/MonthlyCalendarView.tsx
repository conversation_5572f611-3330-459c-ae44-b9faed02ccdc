import { ChevronLeft, ChevronRight, AttachMoney as MoneyIcon, Person as PersonIcon, AccessTime as TimeIcon } from '@mui/icons-material';
import { Box, BoxProps, Button, Chip, ChipProps, Dialog, DialogContent, DialogTitle, IconButton, List, ListItem, ListItemText, ListProps, styled, Typography } from '@mui/material';
import { alpha } from '@mui/material/styles';
import React, { useCallback, useMemo, useRef, useState } from 'react';

interface DayCellProps {
  intensity?: number;
  isWeekend?: boolean;
  isCurrentMonth?: boolean;
}

const CalendarContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  borderRadius: theme.shape.borderRadius,
  height: '100%',
  minHeight: '800px'
}));

const CalendarGrid = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  flex: 1
}));

const WeeksContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  flex: 1
}));

const WeekRow = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  flex: 1,
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&:last-child': {
    borderBottom: 'none'
  }
}));

const DayCell = styled(Box)<DayCellProps>(({ theme, intensity = 0, isWeekend, isCurrentMonth }) => ({
  position: 'relative',
  padding: theme.spacing(1),
  backgroundColor: isCurrentMonth 
    ? alpha(theme.palette.success.main, intensity * 0.1)
    : alpha(theme.palette.grey[100], 0.5),
  borderRight: `1px solid ${theme.palette.divider}`,
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  '&:last-child': {
    borderRight: 'none'
  },
  '&:hover': {
    backgroundColor: isCurrentMonth 
      ? alpha(theme.palette.success.main, intensity * 0.2)
      : alpha(theme.palette.grey[200], 0.8),
  }
}));

const WeekdayHeader = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
}));

const WeekdayCell = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(1),
  color: theme.palette.text.secondary,
  fontWeight: 500,
  textAlign: 'center',
  fontSize: '0.875rem',
  borderRight: `1px solid ${theme.palette.divider}`,
  '&:last-child': {
    borderRight: 'none'
  }
}));

const DateNumber = styled(Typography)<{ isToday?: boolean }>(({ theme, isToday }) => ({
  fontSize: '0.875rem',
  fontWeight: isToday ? 600 : 400,
  color: isToday ? theme.palette.primary.main : 'inherit',
  width: '24px',
  height: '24px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%',
  backgroundColor: isToday ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
}));

const NavigationHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2, 1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const MonthYearDisplay = styled(Typography)(({ theme }) => ({
  fontSize: '22px',
  fontWeight: 400,
  marginRight: theme.spacing(4),
  minWidth: '200px',
}));

const TodayButton = styled(Button)(({ theme }) => ({
  marginRight: theme.spacing(4),
  textTransform: 'none',
  borderRadius: '4px',
  padding: theme.spacing(1, 2),
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const NavigationIconButton = styled(IconButton)(({ theme }) => ({
  padding: theme.spacing(1),
  marginRight: theme.spacing(1),
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const StatsContainer = styled(Box)(({ theme }) => ({
  marginLeft: 'auto',
  display: 'flex',
  gap: theme.spacing(3),
  alignItems: 'center',
}));

const StatBox = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(0, 2),
  borderLeft: `1px solid ${theme.palette.divider}`,
  '&:first-of-type': {
    borderLeft: 'none',
  },
}));

interface EventIndicatorProps extends BoxProps {}
interface EventChipProps extends ChipProps {}
interface DetailsListProps extends ListProps {}

const EventIndicator = styled(Box)<EventIndicatorProps>(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(0.5),
  marginTop: theme.spacing(1),
  flexWrap: 'wrap',
  '& .event-dot': {
    width: 6,
    height: 6,
    borderRadius: '50%',
    backgroundColor: theme.palette.primary.main,
  }
}));

const EventChip = styled(Chip)<EventChipProps>(({ theme }) => ({
  margin: theme.spacing(0.5),
  '&.individual': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    color: theme.palette.primary.main,
  },
  '&.group': {
    backgroundColor: alpha(theme.palette.success.main, 0.1),
    color: theme.palette.success.main,
  },
  '&.tournament': {
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.main,
  }
}));

const DetailsList = styled(List)<DetailsListProps>(({ theme }) => ({
  '& .MuiListItem-root': {
    borderBottom: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderBottom: 'none'
    }
  }
}));

interface TimeSlot {
  time: string;
  percentage: number;
  bookings: number;
}

interface DayStats {
  totalBookings: number;
  timeSlots: TimeSlot[];
  intensity: number;
}

interface BookingEvent {
  id: string;
  time: string;
  customerName: string;
  type: 'individual' | 'group' | 'tournament';
  price: number;
  players: number;
}

interface DayDetails {
  date: Date;
  events: BookingEvent[];
  stats: DayStats;
}

interface MonthlyCalendarViewProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const MonthlyCalendarView: React.FC<MonthlyCalendarViewProps> = ({ selectedDate, onDateSelect }) => {
  const [selectedDayDetails, setSelectedDayDetails] = useState<DayDetails | null>(null);
  const eventsCache = useRef<Map<string, BookingEvent[]>>(new Map());

  // Function to generate a consistent random number based on date
  const getConsistentRandom = useCallback((date: Date, seed: number = 1): number => {
    const dateString = `${date.getFullYear()}${date.getMonth()}${date.getDate()}${seed}`;
    let hash = 0;
    for (let i = 0; i < dateString.length; i++) {
      const char = dateString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return (Math.abs(hash) % 100) / 100; // Returns 0-1
  }, []);

  const handleMonthChange = (increment: number): void => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + increment);
    onDateSelect(newDate);
  };

  const handleTodayClick = (): void => {
    onDateSelect(new Date());
  };

  const getMonthDates = (date: Date): Date[][] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    
    const firstDayOfWeek = firstDay.getDay();
    
    // Calculate start date (last days of previous month if needed)
    const start = new Date(firstDay);
    start.setDate(start.getDate() - firstDayOfWeek);
    
    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];
    
    // Generate dates until we've included all days of the current month
    // plus any days needed to complete the last week
    while (true) {
      currentWeek.push(new Date(start));
      
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
      
      start.setDate(start.getDate() + 1);
      
      // Stop when we've included all days of the current month and completed the week
      if (start.getMonth() !== month && currentWeek.length === 0) {
        break;
      }
    }
    
    return weeks;
  };

  const monthWeeks = useMemo(() => getMonthDates(selectedDate), [selectedDate]);
  const currentMonth = selectedDate.getMonth();

  const getDayStats = useCallback((date: Date): DayStats => {
    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const weekPosition = Math.floor(date.getDate() / 7);
    
    // Base patterns adjusted by month
    const monthFactor = (() => {
      const month = date.getMonth();
      // Peak season (summer months)
      if (month >= 4 && month <= 8) return 1.2;
      // Shoulder season (spring/fall)
      if ((month >= 2 && month <= 3) || (month >= 9 && month <= 10)) return 1.0;
      // Off season (winter)
      return 0.8;
    })();

    // More realistic base bookings
    const baseBookings = isWeekend ? 25 : 15;
    const timeOfDay: { time: string; percentage: number }[] = [
      { time: 'Morning (6AM-10AM)', percentage: 0.3 },
      { time: 'Midday (10AM-2PM)', percentage: 0.4 },
      { time: 'Afternoon (2PM-6PM)', percentage: 0.3 },
    ];
    
    // Use consistent random factors based on the date
    const weekMultiplier = 1 + (weekPosition === 2 ? 0.2 : weekPosition === 1 || weekPosition === 3 ? 0.1 : 0);
    const randomFactor = 0.9 + (getConsistentRandom(date) * 0.2);
    
    const totalBookings = Math.floor(baseBookings * weekMultiplier * randomFactor * monthFactor);
    
    // Calculate time slot distribution with consistent randomness
    const timeSlots = timeOfDay.map((slot, index) => ({
      ...slot,
      bookings: Math.floor(totalBookings * slot.percentage * (1 + (getConsistentRandom(date, index + 1) - 0.5) * 0.1)),
    }));

    return {
      totalBookings,
      timeSlots,
      intensity: totalBookings / (isWeekend ? 30 : 20),
    };
  }, [getConsistentRandom]);

  const monthSummary = useMemo(() => {
    const stats = monthWeeks.reduce((acc, week) => {
      week.forEach((date) => {
      if (date.getMonth() === currentMonth) {
        const dayStats = getDayStats(date);
        acc.totalBookings += dayStats.totalBookings;
        acc.totalDays += 1;
      }
      });
      return acc;
    }, { totalBookings: 0, totalDays: 0 });

    return {
      ...stats,
      averagePerDay: Math.round(stats.totalBookings / stats.totalDays),
    };
  }, [monthWeeks, currentMonth, getDayStats]);

  const generateMockEvents = useCallback((date: Date): BookingEvent[] => {
    const dateKey = date.toISOString().split('T')[0];
    
    // Return cached events if they exist
    if (eventsCache.current.has(dateKey)) {
      return eventsCache.current.get(dateKey)!;
    }

    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    // Use getConsistentRandom to determine event count - more realistic numbers
    const baseEventCount = isWeekend ? 4 : 2;
    const eventCount = Math.floor(baseEventCount * (0.8 + getConsistentRandom(date) * 0.4));

    const events: BookingEvent[] = [];
    // Generate time slots in 12-hour format
    const timeSlots = Array.from({ length: 12 }, (_, i) => {
      const hour = 6 + i; // Starting from 6 AM
      const period = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour > 12 ? hour - 12 : hour;
      return `${displayHour}:00 ${period}`;
    });

    const types: Array<'individual' | 'group' | 'tournament'> = ['individual', 'group', 'tournament'];
    const names = ['John Smith', 'Emma Wilson', 'Michael Brown', 'Sarah Davis', 'James Miller'];

    for (let i = 0; i < eventCount; i++) {
      // Use getConsistentRandom with different seeds for each property
      const timeIndex = Math.floor(getConsistentRandom(date, i * 4 + 1) * timeSlots.length);
      const typeIndex = Math.floor(getConsistentRandom(date, i * 4 + 2) * types.length);
      const nameIndex = Math.floor(getConsistentRandom(date, i * 4 + 3) * names.length);
      
      const type = types[typeIndex];
      const basePrice = type === 'individual' ? 50 : type === 'group' ? 200 : 500;
      const players = type === 'individual' ? 1 : type === 'group' ? 4 : 16;

      events.push({
        id: `${dateKey}-${i}`,
        time: timeSlots[timeIndex],
        customerName: names[nameIndex],
        type,
        price: basePrice,
        players
      });
    }

    const sortedEvents = events.sort((a, b) => {
      // Sort by 12-hour time format
      const timeA = new Date(`1970/01/01 ${a.time}`);
      const timeB = new Date(`1970/01/01 ${b.time}`);
      return timeA.getTime() - timeB.getTime();
    });
    eventsCache.current.set(dateKey, sortedEvents);
    return sortedEvents;
  }, [getConsistentRandom]);

  const handleDayClick = useCallback((date: Date, stats: DayStats) => {
    const events = generateMockEvents(date);
    setSelectedDayDetails({
      date,
      events,
      stats
    });
    onDateSelect(date);
  }, [generateMockEvents, onDateSelect]);

  const handleCloseDetails = () => {
    setSelectedDayDetails(null);
  };

  return (
    <CalendarContainer>
      <NavigationHeader>
        <MonthYearDisplay>
          {selectedDate.toLocaleDateString('en-US', { 
            month: 'long',
            year: 'numeric'
          })}
        </MonthYearDisplay>

        <TodayButton 
          variant="outlined"
          onClick={handleTodayClick}
        >
          Today
        </TodayButton>

        <Box>
          <NavigationIconButton
            onClick={() => handleMonthChange(-1)}
            size="small"
          >
            <ChevronLeft />
          </NavigationIconButton>
          <NavigationIconButton
            onClick={() => handleMonthChange(1)}
            size="small"
          >
            <ChevronRight />
          </NavigationIconButton>
        </Box>

        <StatsContainer>
          <StatBox>
            <Typography variant="h6" color="success.main">
              {monthSummary.totalBookings}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Total Bookings
            </Typography>
          </StatBox>
          <StatBox>
            <Typography variant="h6" color="primary.main">
              {monthSummary.averagePerDay}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Avg. Per Day
            </Typography>
          </StatBox>
        </StatsContainer>
      </NavigationHeader>

      <CalendarGrid>
        <WeekdayHeader>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <WeekdayCell key={day} variant="subtitle2">
              {day}
            </WeekdayCell>
          ))}
        </WeekdayHeader>

        <WeeksContainer>
          {monthWeeks.map((week, weekIndex) => (
            <WeekRow key={weekIndex}>
              {week.map((date, dayIndex) => {
          const isCurrentMonth = date.getMonth() === currentMonth;
          const isWeekend = date.getDay() === 0 || date.getDay() === 6;
          const stats = getDayStats(date);
                const isToday = date.getDate() === new Date().getDate() && 
                              date.getMonth() === new Date().getMonth() && 
                              date.getFullYear() === new Date().getFullYear();
                
                const mockEvents = generateMockEvents(date);
          
          return (
              <DayCell
                    key={dayIndex}
                intensity={stats.intensity}
                isWeekend={isWeekend}
                isCurrentMonth={isCurrentMonth}
                    onClick={() => handleDayClick(date, stats)}
              >
                    <DateNumber
                  variant="body2"
                      isToday={isToday}
                  sx={{
                        color: !isCurrentMonth ? 'text.disabled' : 'inherit',
                  }}
                >
                  {date.getDate()}
                    </DateNumber>

                    <EventIndicator>
                      {mockEvents.slice(0, 3).map((event, i) => (
                        <Box 
                          key={i} 
                          className="event-dot" 
                          sx={{ 
                            backgroundColor: 
                              event.type === 'individual' ? 'primary.main' :
                              event.type === 'group' ? 'success.main' :
                              'error.main'
                          }} 
                        />
                      ))}
                      {mockEvents.length > 3 && (
                        <Typography variant="caption" sx={{ fontSize: '0.7rem', color: 'text.secondary' }}>
                          +{mockEvents.length - 3}
                </Typography>
                      )}
                    </EventIndicator>
              </DayCell>
          );
        })}
            </WeekRow>
          ))}
        </WeeksContainer>
      </CalendarGrid>

      <Dialog 
        open={!!selectedDayDetails} 
        onClose={handleCloseDetails}
        maxWidth="sm"
        fullWidth
      >
        {selectedDayDetails && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedDayDetails.date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </Typography>
                <Box>
                  <EventChip 
                    size="small"
                    label={`${selectedDayDetails.stats.totalBookings} Bookings`}
                    icon={<TimeIcon />}
                  />
                </Box>
              </Box>
            </DialogTitle>
            <DialogContent>
              <DetailsList>
                {selectedDayDetails.events.map((event) => (
                  <ListItem key={event.id}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Typography variant="subtitle1">
                            {event.time}
                          </Typography>
                          <EventChip
                            size="small"
                            label={event.type}
                            className={event.type}
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <PersonIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              {event.customerName} ({event.players} players)
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <MoneyIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              ${event.price}
                            </Typography>
                          </Box>
    </Box>
                      }
                    />
                  </ListItem>
                ))}
              </DetailsList>
            </DialogContent>
          </>
        )}
      </Dialog>
    </CalendarContainer>
  );
};

export default MonthlyCalendarView; 