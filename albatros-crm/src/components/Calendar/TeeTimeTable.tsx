import React from 'react';
import { Box, Typography, Grid, Chip, styled } from '@mui/material';

const TableContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(3)
}));

const TableHeader = styled(Grid)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  '& .MuiTypography-root': {
    color: theme.palette.text.secondary,
    fontWeight: 500
  }
}));

const TimeSlotRow = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(2, 0),
  borderBottom: `1px solid ${theme.palette.divider}`,
  alignItems: 'center'
}));

interface PlayerChipProps {
  isHighlighted?: boolean;
}

const PlayerChip = styled(Chip)<PlayerChipProps>(({ theme, isHighlighted }) => ({
  margin: theme.spacing(0.5),
  backgroundColor: isHighlighted ? '#E8F5E9' : undefined,
  '& .MuiChip-label': {
    color: isHighlighted ? '#2E7D32' : undefined,
  }
}));

const PlayerSlot = styled(Box)({
  width: 32,
  height: 32,
  borderRadius: '50%',
  backgroundColor: '#F5F5F5',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 4px',
  color: '#666',
  fontSize: '14px'
});

interface Player {
  name: string;
  isHighlighted: boolean;
}

interface TeeTimeSlot {
  time: string;
  cost: string;
  players: Player[];
}

interface TeeTimeTableProps {
  selectedDate: Date;
}

const TeeTimeTable: React.FC<TeeTimeTableProps> = ({ selectedDate }) => {
  // This would normally come from an API
  const teeTimeSlots: TeeTimeSlot[] = [
    {
      time: '7:00AM',
      cost: '$66',
      players: [
        { name: 'Charles Wink', isHighlighted: true },
        { name: 'Mark Lemon', isHighlighted: true },
        { name: 'Nathan Green', isHighlighted: false },
        { name: 'Blake Smith', isHighlighted: false }
      ]
    },
    {
      time: '7:15AM',
      cost: '$66',
      players: [
        { name: 'Player 1', isHighlighted: false }
      ]
    },
    {
      time: '7:30AM',
      cost: '$66',
      players: []
    },
    {
      time: '7:45AM',
      cost: '$66',
      players: []
    },
    {
      time: '8:00AM',
      cost: '$66',
      players: [
        { name: 'Player 1', isHighlighted: false },
        { name: 'Player 2', isHighlighted: false },
        { name: 'Player 3', isHighlighted: false }
      ]
    }
  ];

  return (
    <TableContainer>
      <TableHeader container>
        <Grid item xs={2}>
          <Typography variant="subtitle2">Time</Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="subtitle2">Availability</Typography>
        </Grid>
        <Grid item xs={2}>
          <Typography variant="subtitle2">Cost</Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="subtitle2">Names</Typography>
        </Grid>
      </TableHeader>

      {teeTimeSlots.map((slot, index) => (
        <TimeSlotRow container key={index}>
          <Grid item xs={2}>
            <Typography>{slot.time}</Typography>
          </Grid>
          <Grid item xs={4}>
            {[...Array(4)].map((_, i) => (
              <PlayerSlot key={i}>
                {i < slot.players.length ? (i + 1) : ''}
              </PlayerSlot>
            ))}
          </Grid>
          <Grid item xs={2}>
            <Typography>{slot.cost}</Typography>
          </Grid>
          <Grid item xs={4}>
            {slot.players.map((player, i) => (
              <PlayerChip
                key={i}
                label={player.name}
                isHighlighted={player.isHighlighted}
                variant="outlined"
              />
            ))}
          </Grid>
        </TimeSlotRow>
      ))}
    </TableContainer>
  );
};

export default TeeTimeTable; 