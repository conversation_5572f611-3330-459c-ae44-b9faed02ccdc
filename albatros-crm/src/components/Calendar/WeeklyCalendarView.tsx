import React, { useMemo, useRef } from 'react';
import { Box, Typography, Paper, Grid, styled } from '@mui/material';

const DayCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  minHeight: '260px',
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#FFFFFF',
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  cursor: 'pointer',
  '&:hover': {
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
  }
}));

interface AvailabilityChartProps {
  percentage?: number;
}

const AvailabilityChart = styled(Box)<AvailabilityChartProps>(({ theme, percentage = 0 }) => ({
  width: '100%',
  maxWidth: '180px',
  aspectRatio: '1',
  position: 'relative',
  margin: '16px auto',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: '50%',
    background: `conic-gradient(${theme.palette.primary.main} 0% ${percentage}%, #E5E7EB ${percentage}% 100%)`,
  }
}));

const StatsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  marginTop: 'auto',
  textAlign: 'center'
}));

interface DayStats {
  available: number;
  booked: number;
  percentage: number;
}

interface WeeklyCalendarViewProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const WeeklyCalendarView: React.FC<WeeklyCalendarViewProps> = ({ selectedDate, onDateSelect }) => {
  // Use ref to store static stats that persist between renders
  const staticStatsRef = useRef<Map<string, DayStats>>(new Map());

  const getWeekDates = (date: Date): Date[] => {
    const start = new Date(date);
    start.setDate(start.getDate() - start.getDay());
    
    const dates: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const current = new Date(start);
      current.setDate(start.getDate() + i);
      dates.push(current);
    }
    return dates;
  };

  // Generate static mock data for the week
  const weekDates = useMemo(() => getWeekDates(selectedDate), [selectedDate]);

  const getDayStats = (date: Date): DayStats => {
    const dateKey = date.toISOString().split('T')[0];
    
    // Return cached stats if they exist
    if (staticStatsRef.current.has(dateKey)) {
      return staticStatsRef.current.get(dateKey)!;
    }

    const dayOfWeek = date.getDay();
    
    // Define base total slots for each day
    const totalSlotsMap: Record<number, number> = {
      0: 150, // Sunday
      1: 126, // Monday
      2: 126, // Tuesday
      3: 132, // Wednesday
      4: 138, // Thursday
      5: 144, // Friday
      6: 150, // Saturday
    };

    // Define booking patterns for each day
    const bookingPatterns: Record<number, { min: number; max: number }> = {
      0: { min: 115, max: 135 }, // Sunday: very busy
      1: { min: 60, max: 80 },   // Monday: quieter
      2: { min: 70, max: 90 },   // Tuesday: bit busier
      3: { min: 85, max: 105 },  // Wednesday: medium busy
      4: { min: 95, max: 115 },  // Thursday: getting busier
      5: { min: 105, max: 125 }, // Friday: busy
      6: { min: 120, max: 140 }, // Saturday: very busy
    };

    const totalSlots = totalSlotsMap[dayOfWeek];
    const pattern = bookingPatterns[dayOfWeek];
    
    // Generate a random number of bookings within the day's pattern
    const booked = Math.floor(Math.random() * (pattern.max - pattern.min + 1)) + pattern.min;
    const available = totalSlots - booked;
    const percentage = Math.round((booked / totalSlots) * 100);

    const stats = {
      available,
      booked,
      percentage
    };

    // Cache the stats for this date
    staticStatsRef.current.set(dateKey, stats);
    return stats;
  };

  // Generate static stats for the week
  const weekStats = useMemo(() => {
    return weekDates.map(date => ({
      date,
      stats: getDayStats(date)
    }));
  }, [weekDates]);

  return (
    <Grid container spacing={3}>
      {weekStats.map(({ date, stats }, index) => (
        <Grid item xs={12} sm={6} md={4} lg={12/7} key={index}>
          <DayCard onClick={() => onDateSelect(date)}>
            <Typography variant="h6" align="center" sx={{ mb: 1 }}>
              {date.toLocaleDateString('en-US', { weekday: 'long' })}
            </Typography>
            
            <Box sx={{ position: 'relative', width: '100%', display: 'flex', justifyContent: 'center' }}>
              <AvailabilityChart percentage={stats.percentage}>
                <Typography 
                  variant="h5" 
                  sx={{ 
                    position: 'relative',
                    zIndex: 1,
                    fontWeight: 'bold',
                    color: 'text.primary'
                  }}
                >
                  {stats.percentage}%
                </Typography>
              </AvailabilityChart>
            </Box>
            
            <StatsContainer>
              <Box>
                <Typography variant="body1" sx={{ color: 'error.main', fontWeight: 'bold' }}>
                  Available:
                </Typography>
                <Typography variant="h6" sx={{ color: 'error.main' }}>
                  {stats.available}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body1" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                  Booked:
                </Typography>
                <Typography variant="h6" sx={{ color: 'success.main' }}>
                  {stats.booked}
                </Typography>
              </Box>
            </StatsContainer>
          </DayCard>
        </Grid>
      ))}
    </Grid>
  );
};

export default WeeklyCalendarView; 