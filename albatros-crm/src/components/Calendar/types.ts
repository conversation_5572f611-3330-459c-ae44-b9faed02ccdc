import { ReactNode } from 'react';

export interface CalendarProps {
  courseId?: string;
  viewMode?: 'daily' | 'weekly' | 'monthly';
  onDateSelect?: (date: Date) => void;
  onEventSelect?: (eventId: string) => void;
  events?: CalendarEvent[];
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  description?: string;
  type: 'tee-time' | 'event' | 'maintenance';
  status: 'scheduled' | 'completed' | 'cancelled';
  participants?: string[];
  color?: string;
} 