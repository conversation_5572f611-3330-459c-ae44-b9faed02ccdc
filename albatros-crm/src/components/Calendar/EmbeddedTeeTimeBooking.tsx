import React, { useState } from 'react';

// Mock data
const mockTeeTimes = [
  {
    time: '7:34 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '7:50 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$51.07',
    special: true,
  },
  {
    time: '8:08 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '8:25 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '9:16 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '9:41 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '9:50 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
  {
    time: '10:07 AM',
    course: 'Ridgeview Ranch Golf Club',
    price: '$39.95 - $59.95',
    special: false,
  },
];

const user = {
  name: '<PERSON>',
  avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
};

const EmbeddedTeeTimeBooking: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [partySize, setPartySize] = useState(2);
  const [specialRate, setSpecialRate] = useState(false);
  const [transportation, setTransportation] = useState<'cart' | 'walk'>('cart');
  const [timeRange, setTimeRange] = useState<[string, string]>(['', '']);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 300]);

  // Placeholder for calendar and filter logic

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <aside className="w-80 bg-white shadow-lg flex flex-col p-6 gap-6 border-r">
        <div className="flex items-center gap-3 mb-2">
          <img src="/logo.svg" alt="Albatross Logo" className="h-10 w-10" />
          <span className="font-bold text-lg tracking-wide">ALBATROSS</span>
        </div>
        {/* Calendar */}
        <div>
          <label className="block text-xs font-semibold mb-1">Date</label>
          <input
            type="date"
            className="w-full border rounded px-2 py-1"
            value={selectedDate.toISOString().split('T')[0]}
            onChange={e => setSelectedDate(new Date(e.target.value))}
          />
        </div>
        {/* Time of Day */}
        <div>
          <label className="block text-xs font-semibold mb-1">Time</label>
          <div className="flex gap-2">
            <button className="px-2 py-1 rounded bg-gray-100">Morning</button>
            <button className="px-2 py-1 rounded bg-gray-100">Afternoon</button>
            <button className="px-2 py-1 rounded bg-gray-100">Evening</button>
          </div>
        </div>
        {/* Party Size */}
        <div>
          <label className="block text-xs font-semibold mb-1">Party Size</label>
          <select
            className="w-full border rounded px-2 py-1"
            value={partySize}
            onChange={e => setPartySize(Number(e.target.value))}
          >
            {[1, 2, 3, 4].map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
        </div>
        {/* Special Rate */}
        <div className="flex items-center gap-2">
          <label className="text-xs font-semibold">Special Rate</label>
          <input
            type="checkbox"
            checked={specialRate}
            onChange={e => setSpecialRate(e.target.checked)}
          />
        </div>
        {/* Transportation */}
        <div>
          <label className="block text-xs font-semibold mb-1">Transportation</label>
          <div className="flex gap-2">
            <button
              className={`px-2 py-1 rounded ${transportation === 'cart' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
              onClick={() => setTransportation('cart')}
            >
              Cart
            </button>
            <button
              className={`px-2 py-1 rounded ${transportation === 'walk' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
              onClick={() => setTransportation('walk')}
            >
              Walk
            </button>
          </div>
        </div>
        {/* Booking Rules */}
        <div className="bg-gray-100 rounded p-3 text-xs">
          <div className="font-semibold mb-1">Booking Rules</div>
          <ul className="list-disc ml-4">
            <li>Max 4 players per group</li>
            <li>Cancel up to 24 hours in advance</li>
            <li>Special rates require ID at check-in</li>
          </ul>
        </div>
        {/* QR Code */}
        <div className="flex flex-col items-center mt-auto">
          <div className="bg-white p-2 rounded shadow">
            <img src="/qr-placeholder.png" alt="QR Code" className="h-24 w-24" />
          </div>
          <span className="text-xs mt-2 text-center">Download the Albatross app using the QR code for more features!</span>
        </div>
      </aside>
      {/* Main Content */}
      <main className="flex-1 p-8 overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Hello Golf Course Name <span role="img" aria-label="wave">👋🏼</span></h2>
          <div className="flex items-center gap-2">
            <img src={user.avatar} alt={user.name} className="h-10 w-10 rounded-full border" />
            <span className="font-semibold text-sm">{user.name}</span>
          </div>
        </div>
        {/* Tee Time Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {mockTeeTimes.map((tee, idx) => (
            <div
              key={idx}
              className={`rounded-lg border shadow-sm p-4 flex flex-col gap-2 relative ${tee.special ? 'border-yellow-400' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between">
                <span className="font-bold text-lg">{tee.time}</span>
                {tee.special && (
                  <span className="bg-yellow-200 text-yellow-800 text-xs px-2 py-1 rounded font-semibold">Special Rate</span>
                )}
              </div>
              <div className="text-sm text-gray-700">{tee.course}</div>
              <div className="text-lg font-semibold">{tee.price}</div>
              <button className="mt-2 bg-blue-600 text-white rounded px-4 py-2 font-semibold hover:bg-blue-700 transition">Choose Rate</button>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default EmbeddedTeeTimeBooking; 