import React from 'react';
import { Tabs, Tab, Paper, styled } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';

const HeaderContainer = styled(Paper)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(2),
  backgroundColor: '#FFFFFF',
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  overflow: 'hidden'
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  backgroundColor: '#F8FAFC',
  '& .MuiTabs-indicator': {
    display: 'none'
  }
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  minWidth: 120,
  padding: theme.spacing(1.5, 3),
  fontWeight: theme.typography.fontWeightMedium,
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  '&.Mui-selected': {
    backgroundColor: theme.palette.primary.main,
    color: '#FFFFFF',
  },
  '&:not(.Mui-selected)': {
    backgroundColor: 'transparent',
  },
  '&:hover:not(.Mui-selected)': {
    backgroundColor: theme.palette.primary.main + '14', // 14 is 8% opacity in hex
  }
}));

interface CalendarHeaderProps {
  courseName?: string;
  onBlankTabClick?: () => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ courseName, onBlankTabClick }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Set initial tab value based on current route
  const getInitialTab = (): number => {
    switch (location.pathname) {
      case '/calendar':
        return 0;
      case '/setup':
        return 1;
      case '/blank':
        return 2;
      default:
        return 0;
    }
  };

  const [value, setValue] = React.useState<number>(getInitialTab());

  const handleChange = (event: React.SyntheticEvent, newValue: number): void => {
    if (newValue === 2) {
      if (typeof onBlankTabClick === 'function') {
        onBlankTabClick();
      }
      setValue(newValue);
      navigate('/calendar');
      return;
    }
    setValue(newValue);
    switch (newValue) {
      case 0:
        navigate('/calendar');
        break;
      case 1:
        navigate('/setup');
        break;
      default:
        break;
    }
  };

  return (
    <HeaderContainer elevation={0}>
      <StyledTabs 
        value={value} 
        onChange={handleChange}
        variant="fullWidth"
      >
        <StyledTab label="Calendar" />
        <StyledTab label="Setup / Rules" />
        <StyledTab label="BLANK" />
      </StyledTabs>
    </HeaderContainer>
  );
};

export default CalendarHeader; 