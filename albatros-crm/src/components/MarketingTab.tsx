import React, { useState } from 'react';
import styles from './BackOffice.module.css';
import TemplateEditor from './TemplateEditor';

const marketingTemplates = [
  { 
    id: 1, 
    name: 'Welcome Email', 
    type: 'Email', 
    lastModified: '2024-01-15', 
    status: 'Active',
    blocks: [
      { id: '1', type: 'headline', content: 'Welcome to Albatros Golf Club!' },
      { id: '2', type: 'paragraph', content: "Hi {$firstname}, welcome to the Albatros Golf Club family! We're excited to have you join us." },
      { id: '3', type: 'paragraph', content: "As a new member, you'll have access to our world-class facilities, exclusive events, and personalized services." },
      { id: '4', type: 'button', content: 'Book Your First Round' },
    ],
    settings: {
      bgColor: '#FDBDBB',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 2, 
    name: 'Membership Renewal', 
    type: 'SMS', 
    lastModified: '2024-01-14', 
    status: 'Draft',
    blocks: [
      { id: '1', type: 'headline', content: 'Renew Your Membership' },
      { id: '2', type: 'paragraph', content: "Hi {$firstname}, your Albatros Golf Club membership expires soon. Don't miss out on another year of golf excellence!" },
      { id: '3', type: 'button', content: 'Renew Now' },
    ],
    settings: {
      bgColor: '#E0E7FF',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 3, 
    name: 'Tournament Invitation', 
    type: 'Email', 
    lastModified: '2024-01-13', 
    status: 'Active',
    blocks: [
      { id: '1', type: 'headline', content: 'You\'re Invited: Spring Championship' },
      { id: '2', type: 'paragraph', content: "Hi {$firstname}, you're invited to participate in our Spring Championship tournament!" },
      { id: '3', type: 'paragraph', content: "Join us for a day of competitive golf, prizes, and camaraderie. Limited spots available." },
      { id: '4', type: 'button', content: 'Register Now' },
    ],
    settings: {
      bgColor: '#D1FAE5',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 4, 
    name: 'Special Promotion - New Year', 
    type: 'SMS', 
    lastModified: '2024-01-12', 
    status: 'Inactive',
    blocks: [
      { id: '1', type: 'headline', content: 'New Year Special Offer' },
      { id: '2', type: 'paragraph', content: "Start 2024 right with 20% off green fees for the entire month of January!" },
      { id: '3', type: 'button', content: 'Book Now' },
    ],
    settings: {
      bgColor: '#FEF3C7',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 5, 
    name: 'Member Services - Club Fitting', 
    type: 'Email', 
    lastModified: '2024-01-13', 
    status: 'Active',
    blocks: [
      { id: '1', type: 'headline', content: 'Professional Club Fitting Available' },
      { id: '2', type: 'paragraph', content: "Hi {$firstname}, improve your game with our professional club fitting service!" },
      { id: '3', type: 'paragraph', content: "Our certified fitters will analyze your swing and recommend the perfect clubs for your game." },
      { id: '4', type: 'button', content: 'Schedule Fitting' },
    ],
    settings: {
      bgColor: '#FCE7F3',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 6, 
    name: 'Special Promotion - Masters Sunday', 
    type: 'SMS', 
    lastModified: '2024-01-09', 
    status: 'Inactive',
    blocks: [
      { id: '1', type: 'headline', content: 'Masters Sunday Special' },
      { id: '2', type: 'paragraph', content: "Watch the Masters and play golf! Special rates this Sunday with complimentary refreshments." },
      { id: '3', type: 'button', content: 'Reserve Tee Time' },
    ],
    settings: {
      bgColor: '#DBEAFE',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
  { 
    id: 7, 
    name: 'App Welcome Notification', 
    type: 'In-App', 
    lastModified: '2024-01-10', 
    status: 'Active',
    blocks: [
      { id: '1', type: 'headline', content: 'Welcome to Albatros App!' },
      { id: '2', type: 'paragraph', content: "Hi {$firstname}, welcome to the Albatros Golf Club mobile app!" },
      { id: '3', type: 'image', content: 'https://placehold.co/300x150?text=App+Welcome' },
      { id: '4', type: 'button', content: 'Get Started' },
    ],
    settings: {
      bgColor: '#F3E8FF',
      contentBgColor: '#fff',
      language: 'en'
    }
  },
];

const mockRecipients = [
  { id: 1, name: 'John Smith', email: '<EMAIL>', tags: ['VIP', 'Premium'], membershipType: 'Golf Member' },
  { id: 2, name: 'Jane Doe', email: '<EMAIL>', tags: ['Regular'], membershipType: 'New Golfer' },
  { id: 3, name: 'Bob Wilson', email: '<EMAIL>', tags: ['VIP'], membershipType: 'Golf Member' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', tags: ['Regular'], membershipType: 'Obsolete Golfer' },
  { id: 5, name: 'Charlie Davis', email: '<EMAIL>', tags: ['New'], membershipType: 'New Golfer' },
  { id: 6, name: 'Emily Clark', email: '<EMAIL>', tags: ['VIP'], membershipType: 'Golf Member' },
  { id: 7, name: 'Frank Harris', email: '<EMAIL>', tags: ['Obsolete'], membershipType: 'Obsolete Golfer' },
  { id: 8, name: 'Grace Lee', email: '<EMAIL>', tags: ['Premium'], membershipType: 'Golf Member' },
  { id: 9, name: 'Henry King', email: '<EMAIL>', tags: ['New'], membershipType: 'New Golfer' },
  { id: 10, name: 'Ivy Moore', email: '<EMAIL>', tags: ['Regular'], membershipType: 'Golf Member' },
  { id: 11, name: 'Jackie Nguyen', email: '<EMAIL>', tags: ['VIP'], membershipType: 'Golf Member' },
  { id: 12, name: 'Kevin Patel', email: '<EMAIL>', tags: ['Obsolete'], membershipType: 'Obsolete Golfer' },
  { id: 13, name: 'Linda Quinn', email: '<EMAIL>', tags: ['Premium'], membershipType: 'Golf Member' },
  { id: 14, name: 'Mike Ross', email: '<EMAIL>', tags: ['New'], membershipType: 'New Golfer' },
  { id: 15, name: 'Nina Scott', email: '<EMAIL>', tags: ['Regular'], membershipType: 'Golf Member' },
  { id: 16, name: 'Oscar Turner', email: '<EMAIL>', tags: ['VIP'], membershipType: 'Golf Member' },
  { id: 17, name: 'Paula Underwood', email: '<EMAIL>', tags: ['Obsolete'], membershipType: 'Obsolete Golfer' },
  { id: 18, name: 'Quincy Vega', email: '<EMAIL>', tags: ['Premium'], membershipType: 'Golf Member' },
  { id: 19, name: 'Rita White', email: '<EMAIL>', tags: ['New'], membershipType: 'New Golfer' },
  { id: 20, name: 'Sam Young', email: '<EMAIL>', tags: ['Regular'], membershipType: 'Golf Member' },
];

const activeCampaigns = [
  {
    id: 1,
    name: 'Welcome Campaign - Q1 2024',
    template: 'Welcome Email',
    type: 'Email',
    status: 'Active',
    recipients: 150,
    sent: 150,
    opened: 99,
    clicked: 33,
    startDate: '2024-01-01',
    endDate: '2024-01-31',
  },
  {
    id: 2,
    name: 'Tournament Invitation - March Classic',
    template: 'Tournament Invitation',
    type: 'Email',
    status: 'Active',
    recipients: 200,
    sent: 200,
    opened: 124,
    clicked: 74,
    startDate: '2024-02-01',
    endDate: '2024-02-29',
  },
  {
    id: 3,
    name: 'Club Fitting Promotion',
    template: 'Member Services - Club Fitting',
    type: 'SMS',
    status: 'Scheduled',
    recipients: 100,
    sent: 0,
    opened: 0,
    clicked: 0,
    startDate: '2024-03-15',
    endDate: '2024-03-31',
  },
];

const campaignAnalytics = [
  {
    name: 'Welcome Email',
    type: 'Email',
    openRate: 66,
    clickRate: 33,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-01-01',
    endDate: null,
  },
  {
    name: 'Tournament Invitation - March Classic',
    type: 'Email',
    openRate: 62,
    clickRate: 37,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-02-01',
    endDate: null,
  },
  {
    name: 'Member Services - Club Fitting',
    type: 'SMS',
    openRate: 60,
    clickRate: 31,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-03-01',
    endDate: null,
  },
];

const MarketingTab: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);
  const [showCreateCampaignModal, setShowCreateCampaignModal] = useState(false);
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    templateId: '',
    startDate: '',
    endDate: '',
    selectedRecipients: [] as number[],
    filters: {
      membershipType: '',
      tags: [] as string[],
    }
  });

  const handleOpenModal = (campaign: any) => {
    setSelectedCampaign(campaign);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedCampaign(null);
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowEditor(true);
  };

  const handleEditTemplate = (template: any) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
    setEditingTemplate(null);
  };

  const handleCreateCampaign = () => {
    setShowCreateCampaignModal(true);
  };

  const handleCloseCreateCampaignModal = () => {
    setShowCreateCampaignModal(false);
    setCampaignForm({
      name: '',
      templateId: '',
      startDate: '',
      endDate: '',
      selectedRecipients: [],
      filters: {
        membershipType: '',
        tags: [],
      }
    });
  };

  const handleSaveCampaign = () => {
    // Here you would save the campaign to your backend
    console.log('Saving campaign:', campaignForm);
    handleCloseCreateCampaignModal();
  };

  const handleRecipientSelection = (recipientId: number) => {
    setCampaignForm(prev => ({
      ...prev,
      selectedRecipients: prev.selectedRecipients.includes(recipientId)
        ? prev.selectedRecipients.filter(id => id !== recipientId)
        : [...prev.selectedRecipients, recipientId]
    }));
  };

  const handleSelectAllRecipients = () => {
    const allRecipientIds = mockRecipients.map(r => r.id);
    setCampaignForm(prev => ({
      ...prev,
      selectedRecipients: allRecipientIds
    }));
  };

  const handleClearRecipients = () => {
    setCampaignForm(prev => ({
      ...prev,
      selectedRecipients: []
    }));
  };

  if (showEditor) {
    return <TemplateEditor onClose={handleCloseEditor} template={editingTemplate} />;
  }

  return (
    <div className={styles.marketingTab}>
      {/* Marketing Templates Section */}
      <div className={styles.templatesSection}>
        <div className={styles.templatesHeader}>
          <h2 className={styles.heading}>Marketing Templates</h2>
          <div className={styles.templatesActions}>
            <button className={styles.secondaryButton}>See All</button>
            <button className={styles.primaryButton} onClick={handleCreateTemplate}>Create Template</button>
          </div>
        </div>
        <div className={styles.templatesGrid}>
          {marketingTemplates.map((template, idx) => (
            <div key={idx} className={styles.templateCard}>
              <div className={styles.templateHeader}>
                <span className={
                  template.type === 'SMS' ? styles.templateTypeSMS :
                  template.type === 'In-App' ? styles.templateTypeInApp :
                  styles.templateType
                }>
                  {template.type}
                </span>
                <span className={
                  template.status === 'Active' ? styles.templateStatusActive :
                  template.status === 'Draft' ? styles.templateStatusDraft :
                  styles.templateStatusInactive
                }>
                  {template.status}
                </span>
              </div>
              <div className={styles.templateName}>{template.name}</div>
              <div className={styles.templateMeta}>Last modified: {template.lastModified}</div>
              <div className={styles.templateActions}>
                <button 
                  className={styles.editButton} 
                  onClick={() => handleEditTemplate(template)}
                  title="Edit template"
                >
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Marketing Campaigns Section */}
      <div className={styles.campaignsSection}>
        <div className={styles.campaignsHeader}>
          <h2 className={styles.heading}>Marketing Campaigns</h2>
          <div className={styles.campaignsActions}>
            <button className={styles.secondaryButton}>View All</button>
            <button className={styles.primaryButton} onClick={handleCreateCampaign}>Create Campaign</button>
          </div>
        </div>
        <div className={styles.campaignsTableWrapper}>
          <table className={styles.campaignsTable}>
            <thead>
              <tr>
                <th>Campaign Name</th>
                <th>Template</th>
                <th>Type</th>
                <th>Status</th>
                <th>Recipients</th>
                <th>Sent</th>
                <th>Open Rate</th>
                <th>Click Rate</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {activeCampaigns.map((campaign) => (
                <tr key={campaign.id}>
                  <td className={styles.campaignName}>{campaign.name}</td>
                  <td>{campaign.template}</td>
                  <td>
                    <span className={styles.templateType}>{campaign.type}</span>
                  </td>
                  <td>
                    <span className={
                      campaign.status === 'Active' ? styles.templateStatusActive :
                      campaign.status === 'Scheduled' ? styles.templateStatusDraft :
                      styles.templateStatusInactive
                    }>
                      {campaign.status}
                    </span>
                  </td>
                  <td>{campaign.recipients}</td>
                  <td>{campaign.sent}</td>
                  <td>
                    <span className={styles.analyticsCircle}>
                      {campaign.sent > 0 ? Math.round((campaign.opened / campaign.sent) * 100) : 0}%
                    </span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>
                      {campaign.sent > 0 ? Math.round((campaign.clicked / campaign.sent) * 100) : 0}%
                    </span>
                  </td>
                  <td>{campaign.startDate}</td>
                  <td>{campaign.endDate}</td>
                  <td>
                    <button className={styles.moreButton} onClick={() => handleOpenModal(campaign)}>...</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Campaign Analytics Section */}
      <div className={styles.analyticsSection}>
        <h2 className={styles.heading}>Campaign Analytics</h2>
        <div className={styles.analyticsTableWrapper}>
          <table className={styles.analyticsTable}>
            <thead>
              <tr>
                <th>Type</th>
                <th>Campaign</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Open Rate</th>
                <th>Click Rate</th>
                <th>Bounce Rate</th>
                <th>Unsubscribe</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {campaignAnalytics.map((c, idx) => (
                <tr key={idx}>
                  <td>
                    <span className={styles.templateType}>{c.type}</span>
                  </td>
                  <td>{c.name}</td>
                  <td>{c.startDate}</td>
                  <td>{c.endDate || 'Not Set'}</td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.openRate}%<br /><span className={styles.analyticsLabel}>Open Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.clickRate}%<br /><span className={styles.analyticsLabel}>Click Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.bounceRate}%<br /><span className={styles.analyticsLabel}>Bounce Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.unsubscribe}%<br /><span className={styles.analyticsLabel}>Unsubscribe</span></span>
                  </td>
                  <td>
                    <button className={styles.moreButton} onClick={() => handleOpenModal(c)}>...</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Campaign Modal */}
      {showCreateCampaignModal && (
        <div className={styles.modalOverlay} onClick={handleCloseCreateCampaignModal}>
          <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
            <h3>Create New Campaign</h3>
            
            <div className={styles.campaignForm}>
              <div className={styles.formGroup}>
                <label>Campaign Name</label>
                <input
                  type="text"
                  value={campaignForm.name}
                  onChange={(e) => setCampaignForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter campaign name"
                  className={styles.formInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Select Template</label>
                <select
                  value={campaignForm.templateId}
                  onChange={(e) => setCampaignForm(prev => ({ ...prev, templateId: e.target.value }))}
                  className={styles.formSelect}
                >
                  <option value="">Choose a template</option>
                  {marketingTemplates.filter(t => t.status === 'Active').map(template => (
                    <option key={template.id} value={template.id}>
                      {template.name} ({template.type})
                    </option>
                  ))}
                </select>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Start Date</label>
                  <input
                    type="date"
                    value={campaignForm.startDate}
                    onChange={(e) => setCampaignForm(prev => ({ ...prev, startDate: e.target.value }))}
                    className={styles.formInput}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>End Date</label>
                  <input
                    type="date"
                    value={campaignForm.endDate}
                    onChange={(e) => setCampaignForm(prev => ({ ...prev, endDate: e.target.value }))}
                    className={styles.formInput}
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label>Recipient Targeting</label>
                <div className={styles.recipientFilters}>
                  <div className={styles.filterRow}>
                    <select
                      value={campaignForm.filters.membershipType}
                      onChange={(e) => setCampaignForm(prev => ({
                        ...prev,
                        filters: { ...prev.filters, membershipType: e.target.value }
                      }))}
                      className={styles.formSelect}
                    >
                      <option value="">All Golfers</option>
                      <option value="New Golfer">New Golfer</option>
                      <option value="Golf Member">Golf Member</option>
                      <option value="Obsolete Golfer">Obsolete Golfer</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className={styles.formGroup}>
                <div className={styles.recipientHeader}>
                  <label>Select Recipients ({campaignForm.selectedRecipients.length} selected)</label>
                  <div className={styles.recipientActions}>
                    <button type="button" onClick={handleSelectAllRecipients} className={styles.secondaryButton}>
                      Select All
                    </button>
                    <button type="button" onClick={handleClearRecipients} className={styles.secondaryButton}>
                      Clear All
                    </button>
                  </div>
                </div>
                <div className={styles.recipientsList}>
                  <div className={styles.formGroup}>
                    <label>Recipients</label>
                    <div style={{ maxHeight: 220, overflowY: 'auto', border: '1px solid #e5e7eb', borderRadius: 8, padding: 8, background: '#f8fafc' }}>
                      {mockRecipients.map(recipient => (
                        <div key={recipient.id} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                          <input
                            type="checkbox"
                            checked={campaignForm.selectedRecipients.includes(recipient.id)}
                            onChange={() => handleRecipientSelection(recipient.id)}
                          />
                          <span>{recipient.name} ({recipient.membershipType})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.modalActions}>
              <button className={styles.secondaryButton} onClick={handleCloseCreateCampaignModal}>
                Cancel
              </button>
              <button 
                className={styles.primaryButton} 
                onClick={handleSaveCampaign}
                disabled={!campaignForm.name || !campaignForm.templateId || campaignForm.selectedRecipients.length === 0}
              >
                Create Campaign
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal for Campaign Details */}
      {modalOpen && (
        <div className={styles.modalOverlay} onClick={handleCloseModal}>
          <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
            <h3>{selectedCampaign?.name} Details</h3>
            <div className={styles.analyticsModalGrid}>
              {/* Donut Graphs Section */}
              <div className={styles.analyticsDonuts}>
                <DonutStat label="Opened" percent={selectedCampaign?.openRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.opened / selectedCampaign?.sent) * 100) : 0)} color="#4f8cff" number={selectedCampaign?.opened ?? 0} />
                <DonutStat label="Clicked" percent={selectedCampaign?.clickRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.clicked / selectedCampaign?.sent) * 100) : 0)} color="#10b981" number={selectedCampaign?.clicked ?? 0} />
                <DonutStat label="Bounce" percent={selectedCampaign?.bounceRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.bounced / selectedCampaign?.sent) * 100) : 0)} color="#f59e42" number={selectedCampaign?.bounced ?? 0} />
                <DonutStat label="Spam" percent={selectedCampaign?.spamRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.spam / selectedCampaign?.sent) * 100) : 0)} color="#ef4444" number={selectedCampaign?.spam ?? 0} />
              </div>
              {/* Text Details Section */}
              <div className={styles.analyticsModalDetails}>
                <p><b>Type:</b> {selectedCampaign?.type}</p>
                <p><b>Start Date:</b> {selectedCampaign?.startDate}</p>
                <p><b>End Date:</b> {selectedCampaign?.endDate || 'Not Set'}</p>
                <p><b>Open Rate:</b> {selectedCampaign?.openRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.opened / selectedCampaign?.sent) * 100) : 0)}%</p>
                <p><b>Click Rate:</b> {selectedCampaign?.clickRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.clicked / selectedCampaign?.sent) * 100) : 0)}%</p>
                <p><b>Bounce Rate:</b> {selectedCampaign?.bounceRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.bounced / selectedCampaign?.sent) * 100) : 0)}%</p>
                <p><b>Spam:</b> {selectedCampaign?.spamRate ?? (selectedCampaign?.sent ? Math.round((selectedCampaign?.spam / selectedCampaign?.sent) * 100) : 0)}%</p>
              </div>
            </div>
            <button className={styles.primaryButton} onClick={handleCloseModal}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
};

// DonutStat component for visualization
const DonutStat = ({ label, percent, color, number }: { label: string, percent: number, color: string, number: number }) => {
  const radius = 36;
  const stroke = 7;
  const normalizedRadius = radius - stroke / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const progress = Math.max(0, Math.min(100, percent));
  const offset = circumference - (progress / 100) * circumference;
  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', margin: '0 18px' }}>
      <svg height={radius * 2} width={radius * 2}>
        <circle
          stroke="#e5e7eb"
          fill="transparent"
          strokeWidth={stroke}
          r={normalizedRadius}
          cx={radius}
          cy={radius}
        />
        <circle
          stroke={color}
          fill="transparent"
          strokeWidth={stroke}
          strokeLinecap="round"
          strokeDasharray={circumference + ' ' + circumference}
          style={{ strokeDashoffset: offset, transition: 'stroke-dashoffset 0.5s' }}
          r={normalizedRadius}
          cx={radius}
          cy={radius}
        />
      </svg>
      <div style={{ position: 'relative', top: '-54px', textAlign: 'center', width: '72px' }}>
        <div style={{ fontWeight: 700, fontSize: '1.2rem', color }}>{percent}%</div>
        <div style={{ fontSize: '0.9rem', color: '#64748b' }}>{number.toLocaleString()}</div>
      </div>
      <div style={{ marginTop: '-18px', fontWeight: 500, fontSize: '0.95rem', color: '#374151' }}>{label}</div>
    </div>
  );
};

export default MarketingTab; 