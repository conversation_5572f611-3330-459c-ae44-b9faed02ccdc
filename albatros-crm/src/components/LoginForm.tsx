import React, { useState } from 'react';
import { useXanoAuth } from '../api/hooks/useXanoAuth';

export function LoginForm() {
  const { login, logout, signup, resetPassword, user, error, loading, token } = useXanoAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [signupMode, setSignupMode] = useState(false);
  const [resetMode, setResetMode] = useState(false);
  const [resetEmail, setResetEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (signupMode) {
      signup(email, password);
    } else {
      login(email, password);
    }
  };

  const handleReset = (e: React.FormEvent) => {
    e.preventDefault();
    resetPassword(resetEmail);
  };

  return (
    <div style={{ maxWidth: 340, margin: '2rem auto', padding: 24, border: '1px solid #eee', borderRadius: 8 }}>
      {!resetMode ? (
        <form onSubmit={handleSubmit}>
          <h2>{signupMode ? 'Sign Up' : 'Login'}</h2>
          <input value={email} onChange={e => setEmail(e.target.value)} placeholder="Email" style={{ width: '100%', marginBottom: 12, padding: 8 }} />
          <input value={password} onChange={e => setPassword(e.target.value)} type="password" placeholder="Password" style={{ width: '100%', marginBottom: 12, padding: 8 }} />
          <button type="submit" disabled={loading} style={{ width: '100%', padding: 10, background: '#2563eb', color: '#fff', border: 'none', borderRadius: 4 }}>
            {loading ? (signupMode ? 'Signing up...' : 'Logging in...') : signupMode ? 'Sign Up' : 'Login'}
          </button>
          <div style={{ marginTop: 12, display: 'flex', justifyContent: 'space-between' }}>
            <button type="button" onClick={() => setSignupMode(m => !m)} style={{ background: 'none', border: 'none', color: '#2563eb', cursor: 'pointer' }}>
              {signupMode ? 'Have an account? Login' : 'No account? Sign Up'}
            </button>
            <button type="button" onClick={() => setResetMode(true)} style={{ background: 'none', border: 'none', color: '#2563eb', cursor: 'pointer' }}>
              Forgot Password?
            </button>
          </div>
          {error && <div style={{ color: 'red', marginTop: 12 }}>{error}</div>}
          {user && token && (
            <div style={{ color: 'green', marginTop: 12 }}>
              Welcome, {user.email || user.name}!<br />
              <button type="button" onClick={logout} style={{ marginTop: 8, background: '#eee', border: 'none', borderRadius: 4, padding: 6, cursor: 'pointer' }}>Logout</button>
            </div>
          )}
        </form>
      ) : (
        <form onSubmit={handleReset}>
          <h2>Reset Password</h2>
          <input value={resetEmail} onChange={e => setResetEmail(e.target.value)} placeholder="Email" style={{ width: '100%', marginBottom: 12, padding: 8 }} />
          <button type="submit" disabled={loading} style={{ width: '100%', padding: 10, background: '#2563eb', color: '#fff', border: 'none', borderRadius: 4 }}>
            {loading ? 'Sending...' : 'Send Reset Email'}
          </button>
          <button type="button" onClick={() => setResetMode(false)} style={{ marginTop: 12, background: 'none', border: 'none', color: '#2563eb', cursor: 'pointer' }}>Back to Login</button>
          {error && <div style={{ color: 'red', marginTop: 12 }}>{error}</div>}
        </form>
      )}
    </div>
  );
} 