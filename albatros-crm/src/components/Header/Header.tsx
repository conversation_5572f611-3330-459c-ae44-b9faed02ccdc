import React from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import {
  PhotoCamera as CameraIcon,
  Chat as ChatIcon,
  Help as HelpIcon,
  ShoppingCart as CartIcon,
} from '@mui/icons-material';
import { HeaderProps } from './types';
import { HeaderContainer, ActionButtonsContainer } from './styles';

/**
 * Header component that displays the course welcome message and action buttons
 * @param {HeaderProps} props - Component props
 */
const Header: React.FC<HeaderProps> = ({ courseInfo }) => {
  return (
    <HeaderContainer>
      <Box>
        <Typography variant="h4" gutterBottom>
          Hello Westdale Hills Golf Course 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Euless, TX
        </Typography>
      </Box>
      <ActionButtonsContainer>
        <IconButton>
          <CameraIcon />
        </IconButton>
        <IconButton>
          <ChatIcon />
        </IconButton>
        <IconButton>
          <HelpIcon />
        </IconButton>
        <IconButton>
          <CartIcon />
        </IconButton>
      </ActionButtonsContainer>
    </HeaderContainer>
  );
};

export default Header; 