import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Alert,
  Avatar,
  Stack,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import PersonIcon from '@mui/icons-material/Person';
import { format } from 'date-fns';
import { styled } from '@mui/material';
import Menu from '@mui/material/Menu';
import Fab from '@mui/material/Fab';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { SelectChangeEvent } from '@mui/material/Select';

interface WaitlistEntry {
  id: string;
  avatar: string;
  name: string;
  phone: string;
  lastPlayedDate: string | null;
  accepted: number;
  lastWaitlistAccepted: string | null;
  upcomingPlayDate: string | null;
  status: 'Pending' | 'Notified' | 'Expired' | 'Accepted';
  contact: string;
  preferredTime?: string;
  partySize?: number;
}

interface WaitlistRules {
  enabled: boolean;
  maxDaysAhead: number;
  autoExpireDays: number;
  maxPartySize: number;
  notificationMethod: 'Email' | 'SMS' | 'Both';
  restrictedTimes: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  autoAccept: boolean;
}

interface WaitlistFormData {
  name: string;
  phone: string;
  email: string;
  preferredTime: string;
  partySize: number;
  status: WaitlistEntry['status'];
}

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  cursor: 'pointer',
  '&:hover': { 
    backgroundColor: theme.palette.action.hover 
  }
}));

const mockWaitlist: WaitlistEntry[] = [
  {
    id: '1',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    name: 'Abbi Lopez',
    phone: '(*************',
    lastPlayedDate: '2024-12-11',
    accepted: 4,
    lastWaitlistAccepted: '2024-12-01',
    upcomingPlayDate: '2024-12-13',
    status: 'Accepted',
    contact: '<EMAIL>'
  },
  {
    id: '2',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    name: 'Jake Smith',
    phone: '(*************',
    lastPlayedDate: '2024-12-11',
    accepted: 1,
    lastWaitlistAccepted: null,
    upcomingPlayDate: null,
    status: 'Pending',
    contact: '<EMAIL>'
  },
  {
    id: '3',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    name: 'Dylan Chun',
    phone: '(*************',
    lastPlayedDate: '2024-12-09',
    accepted: 3,
    lastWaitlistAccepted: '2024-12-01',
    upcomingPlayDate: '2024-12-12',
    status: 'Notified',
    contact: '<EMAIL>'
  },
  {
    id: '4',
    avatar: '/static/images/avatar/4.jpg',
    name: 'James Rodriguez',
    phone: '(*************',
    lastPlayedDate: null,
    accepted: 0,
    lastWaitlistAccepted: null,
    upcomingPlayDate: null,
    status: 'Pending',
    contact: '<EMAIL>'
  },
  {
    id: '5',
    avatar: '/static/images/avatar/5.jpg',
    name: 'Robert Wilson',
    phone: '(*************',
    lastPlayedDate: '2024-03-17',
    accepted: 32,
    lastWaitlistAccepted: '2024-03-15',
    upcomingPlayDate: '2024-03-24',
    status: 'Accepted',
    contact: '<EMAIL>'
  },
  {
    id: '6',
    avatar: '/static/images/avatar/6.jpg',
    name: 'Emily Parker',
    phone: '(*************',
    lastPlayedDate: '2024-02-28',
    accepted: 3,
    lastWaitlistAccepted: '2024-01-15',
    upcomingPlayDate: null,
    status: 'Expired',
    contact: '<EMAIL>'
  },
  {
    id: '7',
    avatar: '/static/images/avatar/7.jpg',
    name: 'Thomas Anderson',
    phone: '(*************',
    lastPlayedDate: '2024-03-16',
    accepted: 18,
    lastWaitlistAccepted: '2024-03-18',
    upcomingPlayDate: '2024-03-26',
    status: 'Notified',
    contact: '<EMAIL>'
  },
  {
    id: '8',
    avatar: '/static/images/avatar/8.jpg',
    name: 'Lisa Martinez',
    phone: '(*************',
    lastPlayedDate: '2024-03-12',
    accepted: 6,
    lastWaitlistAccepted: '2024-03-01',
    upcomingPlayDate: null,
    status: 'Pending',
    contact: '<EMAIL>'
  },
  {
    id: '9',
    avatar: '/static/images/avatar/9.jpg',
    name: 'Kevin O\'Brien',
    phone: '(*************',
    lastPlayedDate: '2024-03-19',
    accepted: 41,
    lastWaitlistAccepted: '2024-03-17',
    upcomingPlayDate: '2024-03-23',
    status: 'Accepted',
    contact: '<EMAIL>'
  },
  {
    id: '10',
    avatar: '/static/images/avatar/10.jpg',
    name: 'Amanda Lee',
    phone: '(*************',
    lastPlayedDate: '2024-02-15',
    accepted: 2,
    lastWaitlistAccepted: '2024-02-01',
    upcomingPlayDate: null,
    status: 'Expired',
    contact: '<EMAIL>'
  }
];

export const WaitlistTab: React.FC = () => {
  const [currentTab, setCurrentTab] = useState<'current' | 'rules'>('current');
  const [waitlistRules, setWaitlistRules] = useState<WaitlistRules>({
    enabled: true,
    maxDaysAhead: 14,
    autoExpireDays: 2,
    maxPartySize: 4,
    notificationMethod: 'Both',
    restrictedTimes: {
      enabled: true,
      startTime: '07:00',
      endTime: '15:00'
    },
    autoAccept: false
  });
  const [selectedEntry, setSelectedEntry] = useState<WaitlistEntry | null>(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    entry: WaitlistEntry | null;
  } | null>(null);
  const [formData, setFormData] = useState<WaitlistFormData>({
    name: '',
    phone: '',
    email: '',
    preferredTime: '',
    partySize: 1,
    status: 'Pending'
  });
  const [waitlistData, setWaitlistData] = useState<WaitlistEntry[]>(mockWaitlist);

  useEffect(() => {
    if (selectedEntry) {
      setFormData({
        name: selectedEntry.name,
        phone: selectedEntry.phone,
        email: selectedEntry.contact,
        preferredTime: selectedEntry.preferredTime || '',
        partySize: selectedEntry.partySize || 1,
        status: selectedEntry.status
      });
    } else {
      setFormData({
        name: '',
        phone: '',
        email: '',
        preferredTime: '',
        partySize: 1,
        status: 'Pending'
      });
    }
  }, [selectedEntry]);

  const getStatusColor = (status: WaitlistEntry['status']): 'warning' | 'info' | 'error' | 'success' => {
    const colors = {
      Pending: 'warning' as const,
      Notified: 'info' as const,
      Expired: 'error' as const,
      Accepted: 'success' as const
    };
    return colors[status];
  };

  const handleRuleChange = (field: keyof WaitlistRules, value: any) => {
    setWaitlistRules(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatDate = (date: string | null) => {
    if (!date) return 'NONE';
    return format(new Date(date), 'MM-dd-yyyy');
  };

  const handleEntryClick = (entry: WaitlistEntry) => {
    setSelectedEntry(entry);
    setFormModalOpen(true);
  };

  const handleContextMenu = (event: React.MouseEvent, entry: WaitlistEntry) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX,
      mouseY: event.clientY,
      entry
    });
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  const handleRemoveEntry = (entry: WaitlistEntry) => {
    // Implementation for removing entry
    handleContextMenuClose();
  };

  const handleFormChange = (field: keyof WaitlistFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFormSubmit = () => {
    const newEntry: WaitlistEntry = {
      id: selectedEntry?.id || String(Date.now()), // Generate new ID for new entries
      avatar: selectedEntry?.avatar || `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 70)}.jpg`,
      name: formData.name,
      phone: formData.phone,
      lastPlayedDate: selectedEntry?.lastPlayedDate || null,
      accepted: selectedEntry?.accepted || 0,
      lastWaitlistAccepted: selectedEntry?.lastWaitlistAccepted || format(new Date(), 'yyyy-MM-dd'),
      upcomingPlayDate: selectedEntry?.upcomingPlayDate || null,
      status: formData.status,
      contact: formData.email,
      preferredTime: formData.preferredTime,
      partySize: formData.partySize
    };

    setWaitlistData(prev => {
      if (selectedEntry) {
        // Update existing entry
        return prev.map(entry => entry.id === selectedEntry.id ? newEntry : entry);
      } else {
        // Add new entry
        return [...prev, newEntry];
      }
    });

    setFormModalOpen(false);
    setSelectedEntry(null);
  };

  return (
    <Box sx={{ position: 'relative', minHeight: '400px' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={currentTab} 
          onChange={(_, value) => setCurrentTab(value)}
        >
          <Tab label="Current Waitlist" value="current" />
          <Tab label="Waitlist Rules" value="rules" />
        </Tabs>
      </Box>

      {currentTab === 'current' && (
        <Box>
          <Box sx={{ 
            mb: 3, 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center' 
          }}>
            <Typography variant="h6">Active Waitlist Requests</Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => {
                setSelectedEntry(null);
                setFormModalOpen(true);
              }}
            >
              Add Player
            </Button>
          </Box>
          
          <TableContainer 
            component={Paper}
            sx={{
              borderRadius: 1,
              boxShadow: (theme) => theme.shadows[2],
              overflow: 'hidden',
              width: '100%'
            }}
          >
            <Table sx={{ minWidth: 800 }}>
              <TableHead>
                <TableRow sx={{ 
                  backgroundColor: (theme) => theme.palette.grey[50],
                  '& .MuiTableCell-root': {
                    color: (theme) => theme.palette.text.secondary,
                    fontWeight: 500,
                    textAlign: 'center',
                    padding: '16px'
                  }
                }}>
                  <TableCell sx={{ width: '40px', p: 0.5 }}></TableCell>
                  <TableCell sx={{ pl: 1 }}>Name</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Request Date</TableCell>
                  <TableCell>Preferred Time</TableCell>
                  <TableCell>Party Size</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {waitlistData.map((entry) => (
                  <StyledTableRow 
                    key={entry.id}
                    onClick={() => handleEntryClick(entry)}
                    onContextMenu={(e) => handleContextMenu(e, entry)}
                    sx={{
                      '&:hover': { 
                        backgroundColor: theme => theme.palette.action.hover 
                      },
                      borderBottom: '1px solid',
                      borderColor: 'divider',
                      '& .MuiTableCell-root': {
                        textAlign: 'center',
                        padding: '16px'
                      },
                      '& .MuiTableCell-root:first-of-type': {
                        paddingLeft: '16px'
                      }
                    }}
                  >
                    <TableCell sx={{ width: '40px', p: 0.5 }}>
                      <Box sx={{ position: 'relative' }}>
                        <Avatar 
                          src={entry.avatar}
                          sx={{ width: 32, height: 32 }}
                        />
                        {entry.accepted >= 3 && (
                          <Box
                            component="span"
                            sx={{
                              position: 'absolute',
                              top: -4,
                              left: -8,
                              fontSize: '14px',
                              lineHeight: 1,
                              transform: 'rotate(-15deg)'
                            }}
                          >
                            👑
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ pl: 1 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: 600,
                          color: 'text.primary'
                        }}
                      >
                        {entry.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {entry.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(entry.lastWaitlistAccepted)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {entry.preferredTime || 'Not specified'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {entry.partySize || '1'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={entry.status} 
                        color={getStatusColor(entry.status)} 
                        size="small"
                        sx={{ minWidth: 85 }}
                      />
                    </TableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Menu
            open={contextMenu !== null}
            onClose={handleContextMenuClose}
            anchorReference="anchorPosition"
            anchorPosition={
              contextMenu !== null
                ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
                : undefined
            }
          >
            <MenuItem 
              onClick={() => {
                if (contextMenu?.entry) {
                  handleEntryClick(contextMenu.entry);
                }
                handleContextMenuClose();
              }}
            >
              Edit
            </MenuItem>
            <MenuItem 
              onClick={() => {
                if (contextMenu?.entry) {
                  handleRemoveEntry(contextMenu.entry);
                }
              }}
              sx={{ color: 'error.main' }}
            >
              Remove from Waitlist
            </MenuItem>
          </Menu>
        </Box>
      )}

      {currentTab === 'rules' && (
        <Box sx={{ maxWidth: 800 }}>
          <Alert severity="info" sx={{ mb: 3 }}>
            Configure how the waitlist system behaves for your golf course.
          </Alert>
          
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={waitlistRules.enabled}
                    onChange={(e) => handleRuleChange('enabled', e.target.checked)}
                  />
                }
                label="Enable Waitlist System"
              />

              <Divider />

              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                <TextField
                  label="Maximum Days Ahead"
                  type="number"
                  value={waitlistRules.maxDaysAhead}
                  onChange={(e) => handleRuleChange('maxDaysAhead', parseInt(e.target.value))}
                  disabled={!waitlistRules.enabled}
                />
                
                <TextField
                  label="Auto-Expire After (Days)"
                  type="number"
                  value={waitlistRules.autoExpireDays}
                  onChange={(e) => handleRuleChange('autoExpireDays', parseInt(e.target.value))}
                  disabled={!waitlistRules.enabled}
                />

                <TextField
                  label="Maximum Party Size"
                  type="number"
                  value={waitlistRules.maxPartySize}
                  onChange={(e) => handleRuleChange('maxPartySize', parseInt(e.target.value))}
                  disabled={!waitlistRules.enabled}
                />

                <FormControl fullWidth>
                  <InputLabel>Notification Method</InputLabel>
                  <Select
                    value={waitlistRules.notificationMethod}
                    label="Notification Method"
                    onChange={(e) => handleRuleChange('notificationMethod', e.target.value)}
                    disabled={!waitlistRules.enabled}
                  >
                    <MenuItem value="Email">Email Only</MenuItem>
                    <MenuItem value="SMS">SMS Only</MenuItem>
                    <MenuItem value="Both">Both Email & SMS</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Divider />

              <Box>
                <FormControlLabel
                  control={
                    <Switch 
                      checked={waitlistRules.restrictedTimes.enabled}
                      onChange={(e) => handleRuleChange('restrictedTimes', {
                        ...waitlistRules.restrictedTimes,
                        enabled: e.target.checked
                      })}
                      disabled={!waitlistRules.enabled}
                    />
                  }
                  label="Enable Restricted Times"
                />
                
                <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                  <TextField
                    label="Start Time"
                    type="time"
                    value={waitlistRules.restrictedTimes.startTime}
                    onChange={(e) => handleRuleChange('restrictedTimes', {
                      ...waitlistRules.restrictedTimes,
                      startTime: e.target.value
                    })}
                    disabled={!waitlistRules.enabled || !waitlistRules.restrictedTimes.enabled}
                    InputLabelProps={{ shrink: true }}
                  />
                  
                  <TextField
                    label="End Time"
                    type="time"
                    value={waitlistRules.restrictedTimes.endTime}
                    onChange={(e) => handleRuleChange('restrictedTimes', {
                      ...waitlistRules.restrictedTimes,
                      endTime: e.target.value
                    })}
                    disabled={!waitlistRules.enabled || !waitlistRules.restrictedTimes.enabled}
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
              </Box>

              <FormControlLabel
                control={
                  <Switch 
                    checked={waitlistRules.autoAccept}
                    onChange={(e) => handleRuleChange('autoAccept', e.target.checked)}
                    disabled={!waitlistRules.enabled}
                  />
                }
                label="Auto-Accept When Spots Available"
              />

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                <Button variant="outlined">
                  Reset to Defaults
                </Button>
                <Button variant="contained" color="primary" startIcon={<SaveIcon />}>
                  Save Rules
                </Button>
              </Box>
            </Box>
          </Paper>
        </Box>
      )}

      <Dialog
        open={formModalOpen}
        onClose={() => {
          setFormModalOpen(false);
          setSelectedEntry(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedEntry ? 'Edit Waitlist Entry' : 'Add New Waitlist Entry'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ 
            pt: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2
          }}>
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <TextField
                label="Name"
                fullWidth
                value={formData.name}
                onChange={handleFormChange('name')}
                required
              />
              <TextField
                label="Phone Number"
                fullWidth
                value={formData.phone}
                onChange={handleFormChange('phone')}
                required
              />
            </Box>

            <TextField
              label="Email"
              fullWidth
              value={formData.email}
              onChange={handleFormChange('email')}
              type="email"
              required
            />

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <TextField
                label="Preferred Time"
                type="time"
                fullWidth
                value={formData.preferredTime}
                onChange={handleFormChange('preferredTime')}
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                label="Party Size"
                type="number"
                fullWidth
                value={formData.partySize}
                onChange={handleFormChange('partySize')}
                InputProps={{ 
                  inputProps: { min: 1, max: 8 }
                }}
              />
            </Box>

            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                label="Status"
                onChange={(e: SelectChangeEvent) => handleFormChange('status')(e)}
              >
                <MenuItem value="Pending">Pending</MenuItem>
                <MenuItem value="Notified">Notified</MenuItem>
                <MenuItem value="Accepted">Accepted</MenuItem>
                <MenuItem value="Expired">Expired</MenuItem>
              </Select>
            </FormControl>

            {selectedEntry && (
              <Box sx={{ 
                mt: 2, 
                p: 2, 
                bgcolor: 'grey.50',
                borderRadius: 1
              }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Additional Information
                </Typography>
                <Stack spacing={1}>
                  <Typography variant="body2">
                    Last Play Date: {formatDate(selectedEntry.lastPlayedDate)}
                  </Typography>
                  <Typography variant="body2">
                    Times Accepted: {selectedEntry.accepted}
                  </Typography>
                  <Typography variant="body2">
                    Last Waitlist Accepted: {formatDate(selectedEntry.lastWaitlistAccepted)}
                  </Typography>
                  <Typography variant="body2">
                    Upcoming Play Date: {formatDate(selectedEntry.upcomingPlayDate)}
                  </Typography>
                </Stack>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              setFormModalOpen(false);
              setSelectedEntry(null);
            }}
          >
            Cancel
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            onClick={handleFormSubmit}
          >
            {selectedEntry ? 'Save Changes' : 'Add Player'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}; 