export interface CourseHours {
  open: string;
  close: string;
}

export interface CourseProfileData {
  courseName: string;
  courseAddress: string;
  coursePhone: string;
  courseEmail: string;
  courseWebsite: string;
  courseHours: CourseHours;
  facebookUrl: string;
  googleUrl: string;
  openTableLink: string;
  courseLogo: string;
  courseLogoPreview?: string;
}

export interface ProfileTabProps {
  courseId: string;
}

export interface CourseHierarchyProps {
  courseId: string;
}

export interface HierarchyData {
  corporation: string;
  region: string;
  state: string;
  metro: string;
  course: string;
}

export interface Permission {
  role: string;
  enabled: boolean;
}

export interface User {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
  location: string;
  permissions: {
    [key: string]: Permission;
  };
  lastLogin?: string;
  nextLogin?: string;
  status?: 'Good' | 'Poor' | 'Great';
} 