import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Container,
} from '@mui/material';
import { ProfileTab } from './ProfileTab';
import { UsersPermissionsTab } from './UsersPermissionsTab';
import { WaitlistTab } from './WaitlistTab';

type TabValue = 'profile' | 'users' | 'waitlist' | 'integrations' | 'billing' | 'audit' | 'blank';

interface SettingsPageProps {
  courseId: string;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ courseId }) => {
  const [currentTab, setCurrentTab] = useState<TabValue>('profile');

  const handleTabChange = (_event: React.SyntheticEvent, newValue: TabValue) => {
    setCurrentTab(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Paper sx={{ p: 3 }}>
          {/* Top Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs 
              value={currentTab} 
              onChange={handleTabChange}
            >
              <Tab label="Profile" value="profile" />
              <Tab label="Users & Permissions" value="users" />
              <Tab label="Waitlist" value="waitlist" />
              <Tab label="Integrations" value="integrations" />
              <Tab label="Plan / Billing" value="billing" />
              <Tab label="Audit" value="audit" />
              <Tab label="BLANK" value="blank" />
            </Tabs>
          </Box>

          {/* Tab Content */}
          {currentTab === 'profile' && <ProfileTab courseId={courseId} />}
          {currentTab === 'users' && <UsersPermissionsTab courseId={courseId} />}
          {currentTab === 'waitlist' && <WaitlistTab />}
          {currentTab === 'integrations' && <Typography>Integrations (Coming Soon)</Typography>}
          {currentTab === 'billing' && <Typography>Plan / Billing (Coming Soon)</Typography>}
          {currentTab === 'audit' && <Typography>Audit (Coming Soon)</Typography>}
          {currentTab === 'blank' && <Typography>Blank (Coming Soon)</Typography>}
        </Paper>
      </Box>
    </Container>
  );
};

export default SettingsPage; 