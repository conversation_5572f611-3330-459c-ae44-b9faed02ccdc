import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  SelectChangeEvent,
  IconButton,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface Permission {
  role: string;
  enabled: boolean;
}

interface User {
  id: string;
  name: string;
  role: string;
  lastLogin: string;
  nextLogin: string;
  status: 'Active' | 'Inactive' | 'Pending' | 'Limited';
}

interface UserDetailsPermissionsProps {
  user?: User;
  onClose?: () => void;
  onSubmit?: (formData: any) => void;
}

interface FormData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  role: string;
  location: string;
  status: 'Active' | 'Inactive' | 'Pending' | 'Limited';
  permissions: { [key: string]: Permission };
}

const roleMap: { [key: string]: string } = {
  'General Manager': 'general manager',
  'Golf Course Superintendent': 'golf course superintendent',
  'Head Golf Professional': 'head golf professional',
  'Assistant Golf Professional': 'assistant golf professional',
  'Pro Shop Staff': 'pro shop staff',
  'Food & Beverage Manager': 'food beverage manager',
  'Maintenance Staff': 'maintenance staff'
};

export const UserDetailsPermissions = ({ user, onClose, onSubmit }: UserDetailsPermissionsProps) => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    role: 'staff',
    location: 'Twin Creeks Golf Club',
    status: 'Pending',
    permissions: {
      dashboard: { role: 'General Manager', enabled: true },
      calendar: { role: 'Editor', enabled: true },
      roster: { role: 'Editor', enabled: true },
      proShop: { role: 'Editor', enabled: true },
      '19thHole': { role: 'Editor', enabled: true },
      events: { role: 'Editor', enabled: true },
      analytics: { role: 'Editor', enabled: true },
      backOffice: { role: 'Editor', enabled: true },
      settings: { role: 'Editor', enabled: true }
    }
  });

  const [errors, setErrors] = useState<Partial<FormData>>({});

  useEffect(() => {
    if (user) {
      const [firstName, lastName] = user.name.split(' ');
      setFormData(prev => ({
        ...prev,
        firstName,
        lastName,
        role: roleMap[user.role] || 'staff',
        status: user.status,
        phone: '************', // This would come from user data in a real app
        email: '<EMAIL>', // This would come from user data in a real app
      }));
    }
  }, [user]);

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | { value: unknown }>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSelectChange = (field: keyof FormData) => (
    event: SelectChangeEvent
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleRoleChange = (section: string) => (event: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [section]: { ...prev.permissions[section], role: event.target.value }
      }
    }));
  };

  const handleToggleChange = (section: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [section]: { ...prev.permissions[section], enabled: event.target.checked }
      }
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (validateForm() && onSubmit) {
      const selectedRole = Object.entries(roleMap).find(([_, value]) => value === formData.role)?.[0] || 'Staff';
      onSubmit({
        ...formData,
        name: `${formData.firstName} ${formData.lastName}`,
        role: selectedRole,
        status: formData.status
      });
      if (onClose) onClose();
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          {user ? 'Edit User Details & Permissions' : 'Add New User'}
        </Typography>
        {onClose && (
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        )}
      </Box>
      
      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Please fix the errors in the form before submitting.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Left Column - User Details */}
        <Grid item xs={12} md={4} sx={{ 
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          pr: { md: 4 }
        }}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 3, 
              color: 'text.secondary',
              fontWeight: 500,
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
              fontSize: '0.875rem'
            }}
          >
            User Information
          </Typography>
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: 2,
            width: '100%'  // Ensure full width of the column
          }}>
            <TextField
              fullWidth
              label="First Name"
              value={formData.firstName}
              onChange={handleInputChange('firstName')}
              error={!!errors.firstName}
              helperText={errors.firstName}
              required
            />
            <TextField
              fullWidth
              label="Last Name"
              value={formData.lastName}
              onChange={handleInputChange('lastName')}
              error={!!errors.lastName}
              helperText={errors.lastName}
              required
            />
            <TextField
              fullWidth
              label="Phone Number"
              value={formData.phone}
              onChange={handleInputChange('phone')}
              error={!!errors.phone}
              helperText={errors.phone}
              required
            />
            <TextField
              fullWidth
              label="Email Address"
              value={formData.email}
              onChange={handleInputChange('email')}
              error={!!errors.email}
              helperText={errors.email}
              required
            />
            <FormControl fullWidth>
              <InputLabel>Role / Permission</InputLabel>
              <Select
                value={formData.role}
                label="Role / Permission"
                onChange={handleSelectChange('role')}
              >
                <MenuItem value="general manager">General Manager</MenuItem>
                <MenuItem value="golf course superintendent">Golf Course Superintendent</MenuItem>
                <MenuItem value="head golf professional">Head Golf Professional</MenuItem>
                <MenuItem value="assistant golf professional">Assistant Golf Professional</MenuItem>
                <MenuItem value="pro shop staff">Pro Shop Staff</MenuItem>
                <MenuItem value="food beverage manager">Food & Beverage Manager</MenuItem>
                <MenuItem value="maintenance staff">Maintenance Staff</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                label="Status"
                onChange={handleSelectChange('status')}
              >
                <MenuItem value="Active">Active</MenuItem>
                <MenuItem value="Inactive">Inactive</MenuItem>
                <MenuItem value="Pending">Pending</MenuItem>
                <MenuItem value="Limited">Limited</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Location / Level"
              value={formData.location}
              onChange={handleInputChange('location')}
            />
          </Box>
        </Grid>

        {/* Right Column - Permissions - Now wider */}
        <Grid item xs={12} md={8} sx={{ 
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          pl: { md: 4 }
        }}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 3, 
              color: 'text.secondary',
              fontWeight: 500,
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
              fontSize: '0.875rem'
            }}
          >
            Module Access & Permissions
          </Typography>
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: 2,
            width: '100%'  // Take full width of the larger column
          }}>
            {Object.entries(formData.permissions).map(([section, permission]) => (
              <Box
                key={section}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  py: 0.5,
                  width: '100%',
                  opacity: permission.enabled ? 1 : 0.6,
                  '& .MuiTypography-root': {
                    minWidth: '140px',
                    fontSize: '0.875rem',
                    color: permission.enabled ? 'text.primary' : 'text.disabled'
                  },
                  '& .MuiFormControl-root': {
                    flex: 1
                  },
                  '& .MuiFormControlLabel-root': {
                    margin: 0,
                    marginLeft: 2
                  }
                }}
              >
                <Typography>
                  {section.charAt(0).toUpperCase() + section.slice(1)}
                </Typography>
                <FormControl size="small" fullWidth>
                  <InputLabel sx={{ 
                    color: permission.enabled ? undefined : 'text.disabled' 
                  }}>
                    {section} Role
                  </InputLabel>
                  <Select
                    value={permission.role}
                    onChange={handleRoleChange(section)}
                    label={`${section} Role`}
                    disabled={!permission.enabled}
                  >
                    <MenuItem value="General Manager">General Manager</MenuItem>
                    <MenuItem value="Editor">Editor</MenuItem>
                    <MenuItem value="Viewer">Viewer</MenuItem>
                  </Select>
                </FormControl>
                <FormControlLabel
                  control={
                    <Switch
                      checked={permission.enabled}
                      onChange={handleToggleChange(section)}
                      size="small"
                    />
                  }
                  label=""
                  sx={{ ml: 1, mr: 0 }}
                />
              </Box>
            ))}
          </Box>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button variant="outlined" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" variant="contained" color="primary">
          {user ? 'Save Changes' : 'Create User'}
        </Button>
      </Box>
    </Box>
  );
}; 