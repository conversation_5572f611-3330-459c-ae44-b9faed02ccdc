import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Box,
  TextField,
  Button,
  Typography,
  Grid,
  FormControl,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  IconButton,
  SelectChangeEvent
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { User, Permission } from './types';

interface UserFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: User) => void;
  user: User | null;
}

const UserFormModal: React.FC<UserFormModalProps> = ({
  open,
  onClose,
  onSubmit,
  user
}) => {
  const [formData, setFormData] = useState<User>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: 'Staff',
    location: '',
    permissions: {
      dashboard: { role: 'Viewer', enabled: true },
      calendar: { role: 'Viewer', enabled: true },
      roster: { role: 'Viewer', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  });

  useEffect(() => {
    if (user) {
      setFormData(user);
    } else {
      // Reset form for new user
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        role: 'Staff',
        location: '',
        permissions: {
          dashboard: { role: 'Viewer', enabled: true },
          calendar: { role: 'Viewer', enabled: true },
          roster: { role: 'Viewer', enabled: true },
          proShop: { role: 'Viewer', enabled: true },
          '19thHole': { role: 'Viewer', enabled: true },
          events: { role: 'Viewer', enabled: true },
          analytics: { role: 'Viewer', enabled: true },
          backOffice: { role: 'Viewer', enabled: true },
          settings: { role: 'Viewer', enabled: true }
        }
      });
    }
  }, [user]);

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRoleChange = (section: string) => (event: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [section]: { ...prev.permissions[section], role: event.target.value }
      }
    }));
  };

  const handleToggleChange = (section: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [section]: { ...prev.permissions[section], enabled: event.target.checked }
      }
    }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        borderBottom: 1,
        borderColor: 'divider',
        pb: 2
      }}>
        <Typography variant="h6">
          {user ? 'Edit User' : 'Add New User'}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* User Details */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleTextChange}
              />
              <TextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleTextChange}
              />
              <TextField
                fullWidth
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleTextChange}
              />
              <TextField
                fullWidth
                label="Email Address"
                name="email"
                value={formData.email}
                onChange={handleTextChange}
              />
              <FormControl fullWidth>
                <Select
                  value={formData.role}
                  onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                >
                  <MenuItem value="Administrator">Administrator</MenuItem>
                  <MenuItem value="General Manager">General Manager</MenuItem>
                  <MenuItem value="Staff">Staff</MenuItem>
                </Select>
              </FormControl>
              <TextField
                fullWidth
                label="Location"
                name="location"
                value={formData.location}
                onChange={handleTextChange}
              />
            </Box>
          </Grid>

          {/* Permissions */}
          <Grid item xs={12} md={8}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>Permissions</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {Object.entries(formData.permissions).map(([section, permission]) => (
                <Box
                  key={section}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 2
                  }}
                >
                  <Typography sx={{ minWidth: 100 }}>
                    {section.charAt(0).toUpperCase() + section.slice(1)}:
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <Select
                      value={permission.role}
                      onChange={handleRoleChange(section)}
                    >
                      <MenuItem value="General Manager">General Manager</MenuItem>
                      <MenuItem value="Editor">Editor</MenuItem>
                      <MenuItem value="Viewer">Viewer</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={permission.enabled}
                        onChange={handleToggleChange(section)}
                      />
                    }
                    label=""
                  />
                </Box>
              ))}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={handleSubmit} variant="contained">
          {user ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserFormModal; 