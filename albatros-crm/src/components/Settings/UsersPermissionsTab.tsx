import React, { useState } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Select,
  MenuItem,
  FormControl,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import UserFormModal from './UserFormModal';
import { User } from './types';

interface UsersPermissionsTabProps {
  courseId: string;
}

const mockUsers: User[] = [
  {
    id: '1',
    firstName: 'Miranda',
    lastName: 'Helms',
    email: '<EMAIL>',
    phone: '************',
    role: 'General Manager',
    location: 'Twin Creeks Golf Club',
    lastLogin: '10/12/2024',
    nextLogin: '11/12/2024',
    status: 'Good',
    permissions: {
      dashboard: { role: 'General Manager', enabled: true },
      calendar: { role: 'Editor', enabled: true },
      roster: { role: 'Editor', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  },
  {
    id: '2',
    firstName: 'Kyle',
    lastName: 'Eastwood',
    email: '<EMAIL>',
    phone: '************',
    role: 'Superintendent',
    location: 'Twin Creeks Golf Club',
    lastLogin: '10/22/2024',
    nextLogin: '11/12/2024',
    status: 'Good',
    permissions: {
      dashboard: { role: 'Viewer', enabled: true },
      calendar: { role: 'Viewer', enabled: true },
      roster: { role: 'Viewer', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  },
  {
    id: '3',
    firstName: 'Jennifer',
    lastName: 'Lopez',
    email: '<EMAIL>',
    phone: '************',
    role: 'Beverage Specialist',
    location: 'Twin Creeks Golf Club',
    lastLogin: '10/17/2024',
    nextLogin: '11/14/2024',
    status: 'Poor',
    permissions: {
      dashboard: { role: 'Viewer', enabled: true },
      calendar: { role: 'Viewer', enabled: true },
      roster: { role: 'Viewer', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  },
  {
    id: '4',
    firstName: 'Scottie',
    lastName: 'Woods',
    email: '<EMAIL>',
    phone: '************',
    role: 'Grounds Keeper',
    location: 'Twin Creeks Golf Club',
    lastLogin: '10/29/2024',
    nextLogin: '11/17/2024',
    status: 'Great',
    permissions: {
      dashboard: { role: 'Viewer', enabled: true },
      calendar: { role: 'Viewer', enabled: true },
      roster: { role: 'Viewer', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  },
  {
    id: '5',
    firstName: 'Drake',
    lastName: 'Green',
    email: '<EMAIL>',
    phone: '************',
    role: 'Front Desk',
    location: 'Twin Creeks Golf Club',
    lastLogin: '10/29/2024',
    nextLogin: '11/17/2024',
    status: 'Great',
    permissions: {
      dashboard: { role: 'Viewer', enabled: true },
      calendar: { role: 'Viewer', enabled: true },
      roster: { role: 'Viewer', enabled: true },
      proShop: { role: 'Viewer', enabled: true },
      '19thHole': { role: 'Viewer', enabled: true },
      events: { role: 'Viewer', enabled: true },
      analytics: { role: 'Viewer', enabled: true },
      backOffice: { role: 'Viewer', enabled: true },
      settings: { role: 'Viewer', enabled: true }
    }
  }
];

export const UsersPermissionsTab: React.FC<UsersPermissionsTabProps> = ({ courseId }) => {
  const [timeFilter, setTimeFilter] = useState('Next 30 days');
  const [roleFilter, setRoleFilter] = useState('Role');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Good':
        return 'primary';
      case 'Poor':
        return 'error';
      case 'Great':
        return 'success';
      default:
        return 'default';
    }
  };

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedUser(null);
    setIsModalOpen(false);
  };

  const handleCreateUser = () => {
    setSelectedUser(null);
    setIsModalOpen(true);
  };

  return (
    <Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 3 
      }}>
        <Typography variant="h6">User List</Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              displayEmpty
            >
              <MenuItem value="Role">Role</MenuItem>
              <MenuItem value="manager">Manager</MenuItem>
              <MenuItem value="staff">Staff</MenuItem>
              <MenuItem value="admin">Admin</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              displayEmpty
            >
              <MenuItem value="Next 30 days">Next 30 days</MenuItem>
              <MenuItem value="Next 60 days">Next 60 days</MenuItem>
              <MenuItem value="Next 90 days">Next 90 days</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="primary"
            onClick={handleCreateUser}
          >
            Create Users
          </Button>
        </Box>
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Last Log-In</TableCell>
              <TableCell>Next Log-In</TableCell>
              <TableCell align="right">Status</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {mockUsers.map((user) => (
              <TableRow 
                key={user.id} 
                hover
                onClick={() => handleUserClick(user)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>{user.firstName} {user.lastName}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.lastLogin}</TableCell>
                <TableCell>{user.nextLogin}</TableCell>
                <TableCell align="right">
                  <Chip 
                    label={user.status}
                    color={getStatusColor(user.status || '')}
                    size="small"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <UserFormModal
        open={isModalOpen}
        onClose={handleCloseModal}
        user={selectedUser}
        onSubmit={(userData: User) => {
          console.log('Submitting user data:', userData);
          handleCloseModal();
        }}
      />
    </Box>
  );
}; 