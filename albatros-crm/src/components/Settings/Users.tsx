import React, { useState } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Typography,
  Chip
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import UserFormModal from './UserFormModal';
import { User } from './types';

interface UsersProps {
  courseId: string;
}

export const Users: React.FC<UsersProps> = ({ courseId }) => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Mock data - replace with actual data fetching
  const [users] = useState<User[]>([
    {
      id: '1',
      firstName: 'Miranda',
      lastName: 'Helms',
      email: '<EMAIL>',
      phone: '************',
      role: 'General Manager',
      location: 'Twin Creeks Golf Club',
      permissions: {
        dashboard: { role: 'General Manager', enabled: true },
        calendar: { role: 'Editor', enabled: true },
        roster: { role: 'Editor', enabled: true },
        proShop: { role: 'Viewer', enabled: true },
        '19thHole': { role: 'Viewer', enabled: true },
        events: { role: 'Viewer', enabled: true },
        analytics: { role: 'Viewer', enabled: true },
        backOffice: { role: 'Viewer', enabled: true },
        settings: { role: 'Viewer', enabled: true }
      }
    },
    // ... other users
  ]);

  const handleAddUser = () => {
    setSelectedUser(null);
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  const handleSubmit = (userData: User) => {
    // Handle user creation/update logic here
    console.log('Submitting user data:', userData);
    handleCloseModal();
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Users & Permissions</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
          sx={{ textTransform: 'none' }}
        >
          Add User
        </Button>
      </Box>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Status</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow
                key={user.id}
                hover
                onClick={() => handleEditUser(user)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>
                  {user.firstName} {user.lastName}
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.location}</TableCell>
                <TableCell>
                  <Chip
                    label="Active"
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* User Form Modal */}
      <UserFormModal
        open={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        user={selectedUser}
      />
    </Box>
  );
}; 