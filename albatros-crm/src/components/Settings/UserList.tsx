import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Select,
  MenuItem,
  FormControl,
  Chip,
  Dialog,
  Menu,
  ListItemIcon,
  ListItemText,
  Tooltip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { UserDetailsPermissions } from './UserDetailsPermissions';

interface User {
  id: string;
  name: string;
  role: string;
  lastLogin: string;
  nextLogin: string;
  status: 'Active' | 'Inactive' | 'Pending' | 'Limited';
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'General Manager',
    lastLogin: '10/12/2024',
    nextLogin: '11/12/2024',
    status: 'Active'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Golf Course Superintendent',
    lastLogin: '10/22/2024',
    nextLogin: '11/12/2024',
    status: 'Active'
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Head Golf Professional',
    lastLogin: '10/17/2024',
    nextLogin: '11/14/2024',
    status: 'Limited'
  },
  {
    id: '4',
    name: 'Scottie Woods',
    role: 'Assistant Golf Professional',
    lastLogin: '10/29/2024',
    nextLogin: '11/17/2024',
    status: 'Active'
  },
  {
    id: '5',
    name: 'Drake Green',
    role: 'Pro Shop Staff',
    lastLogin: '10/29/2024',
    nextLogin: '11/17/2024',
    status: 'Pending'
  },
  {
    id: '6',
    name: 'Sarah Johnson',
    role: 'Food & Beverage Manager',
    lastLogin: '10/25/2024',
    nextLogin: '11/20/2024',
    status: 'Active'
  },
  {
    id: '7',
    name: 'Mike Thompson',
    role: 'Maintenance Staff',
    lastLogin: '10/30/2024',
    nextLogin: '11/18/2024',
    status: 'Inactive'
  }
];

const roleDisplayMap: { [key: string]: string } = {
  'sort': 'Sort',
  'general manager': 'General Manager',
  'golf course superintendent': 'Golf Course Superintendent',
  'head golf professional': 'Head Golf Professional',
  'assistant golf professional': 'Assistant Golf Professional',
  'pro shop staff': 'Pro Shop Staff',
  'food beverage manager': 'Food & Beverage Manager',
  'maintenance staff': 'Maintenance Staff'
};

// Add status descriptions
const statusDescriptions: { [key: string]: string } = {
  'Active': 'User has full access to their assigned permissions',
  'Inactive': 'Account is disabled - no system access',
  'Pending': 'New user awaiting account activation',
  'Limited': 'Temporary or restricted access only'
};

export const UserList = () => {
  const [timeFilter, setTimeFilter] = useState('Next 30 days');
  const [roleFilter, setRoleFilter] = useState('sort');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [roleAnchorEl, setRoleAnchorEl] = useState<null | HTMLElement>(null);
  const [timeAnchorEl, setTimeAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    // In a real application, you would fetch users based on courseId
    // For now, we'll use the mock data
    setUsers(mockUsers);
  }, []);

  const handleRowClick = (user: User) => {
    setSelectedUser(user);
    setOpenDialog(true);
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
  };

  const handleSubmitUser = (formData: any) => {
    if (selectedUser) {
      // Update existing user
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === selectedUser.id 
            ? { ...user, name: formData.name, role: formData.role }
            : user
        )
      );
    } else {
      // Add new user
      const newUser: User = {
        id: (users.length + 1).toString(),
        name: formData.name,
        role: formData.role,
        lastLogin: new Date().toLocaleDateString(),
        nextLogin: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        status: 'Pending' // New users start with Pending status
      };
      setUsers(prevUsers => [...prevUsers, newUser]);
    }
    handleCloseDialog();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'success';
      case 'Inactive':
        return 'error';
      case 'Pending':
        return 'warning';
      case 'Limited':
        return 'info';
      default:
        return 'default';
    }
  };

  const filteredUsers = users.filter(user => {
    // Filter by role
    if (roleFilter !== 'sort' && user.role.toLowerCase() !== roleFilter.toLowerCase()) {
      return false;
    }

    // Filter by time (this is a mock implementation)
    const nextLoginDate = new Date(user.nextLogin);
    const today = new Date();
    const daysUntilNextLogin = Math.ceil((nextLoginDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (timeFilter === 'Next 30 days' && daysUntilNextLogin > 30) return false;
    if (timeFilter === 'Next 60 days' && daysUntilNextLogin > 60) return false;
    if (timeFilter === 'Next 90 days' && daysUntilNextLogin > 90) return false;

    return true;
  });

  const handleRoleClick = (event: React.MouseEvent<HTMLElement>) => {
    setRoleAnchorEl(event.currentTarget);
  };

  const handleTimeClick = (event: React.MouseEvent<HTMLElement>) => {
    setTimeAnchorEl(event.currentTarget);
  };

  const handleRoleClose = () => {
    setRoleAnchorEl(null);
  };

  const handleTimeClose = () => {
    setTimeAnchorEl(null);
  };

  const handleRoleSelect = (role: string) => {
    setRoleFilter(role);
    handleRoleClose();
  };

  const handleTimeSelect = (time: string) => {
    setTimeFilter(time);
    handleTimeClose();
  };

  return (
    <Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 3 
      }}>
        <Typography variant="h6">User List</Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            endIcon={<KeyboardArrowDownIcon />}
            onClick={handleRoleClick}
            sx={{ textTransform: 'none' }}
          >
            {roleDisplayMap[roleFilter]}
          </Button>

          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            endIcon={<KeyboardArrowDownIcon />}
            onClick={handleTimeClick}
            sx={{ textTransform: 'none' }}
          >
            {timeFilter}
          </Button>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="primary"
            onClick={handleAddUser}
          >
            Create User
          </Button>
        </Box>
      </Box>

      <Menu
        anchorEl={roleAnchorEl}
        open={Boolean(roleAnchorEl)}
        onClose={handleRoleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleRoleSelect('sort')}>
          <ListItemText primary="Sort" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('general manager')}>
          <ListItemText primary="General Manager" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('golf course superintendent')}>
          <ListItemText primary="Golf Course Superintendent" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('head golf professional')}>
          <ListItemText primary="Head Golf Professional" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('assistant golf professional')}>
          <ListItemText primary="Assistant Golf Professional" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('pro shop staff')}>
          <ListItemText primary="Pro Shop Staff" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('food beverage manager')}>
          <ListItemText primary="Food & Beverage Manager" />
        </MenuItem>
        <MenuItem onClick={() => handleRoleSelect('maintenance staff')}>
          <ListItemText primary="Maintenance Staff" />
        </MenuItem>
      </Menu>

      <Menu
        anchorEl={timeAnchorEl}
        open={Boolean(timeAnchorEl)}
        onClose={handleTimeClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleTimeSelect('Next 30 days')}>
          <ListItemText primary="Next 30 days" />
        </MenuItem>
        <MenuItem onClick={() => handleTimeSelect('Next 60 days')}>
          <ListItemText primary="Next 60 days" />
        </MenuItem>
        <MenuItem onClick={() => handleTimeSelect('Next 90 days')}>
          <ListItemText primary="Next 90 days" />
        </MenuItem>
      </Menu>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Last Log-In</TableCell>
              <TableCell>Next Log-In</TableCell>
              <TableCell align="right">Status</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow 
                key={user.id} 
                hover 
                onClick={() => handleRowClick(user)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>{user.name}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.lastLogin}</TableCell>
                <TableCell>{user.nextLogin}</TableCell>
                <TableCell align="right">
                  <Tooltip 
                    title={statusDescriptions[user.status]} 
                    arrow
                    placement="left"
                  >
                  <Chip 
                    label={user.status}
                    color={getStatusColor(user.status)}
                    size="small"
                  />
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <Box sx={{ p: 3 }}>
          <UserDetailsPermissions 
            user={selectedUser || undefined} 
            onClose={handleCloseDialog}
            onSubmit={handleSubmitUser}
          />
        </Box>
      </Dialog>
    </Box>
  );
}; 