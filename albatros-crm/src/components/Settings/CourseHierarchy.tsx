import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  styled,
  useTheme,
  TextField,
  IconButton,
  Tooltip,
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Close as CloseIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';

const HierarchyNode = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(1.5, 2.5),
  position: 'relative',
  border: `1px solid ${theme.palette.divider}`,
  transition: 'all 0.2s ease-in-out',
  cursor: 'default',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  minWidth: '300px',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[2],
  }
}));

const ArrowConnector = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '50%',
  transform: 'translateX(-50%)',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  color: theme.palette.primary.main,
  opacity: 0.5,
  '& .MuiSvgIcon-root': {
    fontSize: '24px',
  }
}));

const HierarchyLevel = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  position: 'relative',
  marginBottom: theme.spacing(6),
  '&:last-child': {
    marginBottom: 0,
  }
}));

interface HierarchyItem {
  id: string;
  name: string;
  level: 'corporation' | 'region' | 'state' | 'metro' | 'course';
  parentId?: string;
}

interface CourseHierarchyProps {
  courseId: string;
}

const defaultHierarchy = {
  corporation: 'New Age Golf Corp',
  region: 'South US Region',
  state: 'State of Texas',
  metro: 'Dallas / Fort Worth Metroplex',
  course: 'Twin Creeks Golf Course'
};

const LEVEL_LABELS = {
  corporation: 'Corporation',
  region: 'Region',
  state: 'State',
  metro: 'Metro',
  course: 'Course'
};

export const CourseHierarchy: React.FC<CourseHierarchyProps> = ({ courseId }) => {
  const theme = useTheme();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [tempName, setTempName] = useState('');
  const [hierarchyItems, setHierarchyItems] = useState<HierarchyItem[]>([
    { id: '1', name: defaultHierarchy.corporation, level: 'corporation' },
    { id: '2', name: defaultHierarchy.region, level: 'region', parentId: '1' },
    { id: '3', name: defaultHierarchy.state, level: 'state', parentId: '2' },
    { id: '4', name: defaultHierarchy.metro, level: 'metro', parentId: '3' },
    { id: '5', name: defaultHierarchy.course, level: 'course', parentId: '4' },
  ]);

  const handleEditStart = (item: HierarchyItem) => {
    setEditingItemId(item.id);
    setTempName(item.name);
  };

  const handleEditCancel = () => {
    setEditingItemId(null);
    setTempName('');
  };

  const handleEditSave = (item: HierarchyItem) => {
    if (tempName.trim()) {
      setHierarchyItems(items =>
        items.map(i => i.id === item.id ? { ...i, name: tempName.trim() } : i)
      );
    }
    setEditingItemId(null);
    setTempName('');
  };

  const handleAddLayer = (level: HierarchyItem['level'], parentId?: string, currentIndex?: number) => {
    const newItem: HierarchyItem = {
      id: String(Date.now()),
      name: `New ${LEVEL_LABELS[level]}`,
      level,
      parentId,
    };

    setHierarchyItems(prev => {
      const insertIndex = typeof currentIndex === 'number' ? currentIndex + 1 : prev.length;
      const newItems = [...prev];
      newItems.splice(insertIndex, 0, newItem);
      return newItems;
    });
    
    handleEditStart(newItem);
  };

  const handleDeleteLayer = (itemId: string) => {
    // Prevent deletion of corporation
    const item = hierarchyItems.find(i => i.id === itemId);
    if (item?.level === 'corporation') return;
    
    setHierarchyItems(items => items.filter(item => item.id !== itemId));
  };

  const renderHierarchyNode = (item: HierarchyItem) => {
    const isEditing = editingItemId === item.id;
    const isCorporation = item.level === 'corporation';

    const handleBlur = () => {
      if (tempName.trim()) {
        handleEditSave(item);
      }
    };

    return (
      <HierarchyNode
        onClick={() => {
          if (isEditMode && !isEditing) {
            handleEditStart(item);
          }
        }}
        sx={{ 
          ...(isCorporation && {
            bgcolor: isEditMode ? 'primary.dark' : 'primary.main',
            color: 'primary.contrastText',
            '&:hover': {
              bgcolor: isEditMode ? 'primary.main' : 'primary.dark',
              color: 'primary.contrastText',
            }
          }),
          ...(item.level === 'course' && {
            border: '2px solid',
            borderColor: 'primary.main',
            bgcolor: 'primary.lighter',
            '&:hover': {
              bgcolor: 'primary.light',
            }
          }),
          ...(isEditMode && !isCorporation && {
            border: `1px solid ${theme.palette.divider}`,
            cursor: 'pointer',
            '&:hover': {
              borderColor: theme.palette.primary.main,
            }
          })
        }}
      >
        {isEditing ? (
          <>
            <TextField
              size="small"
              value={tempName}
              onChange={(e) => setTempName(e.target.value)}
              onBlur={handleBlur}
              autoFocus
              sx={{ 
                minWidth: 200,
                '& .MuiInputBase-root': {
                  color: isCorporation ? 'primary.contrastText' : 'inherit',
                  bgcolor: isCorporation ? 'primary.main' : 'inherit',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: isCorporation ? 'primary.contrastText' : 'inherit',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: isCorporation ? 'primary.contrastText' : 'inherit',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: isCorporation ? 'primary.contrastText' : 'primary.main',
                  }
                }
              }}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleBlur();
                } else if (e.key === 'Escape') {
                  handleEditCancel();
                }
              }}
            />
            <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
              <IconButton 
                size="small" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditCancel();
                }}
                sx={isCorporation ? {
                  color: 'primary.contrastText',
                  '&:hover': {
                    bgcolor: 'primary.light',
                  }
                } : {}}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          </>
        ) : (
          <>
            <Typography 
              variant="subtitle1" 
              color={isCorporation ? 'primary.contrastText' : 'text.primary'}
              fontWeight={500}
              sx={{ flex: 1 }}
            >
              {item.name}
            </Typography>
            {isEditMode && !isCorporation && (
              <Box 
                sx={{ 
                  display: 'flex', 
                  gap: 1, 
                  opacity: 0.7,
                  visibility: 'hidden',
                  transition: 'visibility 0.2s ease-in-out, opacity 0.2s ease-in-out',
                  '.MuiBox-root:hover &': {
                    visibility: 'visible',
                    opacity: 1,
                  }
                }}
              >
                <Tooltip title="Delete">
                  <IconButton 
                    size="small" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteLayer(item.id);
                    }} 
                    color="error"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </>
        )}
      </HierarchyNode>
    );
  };

  return (
    <Paper 
      sx={{ 
        p: 3, 
        mt: 4,
        position: 'relative',
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 500 }}>
          Course Hierarchy
        </Typography>
        <Button
          variant="contained"
          startIcon={isEditMode ? <CloseIcon /> : <EditIcon />}
          onClick={() => setIsEditMode(!isEditMode)}
          sx={{ 
            bgcolor: isEditMode ? 'error.main' : 'primary.main',
            '&:hover': {
              bgcolor: isEditMode ? 'error.dark' : 'primary.dark',
            },
            textTransform: 'none',
          }}
        >
          {isEditMode ? 'Exit Edit Mode' : 'Edit Hierarchy'}
        </Button>
      </Box>

      <Box sx={{ position: 'relative', py: 2 }}>
        {hierarchyItems.map((item, index) => (
          <HierarchyLevel key={item.id}>
            {index > 0 && (
              <ArrowConnector sx={{ top: '-40px' }}>
                <ArrowDownIcon />
              </ArrowConnector>
            )}
            {renderHierarchyNode(item)}
            {isEditMode && index < 4 && !item.level.includes('corporation') && (
              <Tooltip title={`Add ${LEVEL_LABELS[Object.keys(LEVEL_LABELS)[index + 1] as keyof typeof LEVEL_LABELS]}`}>
                <IconButton 
                  size="small"
                  onClick={() => handleAddLayer(
                    Object.keys(LEVEL_LABELS)[index + 1] as HierarchyItem['level'],
                    item.id,
                    index
                  )}
                  sx={{ 
                    position: 'absolute',
                    bottom: '-36px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    bgcolor: 'background.paper',
                    border: `1px solid ${theme.palette.divider}`,
                    zIndex: 1,
                    '&:hover': {
                      bgcolor: 'action.hover',
                    }
                  }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </HierarchyLevel>
        ))}
      </Box>
    </Paper>
  );
};

export default CourseHierarchy; 