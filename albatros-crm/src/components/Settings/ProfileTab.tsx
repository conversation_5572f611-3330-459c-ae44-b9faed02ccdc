import {
    Alert,
    Avatar,
    Box,
    Button,
    CircularProgress,
    FormControl,
    FormHelperText,
    Grid,
    InputLabel,
    OutlinedInput,
    Paper,
    Stack,
    Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { CourseHierarchy } from './CourseHierarchy';
import { CourseHours, CourseProfileData, ProfileTabProps } from './types';

const defaultProfileData: CourseProfileData = {
  courseName: 'Twin Creeks Golf Course',
  courseAddress: '501 Twin Creeks Drive, Allen, TX 75013',
  coursePhone: '(*************',
  courseEmail: '<EMAIL>',
  courseWebsite: 'www.twincreeksgc.com',
  courseHours: {
    open: '06:00',
    close: '20:00'
  },
  facebookUrl: 'https://facebook.com/twincreeksgc',
  googleUrl: 'https://g.page/twincreeksgc',
  openTableLink: 'https://www.opentable.com/r/twin-creeks-golf-course',
  courseLogo: '',
  courseLogoPreview: ''
};

export const ProfileTab: React.FC<ProfileTabProps> = ({ courseId }) => {
  const [profileData, setProfileData] = useState<CourseProfileData>(defaultProfileData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);

  // Cleanup preview URL when component unmounts
  useEffect(() => {
    return () => {
      if (profileData.courseLogoPreview) {
        URL.revokeObjectURL(profileData.courseLogoPreview);
      }
    };
  }, [profileData.courseLogoPreview]);

  const isValidEmail = (email: string): boolean => {
    if (!email) return false;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const isValidUrl = (url: string): boolean => {
    if (!url) return true; // URLs are optional
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  const isValidPhone = (phone: string): boolean => {
    if (!phone) return false;
    const digitsOnly = phone.replace(/\D/g, '');
    return digitsOnly.length === 10;
  };

  const getFieldError = (field: keyof CourseProfileData): string => {
    if (field === 'courseHours') {
      return ''; // Operating hours validation is handled separately
    }

    const value = profileData[field];
    
    // Skip validation for courseHours since it's handled separately
    if (typeof value !== 'string') {
      return '';
    }

    switch (field) {
      case 'courseName':
        return !value.trim() ? 'Course name is required' : '';
      case 'courseAddress':
        return !value.trim() ? 'Address is required' : '';
      case 'courseEmail':
        return !isValidEmail(value) ? 'Please enter a valid email address' : '';
      case 'coursePhone':
        return !isValidPhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'courseWebsite':
        return value && !isValidUrl(value) ? 'Please enter a valid URL' : '';
      case 'facebookUrl':
        return value && !isValidUrl(value) ? 'Please enter a valid Facebook URL' : '';
      case 'googleUrl':
        return value && !isValidUrl(value) ? 'Please enter a valid Google URL' : '';
      case 'openTableLink':
        return value && !isValidUrl(value) ? 'Please enter a valid OpenTable URL' : '';
      default:
        return '';
    }
  };

  const isValidTimeFormat = (time: string): boolean => {
    return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
  };

  const isFormValid = (): boolean => {
    const {
      courseName = '',
      courseAddress = '',
      courseEmail = '',
      coursePhone = '',
      courseWebsite = '',
      courseHours,
      facebookUrl = '',
      googleUrl = '',
      openTableLink = ''
    } = profileData;

    const isHoursValid = isValidTimeFormat(courseHours.open) && isValidTimeFormat(courseHours.close);

    return (
      !!courseName.trim() &&
      !!courseAddress.trim() &&
      isValidEmail(courseEmail) &&
      isValidPhone(coursePhone) &&
      (!courseWebsite || isValidUrl(courseWebsite)) &&
      (!facebookUrl || isValidUrl(facebookUrl)) &&
      (!googleUrl || isValidUrl(googleUrl)) &&
      (!openTableLink || isValidUrl(openTableLink)) &&
      isHoursValid
    );
  };

  const handleProfileChange = (field: keyof CourseProfileData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsDirty(true);
    setProfileData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleTimeChange = (type: keyof CourseHours) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsDirty(true);
    setProfileData(prev => ({
      ...prev,
      courseHours: {
        ...prev.courseHours,
        [type]: event.target.value
      }
    }));
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Revoke the old preview URL if it exists
      if (profileData.courseLogoPreview) {
        URL.revokeObjectURL(profileData.courseLogoPreview);
      }

      // Create new preview URL
      const previewUrl = URL.createObjectURL(file);

      setProfileData(prev => ({
        ...prev,
        courseLogo: file.name,
        courseLogoPreview: previewUrl
      }));
      setIsDirty(true);
    }
  };

  const handleSaveProfile = async () => {
    if (!isFormValid()) return;
    
    setIsSubmitting(true);
    setError(null);

    try {
      // TODO: Implement API call to save profile data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      console.log('Saving profile data:', profileData);
      setIsDirty(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving the profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTextField = (
    field: keyof CourseProfileData,
    label: string,
    required: boolean = true
  ) => {
    const error = getFieldError(field);
    
    return (
      <Box sx={{ mb: 3 }}>
        <FormControl 
          fullWidth 
          error={isDirty && !!error}
        >
          <InputLabel htmlFor={field}>{label}{required && ' *'}</InputLabel>
          <OutlinedInput
            id={field}
            value={profileData[field]}
            onChange={handleProfileChange(field)}
            label={`${label}${required ? ' *' : ''}`}
            placeholder={`Enter ${label.toLowerCase()}`}
          />
          {isDirty && error && (
            <FormHelperText>{error}</FormHelperText>
          )}
        </FormControl>
      </Box>
    );
  };

  const renderTimeInputs = () => {
    return (
      <Box sx={{ mb: 3 }}>
        <Stack direction="row" spacing={2}>
          <FormControl fullWidth variant="outlined">
            <InputLabel htmlFor="open-time" shrink>Open</InputLabel>
            <OutlinedInput
              id="open-time"
              type="time"
              value={profileData.courseHours.open}
              onChange={handleTimeChange('open')}
              notched
              label="Open"
              inputProps={{
                step: 300 // 5 min
              }}
            />
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <InputLabel htmlFor="close-time" shrink>Close</InputLabel>
            <OutlinedInput
              id="close-time"
              type="time"
              value={profileData.courseHours.close}
              onChange={handleTimeChange('close')}
              notched
              label="Close"
              inputProps={{
                step: 300 // 5 min
              }}
            />
          </FormControl>
        </Stack>
      </Box>
    );
  };

  const renderLogoUpload = () => (
    <Box sx={{ 
      mt: 2,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      {profileData.courseLogoPreview && (
        <Avatar
          src={profileData.courseLogoPreview}
          alt="Course Logo Preview"
          variant="rounded"
          sx={{
            width: 120,
            height: 120,
            mb: 1
          }}
        />
      )}
      <Button 
        variant="outlined" 
        component="label"
        sx={{ 
          textTransform: 'none',
          minWidth: '120px'
        }}
      >
        {profileData.courseLogo ? 'Change Logo' : 'Upload Logo'}
        <input 
          type="file" 
          hidden 
          accept="image/*"
          onChange={handleLogoChange}
        />
      </Button>
      {profileData.courseLogo && (
        <Typography 
          variant="caption" 
          color="text.secondary"
          sx={{ textAlign: 'center' }}
        >
          {profileData.courseLogo}
        </Typography>
      )}
    </Box>
  );

  return (
    <Box>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>Course Information</Typography>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            {renderTextField('courseName', 'Course Name')}
            {renderTextField('courseAddress', 'Course Address')}
            {renderTextField('coursePhone', 'Phone Number')}
            {renderTextField('courseEmail', 'Email Address')}
            {renderTextField('courseWebsite', 'Website', false)}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderTimeInputs()}
            {renderTextField('facebookUrl', 'Facebook URL', false)}
            {renderTextField('googleUrl', 'Google URL', false)}
            {renderTextField('openTableLink', 'OpenTable Link', false)}
            {renderLogoUpload()}
          </Grid>
        </Grid>
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button 
            variant="contained" 
            color="primary"
            onClick={handleSaveProfile}
            disabled={!isDirty || !isFormValid() || isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
            sx={{ textTransform: 'none' }}
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </Box>
      </Paper>

      {/* Course Hierarchy Section */}
      <CourseHierarchy courseId={courseId} />
    </Box>
  );
};

export default ProfileTab; 