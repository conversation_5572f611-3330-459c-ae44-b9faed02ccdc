/**
 * XANO Integration Tests
 * 
 * This file contains tests to verify that all CRUD operations work correctly
 * with the XANO backend. Run these tests to ensure the integration is working.
 * 
 * To run these tests:
 * 1. Ensure you have valid XANO credentials
 * 2. Update the config with correct golf_course_id
 * 3. Run: npm test xano-integration.test.ts
 */

import { apiClient } from '../api/apiClient';
import { xanoLogin } from '../api/xanoAuth';
import config from '../config';

describe('XANO Integration Tests', () => {
  let authToken: string;
  let testGolferId: number;
  let testProShopItemId: number;
  let testTeeTimeId: number;

  beforeAll(async () => {
    // Login and get auth token
    try {
      const loginResponse = await xanoLogin('<EMAIL>', 'password');
      authToken = loginResponse.token!;
      apiClient.setAuthToken(authToken);
    } catch (error) {
      console.warn('Login failed - using mock data for tests');
    }
  });

  describe('Authentication', () => {
    test('should login successfully', async () => {
      expect(authToken).toBeDefined();
    });

    test('should verify token', async () => {
      if (!authToken) return;
      
      const response = await apiClient.verifyToken();
      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('id');
    });
  });

  describe('Golfer CRUD Operations', () => {
    test('should create a new golfer', async () => {
      const golferData = {
        first_name: 'Test',
        last_name: 'Golfer',
        email: `test.golfer.${Date.now()}@example.com`,
        phone_number: '555-0123',
        handicap: 15
      };

      const response = await apiClient.createGolfer(golferData);
      
      if (response.success && response.data) {
        testGolferId = response.data.id;
        expect(response.data.first_name).toBe(golferData.first_name);
        expect(response.data.last_name).toBe(golferData.last_name);
        expect(response.data.email).toBe(golferData.email);
      }
    });

    test('should read golfers', async () => {
      const response = await apiClient.getGolfers({
        golf_course_id: parseInt(config.courseId)
      });

      expect(response.success).toBe(true);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should read a specific golfer', async () => {
      if (!testGolferId) return;

      const response = await apiClient.getGolfer(testGolferId);
      
      if (response.success) {
        expect(response.data).toHaveProperty('id', testGolferId);
        expect(response.data).toHaveProperty('first_name');
        expect(response.data).toHaveProperty('last_name');
      }
    });

    test('should update a golfer', async () => {
      if (!testGolferId) return;

      const updateData = {
        handicap: 12,
        phone_number: '555-9999'
      };

      const response = await apiClient.updateGolfer(testGolferId, updateData);
      
      if (response.success && response.data) {
        expect(response.data.handicap).toBe(updateData.handicap);
        expect(response.data.phone_number).toBe(updateData.phone_number);
      }
    });
  });

  describe('Pro Shop CRUD Operations', () => {
    test('should create a pro shop item', async () => {
      const itemData = {
        golf_course_id: parseInt(config.courseId),
        pro_shop_category_name: 'test-category',
        pro_shop_item_name: 'Test Golf Ball',
        pro_shop_item_sku: `TEST-${Date.now()}`,
        description: 'Test golf ball for integration testing',
        price: 29.99,
        inventory_in_stock: 100
      };

      const response = await apiClient.createProShopItem(itemData);
      
      if (response.success && response.data) {
        testProShopItemId = response.data.id;
        expect(response.data.pro_shop_item_name).toBe(itemData.pro_shop_item_name);
        expect(response.data.price).toBe(itemData.price);
      }
    });

    test('should read pro shop items', async () => {
      const response = await apiClient.getProShopItems({
        golf_course_id: parseInt(config.courseId)
      });

      expect(response.success).toBe(true);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should update a pro shop item', async () => {
      if (!testProShopItemId) return;

      const updateData = {
        price: 24.99,
        inventory_in_stock: 75
      };

      const response = await apiClient.updateProShopItem(testProShopItemId, updateData);
      
      if (response.success) {
        expect(response.data.price).toBe(updateData.price);
        expect(response.data.inventory_in_stock).toBe(updateData.inventory_in_stock);
      }
    });
  });

  describe('Tee Time Operations', () => {
    test('should get available tee times', async () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dateString = tomorrow.toISOString().split('T')[0];

      const response = await apiClient.getAvailableTeeTimes({
        golf_course_id: parseInt(config.courseId),
        date: dateString
      });

      expect(response.success).toBe(true);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should book a tee time', async () => {
      if (!testGolferId) return;

      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(10, 0, 0, 0);

      const bookingData = {
        golf_course_id: parseInt(config.courseId),
        golfer_id: testGolferId,
        tee_time: tomorrow.toISOString(),
        number_of_players: 2
      };

      const response = await apiClient.bookTeeTime(bookingData);
      
      if (response.success && response.data) {
        testTeeTimeId = response.data.id;
        expect(response.data.golfer_id).toBe(testGolferId);
        expect(response.data.number_of_players).toBe(2);
      }
    });

    test('should get tee times', async () => {
      const response = await apiClient.getTeeTimes({
        golf_course_id: parseInt(config.courseId)
      });

      expect(response.success).toBe(true);
      expect(Array.isArray(response.data)).toBe(true);
    });
  });

  describe('Dashboard Analytics', () => {
    test('should get dashboard overview', async () => {
      const response = await apiClient.getDashboardOverview({
        golf_course_id: parseInt(config.courseId),
        timeframe: 'monthly'
      });

      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
    });

    test('should get dashboard metrics', async () => {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const response = await apiClient.getDashboardMetrics({
        golf_course_id: parseInt(config.courseId),
        start_date: startDate,
        end_date: endDate
      });

      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid golfer ID', async () => {
      const response = await apiClient.getGolfer(999999);
      expect(response.success).toBe(false);
      expect(response.error).toBeDefined();
    });

    test('should handle invalid authentication', async () => {
      const originalToken = apiClient.getAuthToken();
      apiClient.setAuthToken('invalid-token');

      const response = await apiClient.getGolfers({
        golf_course_id: parseInt(config.courseId)
      });

      expect(response.success).toBe(false);
      
      // Restore original token
      if (originalToken) {
        apiClient.setAuthToken(originalToken);
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    console.log('Test completed. Clean up any test data if needed.');
    console.log(`Test Golfer ID: ${testGolferId}`);
    console.log(`Test Pro Shop Item ID: ${testProShopItemId}`);
    console.log(`Test Tee Time ID: ${testTeeTimeId}`);
  });
});
