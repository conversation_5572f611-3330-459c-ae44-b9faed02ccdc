import { styled } from '@mui/material/styles';
import {
  Dialog,
  TextField,
  Select,
  Box,
  Chip,
  Typography,
  Button,
  Paper,
} from '@mui/material';

export const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: 16,
    backgroundImage: 'none'
  }
}));

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow',
    ]),
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.text.primary,
    },
  },
}));

export const StyledSelect = styled(Select)(({ theme }) => ({
  '& .MuiOutlinedInput-notchedOutline': {
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow',
    ]),
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.text.primary,
  },
}));

export const StyledChip = styled(Chip)(({ theme }) => ({
  borderRadius: 8,
  height: 32,
  '& .MuiChip-label': {
    paddingLeft: 12,
    paddingRight: 12,
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  minWidth: 100,
  borderRadius: 8,
}));

export const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: 8,
  boxShadow: theme.shadows[2],
  padding: theme.spacing(2),
}));

export const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  fontWeight: 600,
}));

export const FormContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: theme.spacing(3),
  marginBottom: theme.spacing(4),
})); 