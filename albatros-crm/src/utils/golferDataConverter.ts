import { Golf<PERSON>, Golfer<PERSON>orm<PERSON><PERSON> } from '../components/Roster/types';

// Define XANO types locally for now
interface XanoGolfer {
  id: number;
  created_at: string;
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

interface XanoGolferCreate {
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

interface XanoGolferUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  address?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

/**
 * Convert XANO golfer data to component format
 */
export function convertXanoToComponentGolfer(xanoGolfer: XanoGolfer): Golfer {
  const firstName = xanoGolfer.first_name || '';
  const lastName = xanoGolfer.last_name || '';
  const fullName = `${firstName} ${lastName}`.trim();
  
  return {
    id: xanoGolfer.id.toString(),
    name: fullName,
    phone: xanoGolfer.phone_number || '',
    email: xanoGolfer.email || '',
    address: xanoGolfer.address || '',
    stars: xanoGolfer.star_rating || 0,
    isMember: xanoGolfer.membership_status || false,
    lastPlayDate: xanoGolfer.last_play_date || '',
    upcomingPlayDate: xanoGolfer.upcoming_play_date || '',
    albatrossStarScore: xanoGolfer.satisfaction_score?.toString() || '',
    nps: '', // Not in XANO schema, could be added later
    events: '', // Not in XANO schema, could be added later
    foodDrink: '', // Not in XANO schema, could be added later
    student: xanoGolfer.is_student ? 'Yes' : 'No',
    initials: `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase(),
    avatar: undefined, // Not in XANO schema, could be added later
    avatarColor: undefined, // Not in XANO schema, could be added later
  };
}

/**
 * Convert component form data to XANO create format
 */
export function convertComponentToXanoCreate(formData: GolferFormData, golf_course_id: number): XanoGolferCreate {
  return {
    golf_course_id,
    first_name: formData.firstName,
    last_name: formData.lastName,
    email: formData.email,
    phone_number: formData.phone,
    address: formData.address,
    handicap: 0, // Default value, could be added to form later
    star_rating: formData.stars,
    membership_status: formData.isMember,
    is_student: formData.student === 'Yes',
    last_play_date: formData.lastSeen,
    upcoming_play_date: formData.upcomingPlay,
    satisfaction_score: parseInt(formData.albatrossStarScore) || 0,
  };
}

/**
 * Convert component form data to XANO update format
 */
export function convertComponentToXanoUpdate(formData: GolferFormData): XanoGolferUpdate {
  return {
    first_name: formData.firstName,
    last_name: formData.lastName,
    email: formData.email,
    phone_number: formData.phone,
    address: formData.address,
    star_rating: formData.stars,
    membership_status: formData.isMember,
    is_student: formData.student === 'Yes',
    last_play_date: formData.lastSeen,
    upcoming_play_date: formData.upcomingPlay,
    satisfaction_score: parseInt(formData.albatrossStarScore) || 0,
  };
}

/**
 * Calculate metrics from XANO golfer data
 */
export function calculateGolferMetrics(golfers: XanoGolfer[]) {
  const total = golfers.length;
  const members = golfers.filter(g => g.membership_status).length;
  const nonMembers = total - members;
  
  // Calculate average star rating
  const totalStars = golfers.reduce((sum, g) => sum + (g.star_rating || 0), 0);
  const averageStars = total > 0 ? Math.round((totalStars / total) * 10) / 10 : 0;
  
  // Calculate recently active (played in last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentlyActive = golfers.filter(g => {
    if (!g.last_play_date) return false;
    const lastPlayDate = new Date(g.last_play_date);
    return lastPlayDate >= thirtyDaysAgo;
  }).length;
  
  // Calculate upcoming activity (scheduled in next 30 days)
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  const upcomingActivity = golfers.filter(g => {
    if (!g.upcoming_play_date) return false;
    const upcomingDate = new Date(g.upcoming_play_date);
    const now = new Date();
    return upcomingDate >= now && upcomingDate <= thirtyDaysFromNow;
  }).length;
  
  return {
    total,
    members,
    nonMembers,
    averageStars,
    recentlyActive,
    upcomingActivity,
  };
}
