// Utility functions to convert between XANO data format and component format

import { XanoGolfer, CreateGolferRequest, UpdateGolferRequest } from '../types/xano';
import { Golf<PERSON>, GolferFormData } from '../components/Roster/types';

/**
 * Convert XANO golfer data to component format
 */
export function convertXanoToComponentGolfer(xanoGolfer: XanoGolfer): Golfer {
  return {
    id: xanoGolfer.id.toString(),
    name: `${xanoGolfer.first_name} ${xanoGolfer.last_name}`.trim(),
    phone: xanoGolfer.phone_number || '',
    email: xanoGolfer.email || '',
    stars: xanoGolfer.star_rating || 0,
    isMember: xanoGolfer.membership_status || false,
    lastPlayDate: xanoGolfer.last_play_date || '',
    upcomingPlayDate: xanoGolfer.upcoming_play_date || '',
    avatar: xanoGolfer.avatar,
    avatarColor: xanoGolfer.avatar_color,
    initials: generateInitials(xanoGolfer.first_name, xanoGolfer.last_name),
    address: formatAddress(xanoGolfer),
    albatrossStarScore: xanoGolfer.satisfaction_score?.toString() || '',
    nps: xanoGolfer.satisfaction_score?.toString() || '',
    events: '', // Not in XANO schema, could be derived from past_tee_times
    foodDrink: '', // Not in XANO schema, could be derived from past_food_orders
    student: xanoGolfer.is_student ? 'Yes' : 'No',
  };
}

/**
 * Convert component golfer form data to XANO create request
 */
export function convertComponentToXanoCreate(
  formData: GolferFormData, 
  golf_course_id: number
): CreateGolferRequest {
  return {
    golf_course_id,
    first_name: formData.firstName,
    last_name: formData.lastName,
    email: formData.email,
    phone_number: formData.phone,
    address: formData.address,
    star_rating: formData.stars,
    membership_status: formData.isMember,
    is_student: formData.student === 'Yes',
    last_play_date: formData.lastSeen,
    upcoming_play_date: formData.upcomingPlay,
    sms_notifications_enabled: true,
    email_notifications_enabled: true,
    in_app_notifications_enabled: true,
  };
}

/**
 * Convert component golfer form data to XANO update request
 */
export function convertComponentToXanoUpdate(
  formData: GolferFormData
): UpdateGolferRequest {
  return {
    first_name: formData.firstName,
    last_name: formData.lastName,
    email: formData.email,
    phone_number: formData.phone,
    address: formData.address,
    star_rating: formData.stars,
    membership_status: formData.isMember,
    is_student: formData.student === 'Yes',
    last_play_date: formData.lastSeen,
    upcoming_play_date: formData.upcomingPlay,
  };
}

/**
 * Convert XANO golfer to component form data
 */
export function convertXanoToFormData(xanoGolfer: XanoGolfer): GolferFormData {
  return {
    id: xanoGolfer.id.toString(),
    firstName: xanoGolfer.first_name,
    lastName: xanoGolfer.last_name,
    phone: xanoGolfer.phone_number || '',
    email: xanoGolfer.email,
    address: formatAddress(xanoGolfer),
    stars: xanoGolfer.star_rating || 0,
    isMember: xanoGolfer.membership_status || false,
    upcomingPlay: xanoGolfer.upcoming_play_date || '',
    lastSeen: xanoGolfer.last_play_date || '',
    albatrossStarScore: xanoGolfer.satisfaction_score?.toString() || '',
    nps: xanoGolfer.satisfaction_score?.toString() || '',
    events: '', // Could be derived from past_tee_times
    foodDrink: '', // Could be derived from past_food_orders
    student: xanoGolfer.is_student ? 'Yes' : 'No',
    avatar: xanoGolfer.avatar,
    avatarColor: xanoGolfer.avatar_color,
  };
}

/**
 * Generate initials from first and last name
 */
function generateInitials(firstName: string, lastName: string): string {
  const first = firstName?.charAt(0)?.toUpperCase() || '';
  const last = lastName?.charAt(0)?.toUpperCase() || '';
  return `${first}${last}`;
}

/**
 * Format address from XANO address fields
 */
function formatAddress(xanoGolfer: XanoGolfer): string {
  const parts = [
    xanoGolfer.address,
    xanoGolfer.city,
    xanoGolfer.state,
    xanoGolfer.zip_code
  ].filter(Boolean);
  
  return parts.join(', ');
}

/**
 * Parse address string into XANO address components
 */
export function parseAddress(addressString: string): {
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
} {
  if (!addressString) return {};
  
  // Simple parsing - in a real app, you might use a more sophisticated address parser
  const parts = addressString.split(',').map(part => part.trim());
  
  if (parts.length === 1) {
    return { address: parts[0] };
  } else if (parts.length === 2) {
    return { address: parts[0], city: parts[1] };
  } else if (parts.length === 3) {
    return { address: parts[0], city: parts[1], state: parts[2] };
  } else if (parts.length >= 4) {
    return { 
      address: parts[0], 
      city: parts[1], 
      state: parts[2], 
      zip_code: parts[3] 
    };
  }
  
  return {};
}

/**
 * Format date for display
 */
export function formatDateForDisplay(dateString: string): string {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (error) {
    return dateString;
  }
}

/**
 * Format date for XANO API (YYYY-MM-DD)
 */
export function formatDateForAPI(dateString: string): string {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  } catch (error) {
    return '';
  }
}

/**
 * Calculate golfer metrics from XANO data
 */
export function calculateGolferMetrics(golfers: XanoGolfer[]) {
  const total = golfers.length;
  const members = golfers.filter(g => g.membership_status).length;
  const nonMembers = total - members;
  
  // Calculate recent activity (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentlyActive = golfers.filter(g => {
    if (!g.last_play_date) return false;
    const lastPlay = new Date(g.last_play_date);
    return lastPlay > thirtyDaysAgo;
  }).length;
  
  // Calculate upcoming activity (next 30 days)
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  
  const upcomingActivity = golfers.filter(g => {
    if (!g.upcoming_play_date) return false;
    const upcomingPlay = new Date(g.upcoming_play_date);
    return upcomingPlay <= thirtyDaysFromNow;
  }).length;
  
  // Calculate average star rating
  const ratedGolfers = golfers.filter(g => g.star_rating && g.star_rating > 0);
  const averageStars = ratedGolfers.length > 0 
    ? ratedGolfers.reduce((sum, g) => sum + (g.star_rating || 0), 0) / ratedGolfers.length
    : 0;
  
  return {
    total,
    members,
    nonMembers,
    recentlyActive,
    upcomingActivity,
    averageStars: Math.round(averageStars * 10) / 10, // Round to 1 decimal
  };
}
