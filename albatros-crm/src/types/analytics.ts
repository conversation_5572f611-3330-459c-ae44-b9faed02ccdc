export interface AnalyticsOverview {
  revenue: number;
  bookings: number;
  utilization: number;
  trends: {
    daily: number[];
    weekly: number[];
    monthly: number[];
  };
}

export interface TeeTime {
  id: string;
  time: string;
  playerName: string;
  numberOfPlayers: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
}

export interface AnalyticsData {
  metrics: {
    members: {
      total: number;
      change: number;
    };
    golferReturnRate: {
      percentage: number;
      change: number;
    };
    obsoleteGolfers: {
      total: number;
      change: number;
    };
  };
}

export interface TeeTimeData extends TeeTime {
  date: string;
} 