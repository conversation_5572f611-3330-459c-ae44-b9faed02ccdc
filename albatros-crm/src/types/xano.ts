// XANO Database Schema Types
// Generated from Albatross Data Model to match XANO backend exactly

// ===== CORE ENTITY TYPES =====

export interface User {
  id: number;
  created_at: string;
  golf_course_id: number;
  name: string;
  email: string;
  corp_permission?: boolean;
  region_permission?: boolean;
  state_permission?: boolean;
  metro_permission?: boolean;
  course_permission?: boolean;
  calendar_access?: boolean;
  roster_access?: boolean;
  pro_shop_access?: boolean;
  nineteenth_hole_access?: boolean;
  tournaments_access?: boolean;
  analytics_access?: boolean;
  back_office_access?: boolean;
  settings_access?: boolean;
  user_role: UserRole;
  avatar?: string;
  last_login?: string;
  status: UserStatus;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  EMPLOYEE = 'employee',
  GOLFER = 'golfer'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface Golfer {
  id: number;
  created_at: string;
  global_id?: string;
  crm_golfer_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  upcoming_tee_time?: string;
  past_tee_times?: string[];
  past_food_orders?: string[];
  backfill_presented?: boolean;
  backfill_accepted?: boolean;
  waitlist_enabled?: boolean;
  waitlist_dates_times?: string[];
  preferred_play_day_times?: string[];
  preferred_course?: string;
  round_history?: RoundHistory[];
  saved_payment_methods?: PaymentMethod[];
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
  payment_info_verified?: boolean;
  campaign_progress?: CampaignProgress[];
  golfer_shipping_address_id?: string;
  // CRM specific fields
  stars?: number;
  last_play_date?: string;
  is_member?: boolean;
  avatar_color?: string;
  albatross_star_score?: string;
  nps?: string;
  events?: string;
  food_drink?: string;
  student?: string;
}

export interface GolfCourse {
  id: number;
  created_at: string;
  course_id: string;
  name: string;
  address: string;
  phone_number: string;
  email_address: string;
  course_logo?: string;
  has_gps?: boolean;
  measurement: MeasurementUnit;
  golf_course_hole_id?: string;
  course_settings?: CourseSettings;
  seasonal_rate?: SeasonalRate[];
  course_holidays?: string[];
  course_hours?: CourseHours;
  pace_of_play_data_id?: string;
  golf_course_data_api?: string;
  google_average_rating?: number;
  google_total_reviews?: number;
  crm_average_course_quality?: number;
  crm_average_food_quality?: number;
  crm_average_overall_satisfaction?: number;
  crm_total_reviews?: number;
  time_tracking_settings?: TimeTrackingSettings;
}

export enum MeasurementUnit {
  YARDS = 'yards',
  METERS = 'meters'
}

export interface TeeTime {
  id: number;
  created_at: string;
  tee_time: string;
  number_of_players: number;
  golfer_id: number;
  golf_course_id: number;
  tee_time_cost_per_golfer: number;
  split_tee_time_cost_with_other_golfer?: boolean;
  tee_time_booking_id?: string;
  special_rate?: number;
  notes?: string;
}

export interface TeeTimeBooking {
  id: number;
  created_at: string;
  calendar_event_id: string;
  golfer_id: number;
  booking_status: BookingStatus;
  global_data_id?: string;
}

export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  NO_SHOW = 'no_show'
}

export interface Transaction {
  id: number;
  created_at: string;
  transaction_amount: number;
  payment_method: PaymentMethodType;
  golfer_id: number;
  payment_info_verified?: boolean;
  transaction_datetime: string;
  transaction_status: TransactionStatus;
  authorization_code?: string;
  e_commerce_transaction_id?: string;
  pro_shop_item_transaction_id?: string;
  food_and_beverage_item_transaction_id?: string;
  rewards_program_id?: string;
  square_transaction_id?: string;
  square_location_id?: string;
  transaction_uuid: string;
  sensitive_payment_data_id?: string;
  revenue_type?: RevenueType;
  revenue_source?: RevenueSource;
}

export enum PaymentMethodType {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  CASH = 'cash',
  DIGITAL_WALLET = 'digital_wallet',
  BANK_TRANSFER = 'bank_transfer'
}

export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export interface ProShopItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  pro_shop_category_name: string;
  pro_shop_item_name: string;
  pro_shop_item_sku: string;
  description?: string;
  price: number;
  cost?: number;
  membership_discount_offered?: boolean;
  membership_discount_amount?: number;
  pro_shop_item_photo?: string;
  pro_shop_mobile_item_offered?: boolean;
  inventory_in_stock: number;
  total_inventory_necessary?: number;
  membership_discount_id?: string;
  is_rentable?: boolean;
  rental_price?: number;
}

export interface FoodAndBeverageItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  food_and_beverage_category: string;
  food_item_name: string;
  description?: string;
  price: number;
  food_customization?: string[];
  cost?: number;
  membership_offered_discount?: boolean;
  membership_discount_amount?: number;
  mobile_app_offered_food_item?: boolean;
  membership_discount_id?: string;
}

// ===== SUPPORTING TYPES =====

export interface RoundHistory {
  date: string;
  score: number;
  course: string;
}

export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  last_four: string;
  expiry?: string;
}

export interface CampaignProgress {
  campaign_id: string;
  progress: number;
  completed: boolean;
}

export interface CourseSettings {
  tee_time_interval: number;
  max_players_per_slot: number;
  cancellation_policy?: string;
  weather_policy?: string;
  is_private?: boolean;
  requires_membership?: boolean;
}

export interface SeasonalRate {
  season: string;
  start_date: string;
  end_date: string;
  rate_multiplier: number;
}

export interface CourseHours {
  monday?: DayHours;
  tuesday?: DayHours;
  wednesday?: DayHours;
  thursday?: DayHours;
  friday?: DayHours;
  saturday?: DayHours;
  sunday?: DayHours;
}

export interface DayHours {
  open: string;
  close: string;
  closed?: boolean;
}

export interface TimeTrackingSettings {
  enabled: boolean;
  break_duration: number;
  overtime_threshold: number;
}

export enum RevenueType {
  TEE_TIME = 'tee_time',
  PRO_SHOP = 'pro_shop',
  FOOD_BEVERAGE = 'food_beverage',
  COACHING = 'coaching',
  EVENT = 'event'
}

export enum RevenueSource {
  WALK_IN = 'walk_in',
  ONLINE = 'online',
  MOBILE_APP = 'mobile_app',
  PHONE = 'phone'
}

// ===== API RESPONSE TYPES =====

export interface XanoApiResponse<T> {
  success?: boolean;
  data?: T;
  error?: XanoApiError;
  pagination?: XanoPaginationInfo;
  meta?: XanoResponseMeta;
}

export interface XanoApiError {
  code: string;
  message: string;
  details?: any;
  field_errors?: XanoFieldError[];
}

export interface XanoFieldError {
  field: string;
  message: string;
  code: string;
}

export interface XanoPaginationInfo {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface XanoResponseMeta {
  timestamp: string;
  request_id: string;
  version: string;
}

// ===== REQUEST TYPES =====

export interface CreateGolferRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
}

export interface UpdateGolferRequest extends Partial<CreateGolferRequest> {
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
}

export interface CreateUserRequest {
  golf_course_id: number;
  name: string;
  email: string;
  password: string;
  user_role: UserRole;
  permissions?: Partial<UserPermissions>;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  user_role?: UserRole;
  status?: UserStatus;
  permissions?: Partial<UserPermissions>;
}

export interface UserPermissions {
  corp_permission?: boolean;
  region_permission?: boolean;
  state_permission?: boolean;
  metro_permission?: boolean;
  course_permission?: boolean;
  calendar_access?: boolean;
  roster_access?: boolean;
  pro_shop_access?: boolean;
  nineteenth_hole_access?: boolean;
  tournaments_access?: boolean;
  analytics_access?: boolean;
  back_office_access?: boolean;
  settings_access?: boolean;
}

export interface BookTeeTimeRequest {
  golf_course_id: number;
  golfer_id: number;
  tee_time: string;
  number_of_players: number;
  special_requests?: string;
  payment_method?: PaymentMethodType;
}

export interface CreateProShopItemRequest {
  golf_course_id: number;
  pro_shop_category_name: string;
  pro_shop_item_name: string;
  pro_shop_item_sku: string;
  description?: string;
  price: number;
  cost?: number;
  inventory_in_stock: number;
  total_inventory_necessary?: number;
  is_rentable?: boolean;
  rental_price?: number;
}

export interface CreateFoodItemRequest {
  golf_course_id: number;
  food_and_beverage_category: string;
  food_item_name: string;
  description?: string;
  price: number;
  food_customization?: string[];
  cost?: number;
  membership_offered_discount?: boolean;
  membership_discount_amount?: number;
  mobile_app_offered_food_item?: boolean;
}

// ===== QUERY PARAMETER TYPES =====

export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

export interface DateRangeParams {
  start_date?: string;
  end_date?: string;
}

export interface GolfCourseParams {
  golf_course_id?: number;
}

// ===== AUTHENTICATION TYPES =====

export interface AuthToken {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
  user_id: string;
  golf_course_id?: string;
  permissions: string[];
}

export interface LoginRequest {
  email: string;
  password: string;
  golf_course_id?: number;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  authToken?: string;
  user: User;
}
