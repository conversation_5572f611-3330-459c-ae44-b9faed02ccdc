/**
 * XANO Database Types
 * 
 * These types match the XANO database schema for the golf course CRM
 */

// Base golfer record from XANO database
export interface XanoGolfer {
  id: number;
  created_at: string;
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

// Data structure for creating a new golfer in XANO
export interface XanoGolferCreate {
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

// Data structure for updating an existing golfer in XANO
export interface XanoGolferUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  handicap?: number;
  star_rating?: number;
  membership_status?: boolean;
  is_student?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  satisfaction_score?: number;
}

// XANO API Response wrapper
export interface XanoResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}

// Golf Course information from XANO
export interface XanoGolfCourse {
  id: number;
  created_at: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  phone?: string;
  email?: string;
  website?: string;
}

// User/Authentication types for XANO
export interface XanoUser {
  id: number;
  created_at: string;
  email: string;
  name?: string;
  golf_course_id?: number;
}

export interface XanoAuthResponse {
  authToken: string;
  user: XanoUser;
}

// Tee time related types (for future use)
export interface XanoTeeTime {
  id: number;
  created_at: string;
  golf_course_id: number;
  golfer_id?: number;
  date: string;
  time: string;
  players: number;
  status: 'booked' | 'available' | 'cancelled';
}

// Pro shop item types (for future use)
export interface XanoProShopItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  name: string;
  description?: string;
  price: number;
  category: string;
  stock_quantity?: number;
  image_url?: string;
}

// Restaurant/19th hole item types (for future use)
export interface XanoMenuItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  name: string;
  description?: string;
  price: number;
  category: string;
  available: boolean;
  image_url?: string;
}
