// XANO API Types
// Based on the XANO database schema and API responses

export interface XanoApiResponse<T> {
  success: boolean;
  data?: T;
  error?: XanoApiError;
  pagination?: PaginationInfo;
  meta?: ResponseMeta;
}

export interface XanoApiError {
  code: string;
  message: string;
  details?: any;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ResponseMeta {
  timestamp: string;
  request_id: string;
  version: string;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  authToken?: string;
  user?: User;
  error?: string;
}

export interface User {
  id: number;
  created_at: string;
  golf_course_id: number;
  name?: string;
  email: string;
  corp_permission?: boolean;
  region_permission?: boolean;
  state_permission?: boolean;
  metro_permission?: boolean;
  course_permission?: boolean;
  calendar_access?: boolean;
  roster_access?: boolean;
  pro_shop_access?: boolean;
  nineteenth_hole_access?: boolean;
  tournaments_access?: boolean;
  analytics_access?: boolean;
  back_office_access?: boolean;
  settings_access?: boolean;
  user_role?: UserRole;
  avatar?: string;
  last_login?: string;
  status?: UserStatus;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  EMPLOYEE = 'employee',
  GOLFER = 'golfer'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// Golfer Types (XANO Schema)
export interface XanoGolfer {
  id: number;
  created_at: string;
  golf_course_id: number;
  global_id?: string;
  crm_golfer_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  upcoming_tee_time?: string;
  past_tee_times?: string[];
  past_food_orders?: string[];
  backfill_presented?: boolean;
  backfill_accepted?: boolean;
  waitlist_enabled?: boolean;
  waitlist_dates_times?: string[];
  preferred_play_day_times?: string[];
  preferred_course?: string;
  round_history?: RoundHistory[];
  saved_payment_methods?: PaymentMethod[];
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  membership_status?: boolean;
  membership_type?: string;
  membership_id?: string;
  satisfaction_score?: number;
  star_rating?: number;
  has_card_on_file?: boolean;
  is_student?: boolean;
  professor_id?: number;
  total_rounds?: number;
  average_score?: number;
  last_play_date?: string;
  upcoming_play_date?: string;
  avatar?: string;
  avatar_color?: string;
}

export interface RoundHistory {
  date: string;
  score: number;
  course: string;
  tees: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank';
  last4: string;
  brand?: string;
  isDefault: boolean;
}

// Request/Response Types for Golfer Operations
export interface CreateGolferRequest {
  golf_course_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  membership_status?: boolean;
  membership_type?: string;
  star_rating?: number;
  is_student?: boolean;
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
}

export interface UpdateGolferRequest extends Partial<CreateGolferRequest> {
  id?: number;
}

// Pro Shop Types
export interface ProShopItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  pro_shop_category_name: string;
  pro_shop_item_name: string;
  pro_shop_item_sku: string;
  description?: string;
  price: number;
  cost?: number;
  inventory_in_stock: number;
  total_inventory_necessary?: number;
  is_rentable?: boolean;
  rental_price?: number;
  pro_shop_mobile_item_offered?: boolean;
}

export interface CreateProShopItemRequest {
  golf_course_id: number;
  pro_shop_category_name: string;
  pro_shop_item_name: string;
  pro_shop_item_sku: string;
  description?: string;
  price: number;
  cost?: number;
  inventory_in_stock: number;
  total_inventory_necessary?: number;
  is_rentable?: boolean;
  rental_price?: number;
  pro_shop_mobile_item_offered?: boolean;
}

// Tee Time Types
export interface TeeTime {
  id: number;
  created_at: string;
  golf_course_id: number;
  golfer_id: number;
  tee_time: string;
  number_of_players: number;
  status?: string;
  notes?: string;
}

export interface CreateTeeTimeRequest {
  golf_course_id: number;
  golfer_id: number;
  tee_time: string;
  number_of_players: number;
  notes?: string;
}

// Food & Beverage Types
export interface FoodBeverageItem {
  id: number;
  created_at: string;
  golf_course_id: number;
  food_and_beverage_category_name: string;
  food_and_beverage_item_name: string;
  description?: string;
  price: number;
  cost?: number;
  inventory_in_stock: number;
  is_available: boolean;
  preparation_time_minutes?: number;
}

// Query Parameter Types
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
}

export interface GolfCourseParams {
  golf_course_id: number;
}

export interface DateRangeParams {
  start_date?: string;
  end_date?: string;
}

// Dashboard Types
export interface DashboardOverviewParams extends GolfCourseParams {
  timeframe?: 'daily' | 'weekly' | 'monthly';
}

export interface DashboardMetricsParams extends GolfCourseParams, DateRangeParams {}

export interface WeatherInfoParams extends GolfCourseParams {}

// Analytics Types
export interface RevenueAnalyticsParams extends GolfCourseParams, DateRangeParams {
  timeView?: string;
}

// Available Tee Times Query
export interface AvailableTimesParams extends GolfCourseParams {
  date: string;
  players?: number;
}

// Booking Tee Time
export interface BookTeeTimeParams extends GolfCourseParams {
  golfer_id: number;
  tee_time: string;
  number_of_players: number;
  notes?: string;
}
