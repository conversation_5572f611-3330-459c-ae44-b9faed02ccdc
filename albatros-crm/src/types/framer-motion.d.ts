declare module 'framer-motion' {
  import * as React from 'react';

  export interface AnimatePresenceProps {
    children: React.ReactNode;
    initial?: boolean;
    onExitComplete?: () => void;
    exitBeforeEnter?: boolean;
    presenceAffectsLayout?: boolean;
  }

  export interface MotionProps {
    initial?: any;
    animate?: any;
    exit?: any;
    transition?: any;
    whileHover?: any;
    whileTap?: any;
    whileDrag?: any;
    whileFocus?: any;
    whileInView?: any;
  }

  type MotionComponent<P = any> = React.ForwardRefExoticComponent<P & MotionProps>;

  interface CustomMotionComponentType {
    <P extends object>(component: React.ComponentType<P>): MotionComponent<P>;
  }

  export const motion: {
    [K in keyof JSX.IntrinsicElements]: MotionComponent<JSX.IntrinsicElements[K]>;
  } & CustomMotionComponentType;

  export const AnimatePresence: React.FC<AnimatePresenceProps>;
} 