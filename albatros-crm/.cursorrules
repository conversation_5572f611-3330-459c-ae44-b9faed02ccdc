# Albatros CRM Cursor Rules

# Environment Variables
REACT_APP_COURSE_ID=6793f989213768ac24c381e4
REACT_APP_API_BASE_URL=http://localhost:8080

# API Endpoint Rules
- Course ID should be passed as a query parameter: ?courseId=6793f989213768ac24c381e4
- Date parameters should use RFC3339 format: YYYY-MM-DDThh:mm:ssZ
- Time view parameter should be one of: daily, weekly, monthly
- API endpoints should follow this structure:
  /analytics/overview?courseId={courseId}&date={date}&timeView={timeView}
  /pro-shop/inventory?courseId={courseId}&category={category}&search={search}
  /tee-times?courseId={courseId}&date={date}&view={view}

# Component Rules
- All components should receive courseId as a prop
- Never hardcode courseId in components
- Use optional chaining (?.) for all nested object access
- Provide fallback values using nullish coalescing operator (?? 0 or ?? 'N/A')
- Add loading and error states for all API calls

# Date Handling
- Always use RFC3339 format for API requests
- Format: new Date().toISOString().split('.')[0] + 'Z'
- Display dates in local format using toLocaleDateString() or toLocaleTimeString()

# API Response Handling
- Check for response.data before accessing properties
- Use optional chaining for nested properties
- Provide default values for all metrics
- Handle loading and error states consistently

# Error Handling
- Wrap all API calls in try/catch blocks
- Set appropriate loading states before and after API calls
- Display user-friendly error messages
- Use error boundaries for component-level errors

# State Management
- Initialize state with null for API data
- Set loading=true before API calls
- Set loading=false in finally block
- Clear errors when retrying operations

# Code Style
- Use consistent parameter ordering in API calls (courseId first)
- Use descriptive variable names
- Add comments for complex logic
- Follow Material-UI styling conventions 