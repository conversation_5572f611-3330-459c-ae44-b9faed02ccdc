{"name": "albatros-crm", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^5.17.1", "@mui/x-data-grid": "^7.24.0", "@mui/x-date-pickers": "^6.20.2", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@nivo/pie": "^0.88.0", "@square/web-sdk": "^2.1.0", "@tanstack/react-query": "^5.79.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/html2canvas": "^0.5.35", "@types/jest": "^29.5.12", "@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@types/react-router-dom": "^5.3.3", "ajv": "^8.12.0", "axios": "^1.7.9", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "framer-motion": "^12.5.0", "html2canvas": "^1.4.1", "mui-tel-input": "^8.0.1", "next": "^15.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-router-dom": "^7.1.3", "react-scripts": "^5.0.1", "recharts": "^2.15.0", "square": "^42.3.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/chart.js": "^2.9.41", "@types/file-saver": "^2.0.7", "@types/react-date-range": "^1.4.10", "autoprefixer": "^10.4.14", "chart.js": "^4.4.9", "cross-env": "^7.0.3", "eslint": "^8.57.1", "file-saver": "^2.0.5", "postcss": "^8.4.21", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "tailwindcss": "^3.4.17"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}