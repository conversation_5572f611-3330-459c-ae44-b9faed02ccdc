# Reviews Feature Documentation

## Overview

The Reviews tab in the Back Office module provides a comprehensive feedback capture and management system for golf courses. It integrates with Google Business API to fetch and display reviews, manages review campaigns, and provides analytics on customer satisfaction.

## Features

### 1. Review Management
- **Multi-source reviews**: Google, internal, email, and SMS reviews
- **Review filtering**: By source, rating, and status
- **Review responses**: Ability to respond to customer reviews
- **Review analytics**: Rating distribution and sentiment analysis

### 2. Google Business Integration
- **Automatic sync**: Fetch reviews from Google Business API
- **Manual sync**: Trigger sync on demand
- **Configuration management**: Store API credentials securely
- **Review linking**: Direct links to Google reviews

### 3. Review Campaigns
- **Multiple channels**: Email, SMS, and in-app notifications
- **Trigger options**: Round completion, manual, or scheduled
- **Template system**: Customizable message templates with placeholders
- **Analytics**: Track sent count and response rates

### 4. Visual Analytics
- **Rating distribution**: Visual representation of review ratings
- **Response rates**: Campaign performance metrics
- **Review trends**: Historical review data analysis

## Setup Instructions

### Google Business API Integration

1. **Create Google Cloud Project**
   ```bash
   # Go to Google Cloud Console
   https://console.cloud.google.com/
   ```

2. **Enable Google My Business API**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google My Business API"
   - Click "Enable"

3. **Create API Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Restrict the API key to Google My Business API only

4. **Get Place ID**
   - Go to Google My Business
   - Find your business listing
   - The Place ID is in the URL or use Google's Place ID Finder

5. **Configure in Albatros CRM**
   - Navigate to Back Office > Reviews > Google Integration
   - Enter your Place ID and API Key
   - Enable auto-sync if desired

### Environment Variables

Add these to your `.env` file:
```env
REACT_APP_COURSE_ID=6793f989213768ac24c381e4
REACT_APP_API_BASE_URL=http://localhost:8080
```

## API Endpoints

### Google Business API
```typescript
// Fetch Google reviews
GET /google-business/reviews?courseId={courseId}&placeId={placeId}

// Sync Google reviews
POST /google-business/sync?courseId={courseId}
```

### Review Campaigns
```typescript
// Get campaigns
GET /review-campaigns?courseId={courseId}

// Create campaign
POST /review-campaigns
{
  name: string,
  type: 'email' | 'sms' | 'in-app',
  template: string,
  trigger: 'round_completion' | 'manual' | 'scheduled',
  courseId: string
}

// Update campaign
PUT /review-campaigns/{id}

// Delete campaign
DELETE /review-campaigns/{id}

// Send campaign
POST /review-campaigns/{id}/send?courseId={courseId}
```

### Review Requests
```typescript
// Send review request
POST /reviews/request
{
  courseId: string,
  golferId: string,
  type: 'email' | 'sms',
  googleReviewUrl: string
}
```

## Component Structure

```
src/
├── components/
│   ├── ReviewsTab.tsx              # Main reviews component
│   └── BackOfficeModule.tsx        # Updated to include Reviews tab
├── api/
│   └── hooks/
│       ├── useGoogleReviews.ts     # Google Business API integration
│       └── useReviewCampaigns.ts   # Campaign management
```

## Usage Examples

### Creating a Review Campaign

1. Navigate to Back Office > Reviews > Review Campaigns
2. Click "Create Campaign"
3. Fill in the form:
   - **Name**: "Post-Round Email Follow-up"
   - **Type**: Email
   - **Trigger**: Round Completion
   - **Template**: "Thank you for playing at {courseName}! How was your experience? Rate us: {link}"

### Responding to Reviews

1. Go to Back Office > Reviews > All Reviews
2. Find the review you want to respond to
3. Click "Respond"
4. Write your response and click "Send Response"

### Syncing Google Reviews

1. Navigate to Back Office > Reviews > Google Integration
2. Enter your Google Place ID and API Key
3. Click "Manual Sync" or enable "Auto-sync reviews"

## Template Variables

Use these placeholders in campaign templates:

- `{courseName}` - Golf course name
- `{golferName}` - Golfer's name
- `{date}` - Current date
- `{link}` - Review link (for Google reviews)
- `{teeTime}` - Tee time information

## Security Considerations

1. **API Key Storage**: API keys are stored in localStorage (consider server-side storage for production)
2. **Rate Limiting**: Implement rate limiting for Google API calls
3. **Data Privacy**: Ensure compliance with data protection regulations
4. **Access Control**: Restrict access to review management features

## Troubleshooting

### Common Issues

1. **Google API Errors**
   - Verify API key is correct and has proper permissions
   - Check if Google My Business API is enabled
   - Ensure Place ID is correct

2. **Campaign Not Sending**
   - Verify campaign status is "active"
   - Check template syntax and placeholders
   - Ensure courseId is properly set

3. **Reviews Not Syncing**
   - Check network connectivity
   - Verify API credentials
   - Review browser console for errors

### Error Messages

- `"Google Business configuration is incomplete"` - Add Place ID and API Key
- `"Failed to sync reviews"` - Check API credentials and network
- `"Failed to create campaign"` - Verify form data and API endpoint

## Future Enhancements

1. **Additional Review Sources**: Facebook, Yelp, TripAdvisor
2. **Advanced Analytics**: Sentiment analysis, trend reporting
3. **Automated Responses**: AI-powered response suggestions
4. **Review Moderation**: Automated filtering and moderation tools
5. **Integration with POS**: Automatic review requests after purchases

## Support

For technical support or questions about the Reviews feature, please refer to the API documentation or contact the development team. 