import React, { useState } from 'react';
import SummaryTab from './SummaryTab';
import TimeSheetTab from './TimeSheetTab';
import MarketingTab from './MarketingTab';
import AccountingTab from './AccountingTab';
import PromosTab from './PromosTab';
import styles from './BackOffice.module.css';

const TABS = [
  { label: 'Summary', component: <SummaryTab /> },
  { label: 'Time Sheet', component: <TimeSheetTab /> },
  { label: 'Marketing', component: <MarketingTab /> },
  { label: 'Accounting', component: <AccountingTab /> },
  { label: 'Promos', component: <PromosTab /> },
];

const BackOfficeModule: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className={styles.backOfficeContainer}>
      <div className={styles.tabs}>
        {TABS.map((tab, idx) => (
          <button
            key={tab.label}
            className={activeTab === idx ? styles.activeTab : styles.tab}
            onClick={() => setActiveTab(idx)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className={styles.tabContent}>
        {TABS[activeTab].component}
      </div>
    </div>
  );
};

export default BackOfficeModule; 