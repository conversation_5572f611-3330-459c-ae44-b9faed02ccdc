import React, { useState } from 'react';
import styles from './BackOffice.module.css';
import TemplateEditor from './TemplateEditor';

const marketingTemplates = [
  { name: 'Welcome Email', type: 'Email', lastModified: '2024-01-15', status: 'Active' },
  { name: 'Membership Renewal', type: 'SMS', lastModified: '2024-01-14', status: 'Draft' },
  { name: 'Tournament Invitation', type: 'Email', lastModified: '2024-01-13', status: 'Active' },
  { name: 'Special Promotion - New Year', type: 'SMS', lastModified: '2024-01-12', status: 'Inactive' },
  { name: 'Member Services - Club Fitting', type: 'Email', lastModified: '2024-01-13', status: 'Active' },
  { name: 'Special Promotion - Masters Sunday', type: 'SMS', lastModified: '2024-01-09', status: 'Inactive' },
];

const campaignAnalytics = [
  {
    name: 'Welcome Email',
    type: 'Email',
    openRate: 66,
    clickRate: 33,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-01-01',
    endDate: null,
  },
  {
    name: 'Tournament Invitation - March Classic',
    type: 'Email',
    openRate: 62,
    clickRate: 37,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-02-01',
    endDate: null,
  },
  {
    name: 'Member Services - Club Fitting',
    type: 'SMS',
    openRate: 60,
    clickRate: 31,
    bounceRate: 2,
    unsubscribe: 8,
    startDate: '2024-03-01',
    endDate: null,
  },
];

const MarketingTab: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
  const [showEditor, setShowEditor] = useState(false);

  const handleOpenModal = (campaign: any) => {
    setSelectedCampaign(campaign);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedCampaign(null);
  };

  const handleCreateTemplate = () => {
    setShowEditor(true);
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
  };

  if (showEditor) {
    return <TemplateEditor onClose={handleCloseEditor} />;
  }

  return (
    <div className={styles.marketingTab}>
      {/* Marketing Templates Section */}
      <div className={styles.templatesSection}>
        <div className={styles.templatesHeader}>
          <h2 className={styles.heading}>Marketing Templates</h2>
          <div className={styles.templatesActions}>
            <button className={styles.secondaryButton}>See All</button>
            <button className={styles.primaryButton} onClick={handleCreateTemplate}>Create Template</button>
          </div>
        </div>
        <div className={styles.templatesGrid}>
          {marketingTemplates.map((template, idx) => (
            <div key={idx} className={styles.templateCard}>
              <div className={styles.templateHeader}>
                <span className={styles.templateType}>{template.type}</span>
                <span className={
                  template.status === 'Active' ? styles.templateStatusActive :
                  template.status === 'Draft' ? styles.templateStatusDraft :
                  styles.templateStatusInactive
                }>
                  {template.status}
                </span>
              </div>
              <div className={styles.templateName}>{template.name}</div>
              <div className={styles.templateMeta}>Last modified: {template.lastModified}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Campaign Analytics Section */}
      <div className={styles.analyticsSection}>
        <h2 className={styles.heading}>Campaign Analytics</h2>
        <div className={styles.analyticsTableWrapper}>
          <table className={styles.analyticsTable}>
            <thead>
              <tr>
                <th>Type</th>
                <th>Campaign</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Open Rate</th>
                <th>Click Rate</th>
                <th>Bounce Rate</th>
                <th>Unsubscribe</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {campaignAnalytics.map((c, idx) => (
                <tr key={idx}>
                  <td>
                    <span className={styles.templateType}>{c.type}</span>
                  </td>
                  <td>{c.name}</td>
                  <td>{c.startDate}</td>
                  <td>{c.endDate || 'Not Set'}</td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.openRate}%<br /><span className={styles.analyticsLabel}>Open Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.clickRate}%<br /><span className={styles.analyticsLabel}>Click Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.bounceRate}%<br /><span className={styles.analyticsLabel}>Bounce Rate</span></span>
                  </td>
                  <td>
                    <span className={styles.analyticsCircle}>{c.unsubscribe}%<br /><span className={styles.analyticsLabel}>Unsubscribe</span></span>
                  </td>
                  <td>
                    <button className={styles.moreButton} onClick={() => handleOpenModal(c)}>...</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal for Campaign Details */}
      {modalOpen && (
        <div className={styles.modalOverlay} onClick={handleCloseModal}>
          <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
            <h3>{selectedCampaign?.name} Details</h3>
            <p>Type: {selectedCampaign?.type}</p>
            <p>Start Date: {selectedCampaign?.startDate}</p>
            <p>End Date: {selectedCampaign?.endDate || 'Not Set'}</p>
            <p>Open Rate: {selectedCampaign?.openRate}%</p>
            <p>Click Rate: {selectedCampaign?.clickRate}%</p>
            <p>Bounce Rate: {selectedCampaign?.bounceRate}%</p>
            <p>Unsubscribe: {selectedCampaign?.unsubscribe}%</p>
            <button className={styles.primaryButton} onClick={handleCloseModal}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketingTab; 