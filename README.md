# Albatross Golf Club CRM

A comprehensive golf club management system built with React and TypeScript, featuring modern UI components and a modular architecture. The system includes management interfaces for the Pro Shop, Restaurant (19th Hole), and other club facilities.

## Tech Stack

### Frontend
- React 18 with TypeScript
- Material-UI v6 (MUI) for UI components
- React Router v7 for navigation
- Framer Motion for animations
- Nivo/Recharts for data visualization
- DND Kit for drag-and-drop functionality
- Axios for API communication

### Development Tools
- TypeScript for type safety
- ESLint for code quality
- Jest and React Testing Library for testing

## Project Structure

```
albatros-crm/
├── src/
│   ├── components/           # Reusable components
│   │   ├── NineteenthHole/  # Restaurant management
│   │   │   ├── MenuList     # Menu display and management
│   │   │   ├── MenuActions  # Menu-related actions
│   │   │   ├── OrdersTab    # Order tracking system
│   │   │   └── ReportsTab   # Analytics and reporting
│   │   │
│   │   └── ProShop/        # Pro Shop management
│   │       ├── ProductList  # Inventory display
│   │       ├── OrdersTab    # Order management
│   │       └── ReportsTab   # Sales analytics
│   │
│   ├── pages/              # Main route components
│   ├── services/           # API and business logic
│   ├── hooks/             # Custom React hooks
│   ├── types/             # TypeScript definitions
│   ├── styles/            # Global styles
│   └── theme/             # MUI theme customization
```

## Key Features

### Pro Shop Management
- Inventory tracking and management
- Product categorization
- Order processing
- Sales analytics and reporting
- Mobile app integration
- Stock level monitoring

### Restaurant (19th Hole) Management
- Menu item management
- Order tracking system
- Real-time order status updates
- Sales analytics
- Peak hours tracking
- Customer order history

## Getting Started

1. Clone the repository:
   ```bash
   git clone [repository-url]
   cd albatros-crm
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```
   The application will be available at http://localhost:3000

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run type-check` - Runs TypeScript type checking

## Environment Configuration

Create a `.env` file in the project root:

```env
REACT_APP_API_URL=http://localhost:8080
```

## Development Guidelines

### Component Organization
- Place reusable components in `src/components`
- Keep page components in `src/pages`
- Use TypeScript interfaces for props
- Implement proper error handling
- Follow Material-UI best practices

### State Management
- Use React hooks for local state
- Implement context where needed
- Keep business logic in services
- Use custom hooks for shared logic

### Styling
- Use MUI's styled components
- Follow the theme configuration
- Maintain responsive design
- Use CSS-in-JS with emotion

## Testing

The project uses Jest and React Testing Library for testing:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch
```

## Contributing

1. Create a feature branch
2. Make your changes
3. Write/update tests
4. Update documentation
5. Submit a pull request

## License

MIT License

## Contact

For questions or support, please contact the development team. 
